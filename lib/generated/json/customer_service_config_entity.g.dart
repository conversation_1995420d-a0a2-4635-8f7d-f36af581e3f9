import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/customer_service_config_entity.dart';

CustomerServiceConfigEntity $CustomerServiceConfigEntityFromJson(
    Map<String, dynamic> json) {
  final CustomerServiceConfigEntity customerServiceConfigEntity = CustomerServiceConfigEntity();
  final List<CustomerServiceConfigData>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CustomerServiceConfigData>(
          e) as CustomerServiceConfigData)
      .toList();
  if (list != null) {
    customerServiceConfigEntity.list = list;
  }
  return customerServiceConfigEntity;
}

Map<String, dynamic> $CustomerServiceConfigEntityToJson(
    CustomerServiceConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CustomerServiceConfigEntityExtension on CustomerServiceConfigEntity {
  CustomerServiceConfigEntity copyWith({
    List<CustomerServiceConfigData>? list,
  }) {
    return CustomerServiceConfigEntity()
      ..list = list ?? this.list;
  }
}

CustomerServiceConfigData $CustomerServiceConfigDataFromJson(
    Map<String, dynamic> json) {
  final CustomerServiceConfigData customerServiceConfigData = CustomerServiceConfigData();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    customerServiceConfigData.id = id;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    customerServiceConfigData.logo = logo;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    customerServiceConfigData.name = name;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    customerServiceConfigData.description = description;
  }
  final String? link = jsonConvert.convert<String>(json['link']);
  if (link != null) {
    customerServiceConfigData.link = link;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    customerServiceConfigData.type = type;
  }
  return customerServiceConfigData;
}

Map<String, dynamic> $CustomerServiceConfigDataToJson(
    CustomerServiceConfigData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['logo'] = entity.logo;
  data['name'] = entity.name;
  data['description'] = entity.description;
  data['link'] = entity.link;
  data['type'] = entity.type;
  return data;
}

extension CustomerServiceConfigDataExtension on CustomerServiceConfigData {
  CustomerServiceConfigData copyWith({
    int? id,
    String? logo,
    String? name,
    String? description,
    String? link,
    int? type,
  }) {
    return CustomerServiceConfigData()
      ..id = id ?? this.id
      ..logo = logo ?? this.logo
      ..name = name ?? this.name
      ..description = description ?? this.description
      ..link = link ?? this.link
      ..type = type ?? this.type;
  }
}