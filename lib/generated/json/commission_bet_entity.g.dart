import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/commission_bet_entity.dart';

CommissionBetEntity $CommissionBetEntityFromJson(Map<String, dynamic> json) {
  final CommissionBetEntity commissionBetEntity = CommissionBetEntity();
  final List<CommissionBetList>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<CommissionBetList>(e) as CommissionBetList)
      .toList();
  if (list != null) {
    commissionBetEntity.list = list;
  }
  return commissionBetEntity;
}

Map<String, dynamic> $CommissionBetEntityToJson(CommissionBetEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CommissionBetEntityExtension on CommissionBetEntity {
  CommissionBetEntity copyWith({
    List<CommissionBetList>? list,
  }) {
    return CommissionBetEntity()
      ..list = list ?? this.list;
  }
}

CommissionBetList $CommissionBetListFromJson(Map<String, dynamic> json) {
  final CommissionBetList commissionBetList = CommissionBetList();
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    commissionBetList.type = type;
  }
  final List<
      CommissionBetListThirdList>? thirdList = (json['thirdList'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<CommissionBetListThirdList>(
          e) as CommissionBetListThirdList).toList();
  if (thirdList != null) {
    commissionBetList.thirdList = thirdList;
  }
  return commissionBetList;
}

Map<String, dynamic> $CommissionBetListToJson(CommissionBetList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['type'] = entity.type;
  data['thirdList'] = entity.thirdList?.map((v) => v.toJson()).toList();
  return data;
}

extension CommissionBetListExtension on CommissionBetList {
  CommissionBetList copyWith({
    String? type,
    List<CommissionBetListThirdList>? thirdList,
  }) {
    return CommissionBetList()
      ..type = type ?? this.type
      ..thirdList = thirdList ?? this.thirdList;
  }
}

CommissionBetListThirdList $CommissionBetListThirdListFromJson(
    Map<String, dynamic> json) {
  final CommissionBetListThirdList commissionBetListThirdList = CommissionBetListThirdList();
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    commissionBetListThirdList.platformName = platformName;
  }
  final List<CommissionBetListThirdListList>? list = (json['list'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<CommissionBetListThirdListList>(
          e) as CommissionBetListThirdListList).toList();
  if (list != null) {
    commissionBetListThirdList.list = list;
  }
  return commissionBetListThirdList;
}

Map<String, dynamic> $CommissionBetListThirdListToJson(
    CommissionBetListThirdList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['platformName'] = entity.platformName;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CommissionBetListThirdListExtension on CommissionBetListThirdList {
  CommissionBetListThirdList copyWith({
    String? platformName,
    List<CommissionBetListThirdListList>? list,
  }) {
    return CommissionBetListThirdList()
      ..platformName = platformName ?? this.platformName
      ..list = list ?? this.list;
  }
}

CommissionBetListThirdListList $CommissionBetListThirdListListFromJson(
    Map<String, dynamic> json) {
  final CommissionBetListThirdListList commissionBetListThirdListList = CommissionBetListThirdListList();
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    commissionBetListThirdListList.gameClassCode = gameClassCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    commissionBetListThirdListList.gameClassName = gameClassName;
  }
  final String? categoryCode = jsonConvert.convert<String>(
      json['categoryCode']);
  if (categoryCode != null) {
    commissionBetListThirdListList.categoryCode = categoryCode;
  }
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    commissionBetListThirdListList.platformName = platformName;
  }
  final int? childLevel = jsonConvert.convert<int>(json['childLevel']);
  if (childLevel != null) {
    commissionBetListThirdListList.childLevel = childLevel;
  }
  final int? teamLevel = jsonConvert.convert<int>(json['teamLevel']);
  if (teamLevel != null) {
    commissionBetListThirdListList.teamLevel = teamLevel;
  }
  final double? commissionRate = jsonConvert.convert<double>(
      json['commissionRate']);
  if (commissionRate != null) {
    commissionBetListThirdListList.commissionRate = commissionRate;
  }
  final double? commissionCap = jsonConvert.convert<double>(
      json['commissionCap']);
  if (commissionCap != null) {
    commissionBetListThirdListList.commissionCap = commissionCap;
  }
  return commissionBetListThirdListList;
}

Map<String, dynamic> $CommissionBetListThirdListListToJson(
    CommissionBetListThirdListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['gameClassCode'] = entity.gameClassCode;
  data['gameClassName'] = entity.gameClassName;
  data['categoryCode'] = entity.categoryCode;
  data['platformName'] = entity.platformName;
  data['childLevel'] = entity.childLevel;
  data['teamLevel'] = entity.teamLevel;
  data['commissionRate'] = entity.commissionRate;
  data['commissionCap'] = entity.commissionCap;
  return data;
}

extension CommissionBetListThirdListListExtension on CommissionBetListThirdListList {
  CommissionBetListThirdListList copyWith({
    String? gameClassCode,
    String? gameClassName,
    String? categoryCode,
    String? platformName,
    int? childLevel,
    int? teamLevel,
    double? commissionRate,
    double? commissionCap,
  }) {
    return CommissionBetListThirdListList()
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..categoryCode = categoryCode ?? this.categoryCode
      ..platformName = platformName ?? this.platformName
      ..childLevel = childLevel ?? this.childLevel
      ..teamLevel = teamLevel ?? this.teamLevel
      ..commissionRate = commissionRate ?? this.commissionRate
      ..commissionCap = commissionCap ?? this.commissionCap;
  }
}