import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/third_party_wallet_info_entity.dart';

ThirdPartyWalletInfoEntity $ThirdPartyWalletInfoEntityFromJson(
    Map<String, dynamic> json) {
  final ThirdPartyWalletInfoEntity thirdPartyWalletInfoEntity = ThirdPartyWalletInfoEntity();
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    thirdPartyWalletInfoEntity.userNo = userNo;
  }
  final int? channelId = jsonConvert.convert<int>(json['channelId']);
  if (channelId != null) {
    thirdPartyWalletInfoEntity.channelId = channelId;
  }
  final String? walletAccount = jsonConvert.convert<String>(
      json['walletAccount']);
  if (walletAccount != null) {
    thirdPartyWalletInfoEntity.walletAccount = walletAccount;
  }
  final double? balance = jsonConvert.convert<double>(json['balance']);
  if (balance != null) {
    thirdPartyWalletInfoEntity.balance = balance;
  }
  return thirdPartyWalletInfoEntity;
}

Map<String, dynamic> $ThirdPartyWalletInfoEntityToJson(
    ThirdPartyWalletInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userNo'] = entity.userNo;
  data['channelId'] = entity.channelId;
  data['walletAccount'] = entity.walletAccount;
  data['balance'] = entity.balance;
  return data;
}

extension ThirdPartyWalletInfoEntityExtension on ThirdPartyWalletInfoEntity {
  ThirdPartyWalletInfoEntity copyWith({
    String? userNo,
    int? channelId,
    String? walletAccount,
    double? balance,
  }) {
    return ThirdPartyWalletInfoEntity()
      ..userNo = userNo ?? this.userNo
      ..channelId = channelId ?? this.channelId
      ..walletAccount = walletAccount ?? this.walletAccount
      ..balance = balance ?? this.balance;
  }
}