import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/order_main_entity.dart';

OrderMainEntityList $OrderMainEntityListFromJson(Map<String, dynamic> json) {
  final OrderMainEntityList orderMainEntityList = OrderMainEntityList();
  final List<OrderMainEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<OrderMainEntity>(e) as OrderMainEntity)
      .toList();
  if (list != null) {
    orderMainEntityList.list = list;
  }
  return orderMainEntityList;
}

Map<String, dynamic> $OrderMainEntityListToJson(OrderMainEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension OrderMainEntityListExtension on OrderMainEntityList {
  OrderMainEntityList copyWith({
    List<OrderMainEntity>? list,
  }) {
    return OrderMainEntityList()
      ..list = list ?? this.list;
  }
}

OrderMainEntity $OrderMainEntityFromJson(Map<String, dynamic> json) {
  final OrderMainEntity orderMainEntity = OrderMainEntity();
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    orderMainEntity.gameClassCode = gameClassCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    orderMainEntity.gameClassName = gameClassName;
  }
  final double? rebate = jsonConvert.convert<double>(json['rebate']);
  if (rebate != null) {
    orderMainEntity.rebate = rebate;
  }
  final double? totalBetAmount = jsonConvert.convert<double>(
      json['totalBetAmount']);
  if (totalBetAmount != null) {
    orderMainEntity.totalBetAmount = totalBetAmount;
  }
  final double? totalWin = jsonConvert.convert<double>(json['totalWin']);
  if (totalWin != null) {
    orderMainEntity.totalWin = totalWin;
  }
  final double? totalSendAmount = jsonConvert.convert<double>(
      json['totalSendAmount']);
  if (totalSendAmount != null) {
    orderMainEntity.totalSendAmount = totalSendAmount;
  }
  return orderMainEntity;
}

Map<String, dynamic> $OrderMainEntityToJson(OrderMainEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['gameClassCode'] = entity.gameClassCode;
  data['gameClassName'] = entity.gameClassName;
  data['rebate'] = entity.rebate;
  data['totalBetAmount'] = entity.totalBetAmount;
  data['totalWin'] = entity.totalWin;
  data['totalSendAmount'] = entity.totalSendAmount;
  return data;
}

extension OrderMainEntityExtension on OrderMainEntity {
  OrderMainEntity copyWith({
    String? gameClassCode,
    String? gameClassName,
    double? rebate,
    double? totalBetAmount,
    double? totalWin,
    double? totalSendAmount,
  }) {
    return OrderMainEntity()
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..rebate = rebate ?? this.rebate
      ..totalBetAmount = totalBetAmount ?? this.totalBetAmount
      ..totalWin = totalWin ?? this.totalWin
      ..totalSendAmount = totalSendAmount ?? this.totalSendAmount;
  }
}

OrderPlatformEntityList $OrderPlatformEntityListFromJson(
    Map<String, dynamic> json) {
  final OrderPlatformEntityList orderPlatformEntityList = OrderPlatformEntityList();
  final List<OrderPlatformEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<OrderPlatformEntity>(e) as OrderPlatformEntity)
      .toList();
  if (list != null) {
    orderPlatformEntityList.list = list;
  }
  return orderPlatformEntityList;
}

Map<String, dynamic> $OrderPlatformEntityListToJson(
    OrderPlatformEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension OrderPlatformEntityListExtension on OrderPlatformEntityList {
  OrderPlatformEntityList copyWith({
    List<OrderPlatformEntity>? list,
  }) {
    return OrderPlatformEntityList()
      ..list = list ?? this.list;
  }
}

OrderPlatformEntity $OrderPlatformEntityFromJson(Map<String, dynamic> json) {
  final OrderPlatformEntity orderPlatformEntity = OrderPlatformEntity();
  final int? platFormId = jsonConvert.convert<int>(json['platFormId']);
  if (platFormId != null) {
    orderPlatformEntity.platFormId = platFormId;
  }
  final String? platFormName = jsonConvert.convert<String>(
      json['platFormName']);
  if (platFormName != null) {
    orderPlatformEntity.platFormName = platFormName;
  }
  final int? rebate = jsonConvert.convert<int>(json['rebate']);
  if (rebate != null) {
    orderPlatformEntity.rebate = rebate;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    orderPlatformEntity.gameClassCode = gameClassCode;
  }
  final String? categoryCode = jsonConvert.convert<String>(
      json['categoryCode']);
  if (categoryCode != null) {
    orderPlatformEntity.categoryCode = categoryCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    orderPlatformEntity.gameClassName = gameClassName;
  }
  final double? totalSendAmount = jsonConvert.convert<double>(
      json['totalSendAmount']);
  if (totalSendAmount != null) {
    orderPlatformEntity.totalSendAmount = totalSendAmount;
  }
  final double? totalBetAmount = jsonConvert.convert<double>(
      json['totalBetAmount']);
  if (totalBetAmount != null) {
    orderPlatformEntity.totalBetAmount = totalBetAmount;
  }
  final double? totalWin = jsonConvert.convert<double>(json['totalWin']);
  if (totalWin != null) {
    orderPlatformEntity.totalWin = totalWin;
  }
  return orderPlatformEntity;
}

Map<String, dynamic> $OrderPlatformEntityToJson(OrderPlatformEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['platFormId'] = entity.platFormId;
  data['platFormName'] = entity.platFormName;
  data['rebate'] = entity.rebate;
  data['gameClassCode'] = entity.gameClassCode;
  data['categoryCode'] = entity.categoryCode;
  data['gameClassName'] = entity.gameClassName;
  data['totalSendAmount'] = entity.totalSendAmount;
  data['totalBetAmount'] = entity.totalBetAmount;
  data['totalWin'] = entity.totalWin;
  return data;
}

extension OrderPlatformEntityExtension on OrderPlatformEntity {
  OrderPlatformEntity copyWith({
    int? platFormId,
    String? platFormName,
    int? rebate,
    String? gameClassCode,
    String? categoryCode,
    String? gameClassName,
    double? totalSendAmount,
    double? totalBetAmount,
    double? totalWin,
  }) {
    return OrderPlatformEntity()
      ..platFormId = platFormId ?? this.platFormId
      ..platFormName = platFormName ?? this.platFormName
      ..rebate = rebate ?? this.rebate
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..categoryCode = categoryCode ?? this.categoryCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..totalSendAmount = totalSendAmount ?? this.totalSendAmount
      ..totalBetAmount = totalBetAmount ?? this.totalBetAmount
      ..totalWin = totalWin ?? this.totalWin;
  }
}