import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/video_filter_entity.dart';

VideoFilterEntityList $VideoFilterEntityListFromJson(
    Map<String, dynamic> json) {
  final VideoFilterEntityList videoFilterEntityList = VideoFilterEntityList();
  final List<VideoFilterEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<VideoFilterEntity>(e) as VideoFilterEntity)
      .toList();
  if (list != null) {
    videoFilterEntityList.list = list;
  }
  return videoFilterEntityList;
}

Map<String, dynamic> $VideoFilterEntityListToJson(
    VideoFilterEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension VideoFilterEntityListExtension on VideoFilterEntityList {
  VideoFilterEntityList copyWith({
    List<VideoFilterEntity>? list,
  }) {
    return VideoFilterEntityList()
      ..list = list ?? this.list;
  }
}

VideoFilterEntity $VideoFilterEntityFromJson(Map<String, dynamic> json) {
  final VideoFilterEntity videoFilterEntity = VideoFilterEntity();
  final List<String>? values = (json['values'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (values != null) {
    videoFilterEntity.values = values;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    videoFilterEntity.title = title;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    videoFilterEntity.type = type;
  }
  return videoFilterEntity;
}

Map<String, dynamic> $VideoFilterEntityToJson(VideoFilterEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['values'] = entity.values;
  data['title'] = entity.title;
  data['type'] = entity.type;
  return data;
}

extension VideoFilterEntityExtension on VideoFilterEntity {
  VideoFilterEntity copyWith({
    List<String>? values,
    String? title,
    String? type,
  }) {
    return VideoFilterEntity()
      ..values = values ?? this.values
      ..title = title ?? this.title
      ..type = type ?? this.type;
  }
}