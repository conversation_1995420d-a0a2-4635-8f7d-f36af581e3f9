import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/commission_details_entity.dart';

CommissionDetailsEntity $CommissionDetailsEntityFromJson(
    Map<String, dynamic> json) {
  final CommissionDetailsEntity commissionDetailsEntity = CommissionDetailsEntity();
  final List<CommissionDetailsRecords>? records = (json['records'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<CommissionDetailsRecords>(
          e) as CommissionDetailsRecords).toList();
  if (records != null) {
    commissionDetailsEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    commissionDetailsEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    commissionDetailsEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    commissionDetailsEntity.current = current;
  }
  final List<CommissionDetailsOrders>? orders = (json['orders'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<CommissionDetailsOrders>(
          e) as CommissionDetailsOrders).toList();
  if (orders != null) {
    commissionDetailsEntity.orders = orders;
  }
  final CommissionDetailsOptimizeCountSql? optimizeCountSql = jsonConvert
      .convert<CommissionDetailsOptimizeCountSql>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    commissionDetailsEntity.optimizeCountSql = optimizeCountSql;
  }
  final CommissionDetailsSearchCount? searchCount = jsonConvert.convert<
      CommissionDetailsSearchCount>(json['searchCount']);
  if (searchCount != null) {
    commissionDetailsEntity.searchCount = searchCount;
  }
  final bool? optimizeJoinOfCountSql = jsonConvert.convert<bool>(
      json['optimizeJoinOfCountSql']);
  if (optimizeJoinOfCountSql != null) {
    commissionDetailsEntity.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    commissionDetailsEntity.maxLimit = maxLimit;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    commissionDetailsEntity.countId = countId;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    commissionDetailsEntity.pages = pages;
  }
  return commissionDetailsEntity;
}

Map<String, dynamic> $CommissionDetailsEntityToJson(
    CommissionDetailsEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders?.map((v) => v.toJson()).toList();
  data['optimizeCountSql'] = entity.optimizeCountSql?.toJson();
  data['searchCount'] = entity.searchCount?.toJson();
  data['optimizeJoinOfCountSql'] = entity.optimizeJoinOfCountSql;
  data['maxLimit'] = entity.maxLimit;
  data['countId'] = entity.countId;
  data['pages'] = entity.pages;
  return data;
}

extension CommissionDetailsEntityExtension on CommissionDetailsEntity {
  CommissionDetailsEntity copyWith({
    List<CommissionDetailsRecords>? records,
    int? total,
    int? size,
    int? current,
    List<CommissionDetailsOrders>? orders,
    CommissionDetailsOptimizeCountSql? optimizeCountSql,
    CommissionDetailsSearchCount? searchCount,
    bool? optimizeJoinOfCountSql,
    int? maxLimit,
    String? countId,
    int? pages,
  }) {
    return CommissionDetailsEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..searchCount = searchCount ?? this.searchCount
      ..optimizeJoinOfCountSql = optimizeJoinOfCountSql ??
          this.optimizeJoinOfCountSql
      ..maxLimit = maxLimit ?? this.maxLimit
      ..countId = countId ?? this.countId
      ..pages = pages ?? this.pages;
  }
}

CommissionDetailsRecords $CommissionDetailsRecordsFromJson(
    Map<String, dynamic> json) {
  final CommissionDetailsRecords commissionDetailsRecords = CommissionDetailsRecords();
  final String? belongDate = jsonConvert.convert<String>(json['belongDate']);
  if (belongDate != null) {
    commissionDetailsRecords.belongDate = belongDate;
  }
  final String? subUserNo = jsonConvert.convert<String>(json['subUserNo']);
  if (subUserNo != null) {
    commissionDetailsRecords.subUserNo = subUserNo;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    commissionDetailsRecords.amount = amount;
  }
  final double? commissionAmount = jsonConvert.convert<double>(
      json['commissionAmount']);
  if (commissionAmount != null) {
    commissionDetailsRecords.commissionAmount = commissionAmount;
  }
  return commissionDetailsRecords;
}

Map<String, dynamic> $CommissionDetailsRecordsToJson(
    CommissionDetailsRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['belongDate'] = entity.belongDate;
  data['subUserNo'] = entity.subUserNo;
  data['amount'] = entity.amount;
  data['commissionAmount'] = entity.commissionAmount;
  return data;
}

extension CommissionDetailsRecordsExtension on CommissionDetailsRecords {
  CommissionDetailsRecords copyWith({
    String? belongDate,
    String? subUserNo,
    double? amount,
    double? commissionAmount,
  }) {
    return CommissionDetailsRecords()
      ..belongDate = belongDate ?? this.belongDate
      ..subUserNo = subUserNo ?? this.subUserNo
      ..amount = amount ?? this.amount
      ..commissionAmount = commissionAmount ?? this.commissionAmount;
  }
}

CommissionDetailsOrders $CommissionDetailsOrdersFromJson(
    Map<String, dynamic> json) {
  final CommissionDetailsOrders commissionDetailsOrders = CommissionDetailsOrders();
  final String? column = jsonConvert.convert<String>(json['column']);
  if (column != null) {
    commissionDetailsOrders.column = column;
  }
  final bool? asc = jsonConvert.convert<bool>(json['asc']);
  if (asc != null) {
    commissionDetailsOrders.asc = asc;
  }
  return commissionDetailsOrders;
}

Map<String, dynamic> $CommissionDetailsOrdersToJson(
    CommissionDetailsOrders entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['column'] = entity.column;
  data['asc'] = entity.asc;
  return data;
}

extension CommissionDetailsOrdersExtension on CommissionDetailsOrders {
  CommissionDetailsOrders copyWith({
    String? column,
    bool? asc,
  }) {
    return CommissionDetailsOrders()
      ..column = column ?? this.column
      ..asc = asc ?? this.asc;
  }
}

CommissionDetailsOptimizeCountSql $CommissionDetailsOptimizeCountSqlFromJson(
    Map<String, dynamic> json) {
  final CommissionDetailsOptimizeCountSql commissionDetailsOptimizeCountSql = CommissionDetailsOptimizeCountSql();
  return commissionDetailsOptimizeCountSql;
}

Map<String, dynamic> $CommissionDetailsOptimizeCountSqlToJson(
    CommissionDetailsOptimizeCountSql entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension CommissionDetailsOptimizeCountSqlExtension on CommissionDetailsOptimizeCountSql {
}

CommissionDetailsSearchCount $CommissionDetailsSearchCountFromJson(
    Map<String, dynamic> json) {
  final CommissionDetailsSearchCount commissionDetailsSearchCount = CommissionDetailsSearchCount();
  return commissionDetailsSearchCount;
}

Map<String, dynamic> $CommissionDetailsSearchCountToJson(
    CommissionDetailsSearchCount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension CommissionDetailsSearchCountExtension on CommissionDetailsSearchCount {
}