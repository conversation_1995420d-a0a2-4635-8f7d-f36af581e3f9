import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/winning_entity.dart';

WinningEntityList $WinningEntityListFromJson(Map<String, dynamic> json) {
  final WinningEntityList winningEntityList = WinningEntityList();
  final List<WinningEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<WinningEntity>(e) as WinningEntity)
      .toList();
  if (list != null) {
    winningEntityList.list = list;
  }
  return winningEntityList;
}

Map<String, dynamic> $WinningEntityListToJson(WinningEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension WinningEntityListExtension on WinningEntityList {
  WinningEntityList copyWith({
    List<WinningEntity>? list,
  }) {
    return WinningEntityList()
      ..list = list ?? this.list;
  }
}

WinningEntity $WinningEntityFromJson(Map<String, dynamic> json) {
  final WinningEntity winningEntity = WinningEntity();
  final String? gameName = jsonConvert.convert<String>(json['gameName']);
  if (gameName != null) {
    winningEntity.gameName = gameName;
  }
  final int? profile = jsonConvert.convert<int>(json['profile']);
  if (profile != null) {
    winningEntity.profile = profile;
  }
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  if (nickName != null) {
    winningEntity.nickName = nickName;
  }
  return winningEntity;
}

Map<String, dynamic> $WinningEntityToJson(WinningEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['gameName'] = entity.gameName;
  data['profile'] = entity.profile;
  data['nickName'] = entity.nickName;
  return data;
}

extension WinningEntityExtension on WinningEntity {
  WinningEntity copyWith({
    String? gameName,
    int? profile,
    String? nickName,
  }) {
    return WinningEntity()
      ..gameName = gameName ?? this.gameName
      ..profile = profile ?? this.profile
      ..nickName = nickName ?? this.nickName;
  }
}