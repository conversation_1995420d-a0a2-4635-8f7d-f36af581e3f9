import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/statement_filter_way_entity.dart';

StatementFilterWayList $StatementFilterWayListFromJson(
    Map<String, dynamic> json) {
  final StatementFilterWayList statementFilterWayList = StatementFilterWayList();
  final List<StatementFilterWay>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<StatementFilterWay>(e) as StatementFilterWay)
      .toList();
  if (list != null) {
    statementFilterWayList.list = list;
  }
  return statementFilterWayList;
}

Map<String, dynamic> $StatementFilterWayListToJson(
    StatementFilterWayList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension StatementFilterWayListExtension on StatementFilterWayList {
  StatementFilterWayList copyWith({
    List<StatementFilterWay>? list,
  }) {
    return StatementFilterWayList()
      ..list = list ?? this.list;
  }
}

StatementFilterWay $StatementFilterWayFromJson(Map<String, dynamic> json) {
  final StatementFilterWay statementFilterWay = StatementFilterWay();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    statementFilterWay.id = id;
  }
  final String? changeWayCode = jsonConvert.convert<String>(
      json['changeWayCode']);
  if (changeWayCode != null) {
    statementFilterWay.changeWayCode = changeWayCode;
  }
  final String? changeWayName = jsonConvert.convert<String>(
      json['changeWayName']);
  if (changeWayName != null) {
    statementFilterWay.changeWayName = changeWayName;
  }
  final bool? isSel = jsonConvert.convert<bool>(json['isSel']);
  if (isSel != null) {
    statementFilterWay.isSel = isSel;
  }
  return statementFilterWay;
}

Map<String, dynamic> $StatementFilterWayToJson(StatementFilterWay entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['changeWayCode'] = entity.changeWayCode;
  data['changeWayName'] = entity.changeWayName;
  data['isSel'] = entity.isSel;
  return data;
}

extension StatementFilterWayExtension on StatementFilterWay {
  StatementFilterWay copyWith({
    int? id,
    String? changeWayCode,
    String? changeWayName,
    bool? isSel,
  }) {
    return StatementFilterWay()
      ..id = id ?? this.id
      ..changeWayCode = changeWayCode ?? this.changeWayCode
      ..changeWayName = changeWayName ?? this.changeWayName
      ..isSel = isSel ?? this.isSel;
  }
}

StatementFilterTypeList $StatementFilterTypeListFromJson(
    Map<String, dynamic> json) {
  final StatementFilterTypeList statementFilterTypeList = StatementFilterTypeList();
  final List<StatementFilterType>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<StatementFilterType>(e) as StatementFilterType)
      .toList();
  if (list != null) {
    statementFilterTypeList.list = list;
  }
  return statementFilterTypeList;
}

Map<String, dynamic> $StatementFilterTypeListToJson(
    StatementFilterTypeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension StatementFilterTypeListExtension on StatementFilterTypeList {
  StatementFilterTypeList copyWith({
    List<StatementFilterType>? list,
  }) {
    return StatementFilterTypeList()
      ..list = list ?? this.list;
  }
}

StatementFilterType $StatementFilterTypeFromJson(Map<String, dynamic> json) {
  final StatementFilterType statementFilterType = StatementFilterType();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    statementFilterType.id = id;
  }
  final String? changeTypeCode = jsonConvert.convert<String>(
      json['changeTypeCode']);
  if (changeTypeCode != null) {
    statementFilterType.changeTypeCode = changeTypeCode;
  }
  final String? changeTypeName = jsonConvert.convert<String>(
      json['changeTypeName']);
  if (changeTypeName != null) {
    statementFilterType.changeTypeName = changeTypeName;
  }
  final String? changeWayCode = jsonConvert.convert<String>(
      json['changeWayCode']);
  if (changeWayCode != null) {
    statementFilterType.changeWayCode = changeWayCode;
  }
  final bool? isSel = jsonConvert.convert<bool>(json['isSel']);
  if (isSel != null) {
    statementFilterType.isSel = isSel;
  }
  return statementFilterType;
}

Map<String, dynamic> $StatementFilterTypeToJson(StatementFilterType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['changeTypeCode'] = entity.changeTypeCode;
  data['changeTypeName'] = entity.changeTypeName;
  data['changeWayCode'] = entity.changeWayCode;
  data['isSel'] = entity.isSel;
  return data;
}

extension StatementFilterTypeExtension on StatementFilterType {
  StatementFilterType copyWith({
    int? id,
    String? changeTypeCode,
    String? changeTypeName,
    String? changeWayCode,
    bool? isSel,
  }) {
    return StatementFilterType()
      ..id = id ?? this.id
      ..changeTypeCode = changeTypeCode ?? this.changeTypeCode
      ..changeTypeName = changeTypeName ?? this.changeTypeName
      ..changeWayCode = changeWayCode ?? this.changeWayCode
      ..isSel = isSel ?? this.isSel;
  }
}