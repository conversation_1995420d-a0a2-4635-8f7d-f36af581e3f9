import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/notifications_response_entity.dart';

NotificationsResponseEntity $NotificationsResponseEntityFromJson(
    Map<String, dynamic> json) {
  final NotificationsResponseEntity notificationsResponseEntity = NotificationsResponseEntity();
  final List<NotificationsRecords>? records = (json['records'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<NotificationsRecords>(e) as NotificationsRecords)
      .toList();
  if (records != null) {
    notificationsResponseEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    notificationsResponseEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    notificationsResponseEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    notificationsResponseEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    notificationsResponseEntity.pages = pages;
  }
  return notificationsResponseEntity;
}

Map<String, dynamic> $NotificationsResponseEntityToJson(
    NotificationsResponseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension NotificationsResponseEntityExtension on NotificationsResponseEntity {
  NotificationsResponseEntity copyWith({
    List<NotificationsRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return NotificationsResponseEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

NotificationsRecords $NotificationsRecordsFromJson(Map<String, dynamic> json) {
  final NotificationsRecords notificationsRecords = NotificationsRecords();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    notificationsRecords.id = id;
  }
  final String? siteMessageTitle = jsonConvert.convert<String>(
      json['siteMessageTitle']);
  if (siteMessageTitle != null) {
    notificationsRecords.siteMessageTitle = siteMessageTitle;
  }
  final String? siteMessageContent = jsonConvert.convert<String>(
      json['siteMessageContent']);
  if (siteMessageContent != null) {
    notificationsRecords.siteMessageContent = siteMessageContent;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    notificationsRecords.createTime = createTime;
  }
  final int? siteMessageRead = jsonConvert.convert<int>(
      json['siteMessageRead']);
  if (siteMessageRead != null) {
    notificationsRecords.siteMessageRead = siteMessageRead;
  }
  return notificationsRecords;
}

Map<String, dynamic> $NotificationsRecordsToJson(NotificationsRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['siteMessageTitle'] = entity.siteMessageTitle;
  data['siteMessageContent'] = entity.siteMessageContent;
  data['createTime'] = entity.createTime;
  data['siteMessageRead'] = entity.siteMessageRead;
  return data;
}

extension NotificationsRecordsExtension on NotificationsRecords {
  NotificationsRecords copyWith({
    int? id,
    String? siteMessageTitle,
    String? siteMessageContent,
    String? createTime,
    int? siteMessageRead,
  }) {
    return NotificationsRecords()
      ..id = id ?? this.id
      ..siteMessageTitle = siteMessageTitle ?? this.siteMessageTitle
      ..siteMessageContent = siteMessageContent ?? this.siteMessageContent
      ..createTime = createTime ?? this.createTime
      ..siteMessageRead = siteMessageRead ?? this.siteMessageRead;
  }
}