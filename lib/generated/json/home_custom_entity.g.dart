import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/home_custom_entity.dart';
import 'package:equatable/equatable.dart';


HomeCustomEntity $HomeCustomEntityFromJson(Map<String, dynamic> json) {
  final HomeCustomEntity homeCustomEntity = HomeCustomEntity();
  final List<String>? banners = (json['banners'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (banners != null) {
    homeCustomEntity.banners = banners;
  }
  final List<String>? notices = (json['notices'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (notices != null) {
    homeCustomEntity.notices = notices;
  }
  final HomeCustomSection? jin = jsonConvert.convert<HomeCustomSection>(
      json['jin']);
  if (jin != null) {
    homeCustomEntity.jin = jin;
  }
  final HomeCustomSection? sport = jsonConvert.convert<HomeCustomSection>(
      json['sport']);
  if (sport != null) {
    homeCustomEntity.sport = sport;
  }
  final HomeCustomSection? casino = jsonConvert.convert<HomeCustomSection>(
      json['casino']);
  if (casino != null) {
    homeCustomEntity.casino = casino;
  }
  final HomeCustomSection? lottery = jsonConvert.convert<HomeCustomSection>(
      json['lottery']);
  if (lottery != null) {
    homeCustomEntity.lottery = lottery;
  }
  final HomeCustomSection? eSport = jsonConvert.convert<HomeCustomSection>(
      json['eSport']);
  if (eSport != null) {
    homeCustomEntity.eSport = eSport;
  }
  final HomeCustomSection? game = jsonConvert.convert<HomeCustomSection>(
      json['game']);
  if (game != null) {
    homeCustomEntity.game = game;
  }
  final HomeCustomSection? rank = jsonConvert.convert<HomeCustomSection>(
      json['rank']);
  if (rank != null) {
    homeCustomEntity.rank = rank;
  }
  return homeCustomEntity;
}

Map<String, dynamic> $HomeCustomEntityToJson(HomeCustomEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['banners'] = entity.banners;
  data['notices'] = entity.notices;
  data['jin'] = entity.jin?.toJson();
  data['sport'] = entity.sport?.toJson();
  data['casino'] = entity.casino?.toJson();
  data['lottery'] = entity.lottery?.toJson();
  data['eSport'] = entity.eSport?.toJson();
  data['game'] = entity.game?.toJson();
  data['rank'] = entity.rank?.toJson();
  return data;
}

extension HomeCustomEntityExtension on HomeCustomEntity {
  HomeCustomEntity copyWith({
    List<String>? banners,
    List<String>? notices,
    HomeCustomSection? jin,
    HomeCustomSection? sport,
    HomeCustomSection? casino,
    HomeCustomSection? lottery,
    HomeCustomSection? eSport,
    HomeCustomSection? game,
    HomeCustomSection? rank,
  }) {
    return HomeCustomEntity()
      ..banners = banners ?? this.banners
      ..notices = notices ?? this.notices
      ..jin = jin ?? this.jin
      ..sport = sport ?? this.sport
      ..casino = casino ?? this.casino
      ..lottery = lottery ?? this.lottery
      ..eSport = eSport ?? this.eSport
      ..game = game ?? this.game
      ..rank = rank ?? this.rank;
  }
}

HomeCustomSection $HomeCustomSectionFromJson(Map<String, dynamic> json) {
  final HomeCustomSection homeCustomSection = HomeCustomSection();
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    homeCustomSection.title = title;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    homeCustomSection.icon = icon;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['isHot']);
  if (isHot != null) {
    homeCustomSection.isHot = isHot;
  }
  final List<HomeCustomSubItem>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<HomeCustomSubItem>(e) as HomeCustomSubItem)
      .toList();
  if (list != null) {
    homeCustomSection.list = list;
  }
  return homeCustomSection;
}

Map<String, dynamic> $HomeCustomSectionToJson(HomeCustomSection entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['title'] = entity.title;
  data['icon'] = entity.icon;
  data['isHot'] = entity.isHot;
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension HomeCustomSectionExtension on HomeCustomSection {
  HomeCustomSection copyWith({
    String? title,
    String? icon,
    bool? isHot,
    List<HomeCustomSubItem>? list,
  }) {
    return HomeCustomSection()
      ..title = title ?? this.title
      ..icon = icon ?? this.icon
      ..isHot = isHot ?? this.isHot
      ..list = list ?? this.list;
  }
}

HomeCustomSubItem $HomeCustomSubItemFromJson(Map<String, dynamic> json) {
  final HomeCustomSubItem homeCustomSubItem = HomeCustomSubItem();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeCustomSubItem.id = id;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    homeCustomSubItem.image = image;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    homeCustomSubItem.name = name;
  }
  final String? eName = jsonConvert.convert<String>(json['eName']);
  if (eName != null) {
    homeCustomSubItem.eName = eName;
  }
  final String? loginUrl = jsonConvert.convert<String>(json['loginUrl']);
  if (loginUrl != null) {
    homeCustomSubItem.loginUrl = loginUrl;
  }
  final int? thirdPlatformId = jsonConvert.convert<int>(
      json['thirdPlatformId']);
  if (thirdPlatformId != null) {
    homeCustomSubItem.thirdPlatformId = thirdPlatformId;
  }
  final HomeJumpType? type = jsonConvert.convert<HomeJumpType>(json['type']);
  if (type != null) {
    homeCustomSubItem.type = type;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['isHot']);
  if (isHot != null) {
    homeCustomSubItem.isHot = isHot;
  }
  return homeCustomSubItem;
}

Map<String, dynamic> $HomeCustomSubItemToJson(HomeCustomSubItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['image'] = entity.image;
  data['name'] = entity.name;
  data['eName'] = entity.eName;
  data['loginUrl'] = entity.loginUrl;
  data['thirdPlatformId'] = entity.thirdPlatformId;
  data['type'] = entity.type.toJson();
  data['isHot'] = entity.isHot;
  return data;
}

extension HomeCustomSubItemExtension on HomeCustomSubItem {
  HomeCustomSubItem copyWith({
    int? id,
    String? image,
    String? name,
    String? eName,
    String? loginUrl,
    int? thirdPlatformId,
    HomeJumpType? type,
    bool? isHot,
  }) {
    return HomeCustomSubItem()
      ..id = id ?? this.id
      ..image = image ?? this.image
      ..name = name ?? this.name
      ..eName = eName ?? this.eName
      ..loginUrl = loginUrl ?? this.loginUrl
      ..thirdPlatformId = thirdPlatformId ?? this.thirdPlatformId
      ..type = type ?? this.type
      ..isHot = isHot ?? this.isHot;
  }
}