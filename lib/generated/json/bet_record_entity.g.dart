import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/bet_record_entity.dart';

BetRecordEntity $BetRecordEntityFromJson(Map<String, dynamic> json) {
  final BetRecordEntity betRecordEntity = BetRecordEntity();
  final BetRecordPage? page = jsonConvert.convert<BetRecordPage>(json['page']);
  if (page != null) {
    betRecordEntity.page = page;
  }
  final int? betAmountToday = jsonConvert.convert<int>(json['betAmountToday']);
  if (betAmountToday != null) {
    betRecordEntity.betAmountToday = betAmountToday;
  }
  final int? totalWinToday = jsonConvert.convert<int>(json['totalWinToday']);
  if (totalWinToday != null) {
    betRecordEntity.totalWinToday = totalWinToday;
  }
  final int? totalSendAmount = jsonConvert.convert<int>(
      json['totalSendAmount']);
  if (totalSendAmount != null) {
    betRecordEntity.totalSendAmount = totalSendAmount;
  }
  return betRecordEntity;
}

Map<String, dynamic> $BetRecordEntityToJson(BetRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page.toJson();
  data['betAmountToday'] = entity.betAmountToday;
  data['totalWinToday'] = entity.totalWinToday;
  data['totalSendAmount'] = entity.totalSendAmount;
  return data;
}

extension BetRecordEntityExtension on BetRecordEntity {
  BetRecordEntity copyWith({
    BetRecordPage? page,
    int? betAmountToday,
    int? totalWinToday,
    int? totalSendAmount,
  }) {
    return BetRecordEntity()
      ..page = page ?? this.page
      ..betAmountToday = betAmountToday ?? this.betAmountToday
      ..totalWinToday = totalWinToday ?? this.totalWinToday
      ..totalSendAmount = totalSendAmount ?? this.totalSendAmount;
  }
}

BetRecordPage $BetRecordPageFromJson(Map<String, dynamic> json) {
  final BetRecordPage betRecordPage = BetRecordPage();
  final List<BetRecordPageRecords>? records = (json['records'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<BetRecordPageRecords>(e) as BetRecordPageRecords)
      .toList();
  if (records != null) {
    betRecordPage.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    betRecordPage.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    betRecordPage.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    betRecordPage.current = current;
  }
  final List<BetRecordPageOrders>? orders = (json['orders'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<BetRecordPageOrders>(e) as BetRecordPageOrders)
      .toList();
  if (orders != null) {
    betRecordPage.orders = orders;
  }
  final BetRecordPageOptimizeCountSql? optimizeCountSql = jsonConvert.convert<
      BetRecordPageOptimizeCountSql>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    betRecordPage.optimizeCountSql = optimizeCountSql;
  }
  final BetRecordPageSearchCount? searchCount = jsonConvert.convert<
      BetRecordPageSearchCount>(json['searchCount']);
  if (searchCount != null) {
    betRecordPage.searchCount = searchCount;
  }
  final bool? optimizeJoinOfCountSql = jsonConvert.convert<bool>(
      json['optimizeJoinOfCountSql']);
  if (optimizeJoinOfCountSql != null) {
    betRecordPage.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    betRecordPage.maxLimit = maxLimit;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    betRecordPage.countId = countId;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    betRecordPage.pages = pages;
  }
  return betRecordPage;
}

Map<String, dynamic> $BetRecordPageToJson(BetRecordPage entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders.map((v) => v.toJson()).toList();
  data['optimizeCountSql'] = entity.optimizeCountSql.toJson();
  data['searchCount'] = entity.searchCount.toJson();
  data['optimizeJoinOfCountSql'] = entity.optimizeJoinOfCountSql;
  data['maxLimit'] = entity.maxLimit;
  data['countId'] = entity.countId;
  data['pages'] = entity.pages;
  return data;
}

extension BetRecordPageExtension on BetRecordPage {
  BetRecordPage copyWith({
    List<BetRecordPageRecords>? records,
    int? total,
    int? size,
    int? current,
    List<BetRecordPageOrders>? orders,
    BetRecordPageOptimizeCountSql? optimizeCountSql,
    BetRecordPageSearchCount? searchCount,
    bool? optimizeJoinOfCountSql,
    int? maxLimit,
    String? countId,
    int? pages,
  }) {
    return BetRecordPage()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..searchCount = searchCount ?? this.searchCount
      ..optimizeJoinOfCountSql = optimizeJoinOfCountSql ??
          this.optimizeJoinOfCountSql
      ..maxLimit = maxLimit ?? this.maxLimit
      ..countId = countId ?? this.countId
      ..pages = pages ?? this.pages;
  }
}

BetRecordPageRecords $BetRecordPageRecordsFromJson(Map<String, dynamic> json) {
  final BetRecordPageRecords betRecordPageRecords = BetRecordPageRecords();
  final String? betOrderNo = jsonConvert.convert<String>(json['betOrderNo']);
  if (betOrderNo != null) {
    betRecordPageRecords.betOrderNo = betOrderNo;
  }
  final String? betTime = jsonConvert.convert<String>(json['betTime']);
  if (betTime != null) {
    betRecordPageRecords.betTime = betTime;
  }
  final String? lotteryName = jsonConvert.convert<String>(json['lotteryName']);
  if (lotteryName != null) {
    betRecordPageRecords.lotteryName = lotteryName;
  }
  final String? periodId = jsonConvert.convert<String>(json['periodId']);
  if (periodId != null) {
    betRecordPageRecords.periodId = periodId;
  }
  final String? itemType = jsonConvert.convert<String>(json['itemType']);
  if (itemType != null) {
    betRecordPageRecords.itemType = itemType;
  }
  final String? itemObject = jsonConvert.convert<String>(json['itemObject']);
  if (itemObject != null) {
    betRecordPageRecords.itemObject = itemObject;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    betRecordPageRecords.userNo = userNo;
  }
  final int? odds = jsonConvert.convert<int>(json['odds']);
  if (odds != null) {
    betRecordPageRecords.odds = odds;
  }
  final int? betAmount = jsonConvert.convert<int>(json['betAmount']);
  if (betAmount != null) {
    betRecordPageRecords.betAmount = betAmount;
  }
  final int? winAmount = jsonConvert.convert<int>(json['winAmount']);
  if (winAmount != null) {
    betRecordPageRecords.winAmount = winAmount;
  }
  final int? orderStatus = jsonConvert.convert<int>(json['orderStatus']);
  if (orderStatus != null) {
    betRecordPageRecords.orderStatus = orderStatus;
  }
  final int? returnRate = jsonConvert.convert<int>(json['returnRate']);
  if (returnRate != null) {
    betRecordPageRecords.returnRate = returnRate;
  }
  final int? returnAmount = jsonConvert.convert<int>(json['returnAmount']);
  if (returnAmount != null) {
    betRecordPageRecords.returnAmount = returnAmount;
  }
  final String? openResult = jsonConvert.convert<String>(json['openResult']);
  if (openResult != null) {
    betRecordPageRecords.openResult = openResult;
  }
  final int? sendAmount = jsonConvert.convert<int>(json['sendAmount']);
  if (sendAmount != null) {
    betRecordPageRecords.sendAmount = sendAmount;
  }
  final String? lotteryOptions = jsonConvert.convert<String>(
      json['lotteryOptions']);
  if (lotteryOptions != null) {
    betRecordPageRecords.lotteryOptions = lotteryOptions;
  }
  final int? gameResult = jsonConvert.convert<int>(json['gameResult']);
  if (gameResult != null) {
    betRecordPageRecords.gameResult = gameResult;
  }
  final String? gameCategoryCode = jsonConvert.convert<String>(
      json['gameCategoryCode']);
  if (gameCategoryCode != null) {
    betRecordPageRecords.gameCategoryCode = gameCategoryCode;
  }
  final int? accountMoney = jsonConvert.convert<int>(json['accountMoney']);
  if (accountMoney != null) {
    betRecordPageRecords.accountMoney = accountMoney;
  }
  final String? betIp = jsonConvert.convert<String>(json['betIp']);
  if (betIp != null) {
    betRecordPageRecords.betIp = betIp;
  }
  final String? ipAddress = jsonConvert.convert<String>(json['ipAddress']);
  if (ipAddress != null) {
    betRecordPageRecords.ipAddress = ipAddress;
  }
  final String? requestUrl = jsonConvert.convert<String>(json['requestUrl']);
  if (requestUrl != null) {
    betRecordPageRecords.requestUrl = requestUrl;
  }
  return betRecordPageRecords;
}

Map<String, dynamic> $BetRecordPageRecordsToJson(BetRecordPageRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['betOrderNo'] = entity.betOrderNo;
  data['betTime'] = entity.betTime;
  data['lotteryName'] = entity.lotteryName;
  data['periodId'] = entity.periodId;
  data['itemType'] = entity.itemType;
  data['itemObject'] = entity.itemObject;
  data['userNo'] = entity.userNo;
  data['odds'] = entity.odds;
  data['betAmount'] = entity.betAmount;
  data['winAmount'] = entity.winAmount;
  data['orderStatus'] = entity.orderStatus;
  data['returnRate'] = entity.returnRate;
  data['returnAmount'] = entity.returnAmount;
  data['openResult'] = entity.openResult;
  data['sendAmount'] = entity.sendAmount;
  data['lotteryOptions'] = entity.lotteryOptions;
  data['gameResult'] = entity.gameResult;
  data['gameCategoryCode'] = entity.gameCategoryCode;
  data['accountMoney'] = entity.accountMoney;
  data['betIp'] = entity.betIp;
  data['ipAddress'] = entity.ipAddress;
  data['requestUrl'] = entity.requestUrl;
  return data;
}

extension BetRecordPageRecordsExtension on BetRecordPageRecords {
  BetRecordPageRecords copyWith({
    String? betOrderNo,
    String? betTime,
    String? lotteryName,
    String? periodId,
    String? itemType,
    String? itemObject,
    String? userNo,
    int? odds,
    int? betAmount,
    int? winAmount,
    int? orderStatus,
    int? returnRate,
    int? returnAmount,
    String? openResult,
    int? sendAmount,
    String? lotteryOptions,
    int? gameResult,
    String? gameCategoryCode,
    int? accountMoney,
    String? betIp,
    String? ipAddress,
    String? requestUrl,
  }) {
    return BetRecordPageRecords()
      ..betOrderNo = betOrderNo ?? this.betOrderNo
      ..betTime = betTime ?? this.betTime
      ..lotteryName = lotteryName ?? this.lotteryName
      ..periodId = periodId ?? this.periodId
      ..itemType = itemType ?? this.itemType
      ..itemObject = itemObject ?? this.itemObject
      ..userNo = userNo ?? this.userNo
      ..odds = odds ?? this.odds
      ..betAmount = betAmount ?? this.betAmount
      ..winAmount = winAmount ?? this.winAmount
      ..orderStatus = orderStatus ?? this.orderStatus
      ..returnRate = returnRate ?? this.returnRate
      ..returnAmount = returnAmount ?? this.returnAmount
      ..openResult = openResult ?? this.openResult
      ..sendAmount = sendAmount ?? this.sendAmount
      ..lotteryOptions = lotteryOptions ?? this.lotteryOptions
      ..gameResult = gameResult ?? this.gameResult
      ..gameCategoryCode = gameCategoryCode ?? this.gameCategoryCode
      ..accountMoney = accountMoney ?? this.accountMoney
      ..betIp = betIp ?? this.betIp
      ..ipAddress = ipAddress ?? this.ipAddress
      ..requestUrl = requestUrl ?? this.requestUrl;
  }
}

BetRecordPageOrders $BetRecordPageOrdersFromJson(Map<String, dynamic> json) {
  final BetRecordPageOrders betRecordPageOrders = BetRecordPageOrders();
  final String? column = jsonConvert.convert<String>(json['column']);
  if (column != null) {
    betRecordPageOrders.column = column;
  }
  final bool? asc = jsonConvert.convert<bool>(json['asc']);
  if (asc != null) {
    betRecordPageOrders.asc = asc;
  }
  return betRecordPageOrders;
}

Map<String, dynamic> $BetRecordPageOrdersToJson(BetRecordPageOrders entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['column'] = entity.column;
  data['asc'] = entity.asc;
  return data;
}

extension BetRecordPageOrdersExtension on BetRecordPageOrders {
  BetRecordPageOrders copyWith({
    String? column,
    bool? asc,
  }) {
    return BetRecordPageOrders()
      ..column = column ?? this.column
      ..asc = asc ?? this.asc;
  }
}

BetRecordPageOptimizeCountSql $BetRecordPageOptimizeCountSqlFromJson(
    Map<String, dynamic> json) {
  final BetRecordPageOptimizeCountSql betRecordPageOptimizeCountSql = BetRecordPageOptimizeCountSql();
  return betRecordPageOptimizeCountSql;
}

Map<String, dynamic> $BetRecordPageOptimizeCountSqlToJson(
    BetRecordPageOptimizeCountSql entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension BetRecordPageOptimizeCountSqlExtension on BetRecordPageOptimizeCountSql {
}

BetRecordPageSearchCount $BetRecordPageSearchCountFromJson(
    Map<String, dynamic> json) {
  final BetRecordPageSearchCount betRecordPageSearchCount = BetRecordPageSearchCount();
  return betRecordPageSearchCount;
}

Map<String, dynamic> $BetRecordPageSearchCountToJson(
    BetRecordPageSearchCount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension BetRecordPageSearchCountExtension on BetRecordPageSearchCount {
}