import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/commission_overview_entity.dart';

CommissionOverviewEntity $CommissionOverviewEntityFromJson(
    Map<String, dynamic> json) {
  final CommissionOverviewEntity commissionOverviewEntity = CommissionOverviewEntity();
  final double? totalCommission = jsonConvert.convert<double>(
      json['totalCommission']);
  if (totalCommission != null) {
    commissionOverviewEntity.totalCommission = totalCommission;
  }
  final double? commission = jsonConvert.convert<double>(json['commission']);
  if (commission != null) {
    commissionOverviewEntity.commission = commission;
  }
  final double? yesterdayCommission = jsonConvert.convert<double>(
      json['yesterdayCommission']);
  if (yesterdayCommission != null) {
    commissionOverviewEntity.yesterdayCommission = yesterdayCommission;
  }
  return commissionOverviewEntity;
}

Map<String, dynamic> $CommissionOverviewEntityToJson(
    CommissionOverviewEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['totalCommission'] = entity.totalCommission;
  data['commission'] = entity.commission;
  data['yesterdayCommission'] = entity.yesterdayCommission;
  return data;
}

extension CommissionOverviewEntityExtension on CommissionOverviewEntity {
  CommissionOverviewEntity copyWith({
    double? totalCommission,
    double? commission,
    double? yesterdayCommission,
  }) {
    return CommissionOverviewEntity()
      ..totalCommission = totalCommission ?? this.totalCommission
      ..commission = commission ?? this.commission
      ..yesterdayCommission = yesterdayCommission ?? this.yesterdayCommission;
  }
}