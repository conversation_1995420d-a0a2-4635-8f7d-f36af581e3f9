import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/promotion_banner_entity.dart';

PromotionBannerEntity $PromotionBannerEntityFromJson(
    Map<String, dynamic> json) {
  final PromotionBannerEntity promotionBannerEntity = PromotionBannerEntity();
  final List<PromotionBannerList>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<PromotionBannerList>(e) as PromotionBannerList)
      .toList();
  if (list != null) {
    promotionBannerEntity.list = list;
  }
  return promotionBannerEntity;
}

Map<String, dynamic> $PromotionBannerEntityToJson(
    PromotionBannerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension PromotionBannerEntityExtension on PromotionBannerEntity {
  PromotionBannerEntity copyWith({
    List<PromotionBannerList>? list,
  }) {
    return PromotionBannerEntity()
      ..list = list ?? this.list;
  }
}

PromotionBannerList $PromotionBannerListFromJson(Map<String, dynamic> json) {
  final PromotionBannerList promotionBannerList = PromotionBannerList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    promotionBannerList.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    promotionBannerList.title = title;
  }
  final String? link = jsonConvert.convert<String>(json['link']);
  if (link != null) {
    promotionBannerList.link = link;
  }
  final String? addtime = jsonConvert.convert<String>(json['addtime']);
  if (addtime != null) {
    promotionBannerList.addtime = addtime;
  }
  final String? aContent = jsonConvert.convert<String>(json['a_content']);
  if (aContent != null) {
    promotionBannerList.aContent = aContent;
  }
  final String? kclass = jsonConvert.convert<String>(json['kclass']);
  if (kclass != null) {
    promotionBannerList.kclass = kclass;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    promotionBannerList.description = description;
  }
  final String? startime = jsonConvert.convert<String>(json['startime']);
  if (startime != null) {
    promotionBannerList.startime = startime;
  }
  final String? endtime = jsonConvert.convert<String>(json['endtime']);
  if (endtime != null) {
    promotionBannerList.endtime = endtime;
  }
  final String? sort = jsonConvert.convert<String>(json['sort']);
  if (sort != null) {
    promotionBannerList.sort = sort;
  }
  final String? cid = jsonConvert.convert<String>(json['cid']);
  if (cid != null) {
    promotionBannerList.cid = cid;
  }
  final String? ctitle = jsonConvert.convert<String>(json['ctitle']);
  if (ctitle != null) {
    promotionBannerList.ctitle = ctitle;
  }
  final String? csort = jsonConvert.convert<String>(json['csort']);
  if (csort != null) {
    promotionBannerList.csort = csort;
  }
  return promotionBannerList;
}

Map<String, dynamic> $PromotionBannerListToJson(PromotionBannerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['title'] = entity.title;
  data['link'] = entity.link;
  data['addtime'] = entity.addtime;
  data['a_content'] = entity.aContent;
  data['kclass'] = entity.kclass;
  data['description'] = entity.description;
  data['startime'] = entity.startime;
  data['endtime'] = entity.endtime;
  data['sort'] = entity.sort;
  data['cid'] = entity.cid;
  data['ctitle'] = entity.ctitle;
  data['csort'] = entity.csort;
  return data;
}

extension PromotionBannerListExtension on PromotionBannerList {
  PromotionBannerList copyWith({
    String? id,
    String? title,
    String? link,
    String? addtime,
    String? aContent,
    String? kclass,
    String? description,
    String? startime,
    String? endtime,
    String? sort,
    String? cid,
    String? ctitle,
    String? csort,
  }) {
    return PromotionBannerList()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..link = link ?? this.link
      ..addtime = addtime ?? this.addtime
      ..aContent = aContent ?? this.aContent
      ..kclass = kclass ?? this.kclass
      ..description = description ?? this.description
      ..startime = startime ?? this.startime
      ..endtime = endtime ?? this.endtime
      ..sort = sort ?? this.sort
      ..cid = cid ?? this.cid
      ..ctitle = ctitle ?? this.ctitle
      ..csort = csort ?? this.csort;
  }
}