import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';

WithdrawRecordEntity $WithdrawRecordEntityFromJson(Map<String, dynamic> json) {
  final WithdrawRecordEntity withdrawRecordEntity = WithdrawRecordEntity();
  final List<WithdrawRecord>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<WithdrawRecord>(e) as WithdrawRecord)
      .toList();
  if (records != null) {
    withdrawRecordEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    withdrawRecordEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    withdrawRecordEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    withdrawRecordEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    withdrawRecordEntity.pages = pages;
  }
  return withdrawRecordEntity;
}

Map<String, dynamic> $WithdrawRecordEntityToJson(WithdrawRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension WithdrawRecordEntityExtension on WithdrawRecordEntity {
  WithdrawRecordEntity copyWith({
    List<WithdrawRecord>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return WithdrawRecordEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

WithdrawRecord $WithdrawRecordFromJson(Map<String, dynamic> json) {
  final WithdrawRecord withdrawRecord = WithdrawRecord();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    withdrawRecord.id = id;
  }
  final String? transactionNo = jsonConvert.convert<String>(
      json['transactionNo']);
  if (transactionNo != null) {
    withdrawRecord.transactionNo = transactionNo;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    withdrawRecord.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    withdrawRecord.userNo = userNo;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    withdrawRecord.realName = realName;
  }
  final String? cardNo = jsonConvert.convert<String>(json['cardNo']);
  if (cardNo != null) {
    withdrawRecord.cardNo = cardNo;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    withdrawRecord.bankName = bankName;
  }
  final int? orderInitialAmount = jsonConvert.convert<int>(
      json['orderInitialAmount']);
  if (orderInitialAmount != null) {
    withdrawRecord.orderInitialAmount = orderInitialAmount;
  }
  final double? orderAmount = jsonConvert.convert<double>(json['orderAmount']);
  if (orderAmount != null) {
    withdrawRecord.orderAmount = orderAmount;
  }
  final dynamic totalBetAmount = json['totalBetAmount'];
  if (totalBetAmount != null) {
    withdrawRecord.totalBetAmount = totalBetAmount;
  }
  final dynamic betAmountRate = json['betAmountRate'];
  if (betAmountRate != null) {
    withdrawRecord.betAmountRate = betAmountRate;
  }
  final double? serviceCharge = jsonConvert.convert<double>(
      json['serviceCharge']);
  if (serviceCharge != null) {
    withdrawRecord.serviceCharge = serviceCharge;
  }
  final double? reduceAmount = jsonConvert.convert<double>(
      json['reduceAmount']);
  if (reduceAmount != null) {
    withdrawRecord.reduceAmount = reduceAmount;
  }
  final double? serviceChargeRate = jsonConvert.convert<double>(
      json['serviceChargeRate']);
  if (serviceChargeRate != null) {
    withdrawRecord.serviceChargeRate = serviceChargeRate;
  }
  final dynamic thanServiceChargeRate = json['thanServiceChargeRate'];
  if (thanServiceChargeRate != null) {
    withdrawRecord.thanServiceChargeRate = thanServiceChargeRate;
  }
  final double? finalAmount = jsonConvert.convert<double>(json['finalAmount']);
  if (finalAmount != null) {
    withdrawRecord.finalAmount = finalAmount;
  }
  final String? cashoutWayCode = jsonConvert.convert<String>(
      json['cashoutWayCode']);
  if (cashoutWayCode != null) {
    withdrawRecord.cashoutWayCode = cashoutWayCode;
  }
  final String? cashoutWayName = jsonConvert.convert<String>(
      json['cashoutWayName']);
  if (cashoutWayName != null) {
    withdrawRecord.cashoutWayName = cashoutWayName;
  }
  final String? cashoutTypeCode = jsonConvert.convert<String>(
      json['cashoutTypeCode']);
  if (cashoutTypeCode != null) {
    withdrawRecord.cashoutTypeCode = cashoutTypeCode;
  }
  final String? cashoutTypeName = jsonConvert.convert<String>(
      json['cashoutTypeName']);
  if (cashoutTypeName != null) {
    withdrawRecord.cashoutTypeName = cashoutTypeName;
  }
  final String? requestTime = jsonConvert.convert<String>(json['requestTime']);
  if (requestTime != null) {
    withdrawRecord.requestTime = requestTime;
  }
  final int? orderStatus = jsonConvert.convert<int>(json['orderStatus']);
  if (orderStatus != null) {
    withdrawRecord.orderStatus = orderStatus;
  }
  final String? operateTime = jsonConvert.convert<String>(json['operateTime']);
  if (operateTime != null) {
    withdrawRecord.operateTime = operateTime;
  }
  final String? operator = jsonConvert.convert<String>(json['operator']);
  if (operator != null) {
    withdrawRecord.operator = operator;
  }
  final int? isFirst = jsonConvert.convert<int>(json['isFirst']);
  if (isFirst != null) {
    withdrawRecord.isFirst = isFirst;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    withdrawRecord.remark = remark;
  }
  final String? refusalRemark = jsonConvert.convert<String>(
      json['refusalRemark']);
  if (refusalRemark != null) {
    withdrawRecord.refusalRemark = refusalRemark;
  }
  final bool? read = jsonConvert.convert<bool>(json['read']);
  if (read != null) {
    withdrawRecord.read = read;
  }
  return withdrawRecord;
}

Map<String, dynamic> $WithdrawRecordToJson(WithdrawRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['transactionNo'] = entity.transactionNo;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['realName'] = entity.realName;
  data['cardNo'] = entity.cardNo;
  data['bankName'] = entity.bankName;
  data['orderInitialAmount'] = entity.orderInitialAmount;
  data['orderAmount'] = entity.orderAmount;
  data['totalBetAmount'] = entity.totalBetAmount;
  data['betAmountRate'] = entity.betAmountRate;
  data['serviceCharge'] = entity.serviceCharge;
  data['reduceAmount'] = entity.reduceAmount;
  data['serviceChargeRate'] = entity.serviceChargeRate;
  data['thanServiceChargeRate'] = entity.thanServiceChargeRate;
  data['finalAmount'] = entity.finalAmount;
  data['cashoutWayCode'] = entity.cashoutWayCode;
  data['cashoutWayName'] = entity.cashoutWayName;
  data['cashoutTypeCode'] = entity.cashoutTypeCode;
  data['cashoutTypeName'] = entity.cashoutTypeName;
  data['requestTime'] = entity.requestTime;
  data['orderStatus'] = entity.orderStatus;
  data['operateTime'] = entity.operateTime;
  data['operator'] = entity.operator;
  data['isFirst'] = entity.isFirst;
  data['remark'] = entity.remark;
  data['refusalRemark'] = entity.refusalRemark;
  data['read'] = entity.read;
  return data;
}

extension WithdrawRecordExtension on WithdrawRecord {
  WithdrawRecord copyWith({
    int? id,
    String? transactionNo,
    int? userId,
    String? userNo,
    String? realName,
    String? cardNo,
    String? bankName,
    int? orderInitialAmount,
    double? orderAmount,
    dynamic totalBetAmount,
    dynamic betAmountRate,
    double? serviceCharge,
    double? reduceAmount,
    double? serviceChargeRate,
    dynamic thanServiceChargeRate,
    double? finalAmount,
    String? cashoutWayCode,
    String? cashoutWayName,
    String? cashoutTypeCode,
    String? cashoutTypeName,
    String? requestTime,
    int? orderStatus,
    String? operateTime,
    String? operator,
    int? isFirst,
    String? remark,
    String? refusalRemark,
    bool? read,
  }) {
    return WithdrawRecord()
      ..id = id ?? this.id
      ..transactionNo = transactionNo ?? this.transactionNo
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..realName = realName ?? this.realName
      ..cardNo = cardNo ?? this.cardNo
      ..bankName = bankName ?? this.bankName
      ..orderInitialAmount = orderInitialAmount ?? this.orderInitialAmount
      ..orderAmount = orderAmount ?? this.orderAmount
      ..totalBetAmount = totalBetAmount ?? this.totalBetAmount
      ..betAmountRate = betAmountRate ?? this.betAmountRate
      ..serviceCharge = serviceCharge ?? this.serviceCharge
      ..reduceAmount = reduceAmount ?? this.reduceAmount
      ..serviceChargeRate = serviceChargeRate ?? this.serviceChargeRate
      ..thanServiceChargeRate = thanServiceChargeRate ??
          this.thanServiceChargeRate
      ..finalAmount = finalAmount ?? this.finalAmount
      ..cashoutWayCode = cashoutWayCode ?? this.cashoutWayCode
      ..cashoutWayName = cashoutWayName ?? this.cashoutWayName
      ..cashoutTypeCode = cashoutTypeCode ?? this.cashoutTypeCode
      ..cashoutTypeName = cashoutTypeName ?? this.cashoutTypeName
      ..requestTime = requestTime ?? this.requestTime
      ..orderStatus = orderStatus ?? this.orderStatus
      ..operateTime = operateTime ?? this.operateTime
      ..operator = operator ?? this.operator
      ..isFirst = isFirst ?? this.isFirst
      ..remark = remark ?? this.remark
      ..refusalRemark = refusalRemark ?? this.refusalRemark
      ..read = read ?? this.read;
  }
}

WithdrawStatusEntity $WithdrawStatusEntityFromJson(Map<String, dynamic> json) {
  final WithdrawStatusEntity withdrawStatusEntity = WithdrawStatusEntity();
  final bool? status = jsonConvert.convert<bool>(json['status']);
  if (status != null) {
    withdrawStatusEntity.status = status;
  }
  final int? statusCode = jsonConvert.convert<int>(json['statusCode']);
  if (statusCode != null) {
    withdrawStatusEntity.statusCode = statusCode;
  }
  final String? errorMessage = jsonConvert.convert<String>(
      json['errorMessage']);
  if (errorMessage != null) {
    withdrawStatusEntity.errorMessage = errorMessage;
  }
  return withdrawStatusEntity;
}

Map<String, dynamic> $WithdrawStatusEntityToJson(WithdrawStatusEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['statusCode'] = entity.statusCode;
  data['errorMessage'] = entity.errorMessage;
  return data;
}

extension WithdrawStatusEntityExtension on WithdrawStatusEntity {
  WithdrawStatusEntity copyWith({
    bool? status,
    int? statusCode,
    String? errorMessage,
  }) {
    return WithdrawStatusEntity()
      ..status = status ?? this.status
      ..statusCode = statusCode ?? this.statusCode
      ..errorMessage = errorMessage ?? this.errorMessage;
  }
}