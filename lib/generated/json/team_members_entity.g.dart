import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/team_members_entity.dart';

TeamMembersEntity $TeamMembersEntityFromJson(Map<String, dynamic> json) {
  final TeamMembersEntity teamMembersEntity = TeamMembersEntity();
  final List<TeamMembersRecords>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<TeamMembersRecords>(e) as TeamMembersRecords)
      .toList();
  if (records != null) {
    teamMembersEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    teamMembersEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    teamMembersEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    teamMembersEntity.current = current;
  }
  final List<TeamMembersOrders>? orders = (json['orders'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<TeamMembersOrders>(e) as TeamMembersOrders)
      .toList();
  if (orders != null) {
    teamMembersEntity.orders = orders;
  }
  final TeamMembersOptimizeCountSql? optimizeCountSql = jsonConvert.convert<
      TeamMembersOptimizeCountSql>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    teamMembersEntity.optimizeCountSql = optimizeCountSql;
  }
  final TeamMembersSearchCount? searchCount = jsonConvert.convert<
      TeamMembersSearchCount>(json['searchCount']);
  if (searchCount != null) {
    teamMembersEntity.searchCount = searchCount;
  }
  final bool? optimizeJoinOfCountSql = jsonConvert.convert<bool>(
      json['optimizeJoinOfCountSql']);
  if (optimizeJoinOfCountSql != null) {
    teamMembersEntity.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    teamMembersEntity.maxLimit = maxLimit;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    teamMembersEntity.countId = countId;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    teamMembersEntity.pages = pages;
  }
  return teamMembersEntity;
}

Map<String, dynamic> $TeamMembersEntityToJson(TeamMembersEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders?.map((v) => v.toJson()).toList();
  data['optimizeCountSql'] = entity.optimizeCountSql?.toJson();
  data['searchCount'] = entity.searchCount?.toJson();
  data['optimizeJoinOfCountSql'] = entity.optimizeJoinOfCountSql;
  data['maxLimit'] = entity.maxLimit;
  data['countId'] = entity.countId;
  data['pages'] = entity.pages;
  return data;
}

extension TeamMembersEntityExtension on TeamMembersEntity {
  TeamMembersEntity copyWith({
    List<TeamMembersRecords>? records,
    int? total,
    int? size,
    int? current,
    List<TeamMembersOrders>? orders,
    TeamMembersOptimizeCountSql? optimizeCountSql,
    TeamMembersSearchCount? searchCount,
    bool? optimizeJoinOfCountSql,
    int? maxLimit,
    String? countId,
    int? pages,
  }) {
    return TeamMembersEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..searchCount = searchCount ?? this.searchCount
      ..optimizeJoinOfCountSql = optimizeJoinOfCountSql ??
          this.optimizeJoinOfCountSql
      ..maxLimit = maxLimit ?? this.maxLimit
      ..countId = countId ?? this.countId
      ..pages = pages ?? this.pages;
  }
}

TeamMembersRecords $TeamMembersRecordsFromJson(Map<String, dynamic> json) {
  final TeamMembersRecords teamMembersRecords = TeamMembersRecords();
  final String? subUserNo = jsonConvert.convert<String>(json['subUserNo']);
  if (subUserNo != null) {
    teamMembersRecords.subUserNo = subUserNo;
  }
  final int? level = jsonConvert.convert<int>(json['level']);
  if (level != null) {
    teamMembersRecords.level = level;
  }
  final String? registerDate = jsonConvert.convert<String>(
      json['registerDate']);
  if (registerDate != null) {
    teamMembersRecords.registerDate = registerDate;
  }
  return teamMembersRecords;
}

Map<String, dynamic> $TeamMembersRecordsToJson(TeamMembersRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subUserNo'] = entity.subUserNo;
  data['level'] = entity.level;
  data['registerDate'] = entity.registerDate;
  return data;
}

extension TeamMembersRecordsExtension on TeamMembersRecords {
  TeamMembersRecords copyWith({
    String? subUserNo,
    int? level,
    String? registerDate,
  }) {
    return TeamMembersRecords()
      ..subUserNo = subUserNo ?? this.subUserNo
      ..level = level ?? this.level
      ..registerDate = registerDate ?? this.registerDate;
  }
}

TeamMembersOrders $TeamMembersOrdersFromJson(Map<String, dynamic> json) {
  final TeamMembersOrders teamMembersOrders = TeamMembersOrders();
  final String? column = jsonConvert.convert<String>(json['column']);
  if (column != null) {
    teamMembersOrders.column = column;
  }
  final bool? asc = jsonConvert.convert<bool>(json['asc']);
  if (asc != null) {
    teamMembersOrders.asc = asc;
  }
  return teamMembersOrders;
}

Map<String, dynamic> $TeamMembersOrdersToJson(TeamMembersOrders entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['column'] = entity.column;
  data['asc'] = entity.asc;
  return data;
}

extension TeamMembersOrdersExtension on TeamMembersOrders {
  TeamMembersOrders copyWith({
    String? column,
    bool? asc,
  }) {
    return TeamMembersOrders()
      ..column = column ?? this.column
      ..asc = asc ?? this.asc;
  }
}

TeamMembersOptimizeCountSql $TeamMembersOptimizeCountSqlFromJson(
    Map<String, dynamic> json) {
  final TeamMembersOptimizeCountSql teamMembersOptimizeCountSql = TeamMembersOptimizeCountSql();
  return teamMembersOptimizeCountSql;
}

Map<String, dynamic> $TeamMembersOptimizeCountSqlToJson(
    TeamMembersOptimizeCountSql entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension TeamMembersOptimizeCountSqlExtension on TeamMembersOptimizeCountSql {
}

TeamMembersSearchCount $TeamMembersSearchCountFromJson(
    Map<String, dynamic> json) {
  final TeamMembersSearchCount teamMembersSearchCount = TeamMembersSearchCount();
  return teamMembersSearchCount;
}

Map<String, dynamic> $TeamMembersSearchCountToJson(
    TeamMembersSearchCount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension TeamMembersSearchCountExtension on TeamMembersSearchCount {
}