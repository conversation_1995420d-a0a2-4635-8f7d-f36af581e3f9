import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/lottery_result_entity.dart';

LotteryResultEntity $LotteryResultEntityFromJson(Map<String, dynamic> json) {
  final LotteryResultEntity lotteryResultEntity = LotteryResultEntity();
  final List<LotteryResultRecords>? records = (json['records'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<LotteryResultRecords>(e) as LotteryResultRecords)
      .toList();
  if (records != null) {
    lotteryResultEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    lotteryResultEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    lotteryResultEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    lotteryResultEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    lotteryResultEntity.pages = pages;
  }
  return lotteryResultEntity;
}

Map<String, dynamic> $LotteryResultEntityToJson(LotteryResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension LotteryResultEntityExtension on LotteryResultEntity {
  LotteryResultEntity copyWith({
    List<LotteryResultRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return LotteryResultEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

LotteryResultRecords $LotteryResultRecordsFromJson(Map<String, dynamic> json) {
  final LotteryResultRecords lotteryResultRecords = LotteryResultRecords();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lotteryResultRecords.id = id;
  }
  final int? lotteryId = jsonConvert.convert<int>(json['lotteryId']);
  if (lotteryId != null) {
    lotteryResultRecords.lotteryId = lotteryId;
  }
  final String? belongDate = jsonConvert.convert<String>(json['belongDate']);
  if (belongDate != null) {
    lotteryResultRecords.belongDate = belongDate;
  }
  final String? periodId = jsonConvert.convert<String>(json['periodId']);
  if (periodId != null) {
    lotteryResultRecords.periodId = periodId;
  }
  final String? beginTime = jsonConvert.convert<String>(json['beginTime']);
  if (beginTime != null) {
    lotteryResultRecords.beginTime = beginTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    lotteryResultRecords.endTime = endTime;
  }
  final String? openTime = jsonConvert.convert<String>(json['openTime']);
  if (openTime != null) {
    lotteryResultRecords.openTime = openTime;
  }
  final int? totalBetCount = jsonConvert.convert<int>(json['totalBetCount']);
  if (totalBetCount != null) {
    lotteryResultRecords.totalBetCount = totalBetCount;
  }
  final double? totalBetAmount = jsonConvert.convert<double>(
      json['totalBetAmount']);
  if (totalBetAmount != null) {
    lotteryResultRecords.totalBetAmount = totalBetAmount;
  }
  final int? totalBetPerson = jsonConvert.convert<int>(json['totalBetPerson']);
  if (totalBetPerson != null) {
    lotteryResultRecords.totalBetPerson = totalBetPerson;
  }
  final double? totalWinAmount = jsonConvert.convert<double>(
      json['totalWinAmount']);
  if (totalWinAmount != null) {
    lotteryResultRecords.totalWinAmount = totalWinAmount;
  }
  final String? openResult = jsonConvert.convert<String>(json['openResult']);
  if (openResult != null) {
    lotteryResultRecords.openResult = openResult;
  }
  final int? openStatus = jsonConvert.convert<int>(json['openStatus']);
  if (openStatus != null) {
    lotteryResultRecords.openStatus = openStatus;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    lotteryResultRecords.createTime = createTime;
  }
  final int? periodNum = jsonConvert.convert<int>(json['periodNum']);
  if (periodNum != null) {
    lotteryResultRecords.periodNum = periodNum;
  }
  final dynamic handler = json['handler'];
  if (handler != null) {
    lotteryResultRecords.handler = handler;
  }
  return lotteryResultRecords;
}

Map<String, dynamic> $LotteryResultRecordsToJson(LotteryResultRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['lotteryId'] = entity.lotteryId;
  data['belongDate'] = entity.belongDate;
  data['periodId'] = entity.periodId;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['openTime'] = entity.openTime;
  data['totalBetCount'] = entity.totalBetCount;
  data['totalBetAmount'] = entity.totalBetAmount;
  data['totalBetPerson'] = entity.totalBetPerson;
  data['totalWinAmount'] = entity.totalWinAmount;
  data['openResult'] = entity.openResult;
  data['openStatus'] = entity.openStatus;
  data['createTime'] = entity.createTime;
  data['periodNum'] = entity.periodNum;
  data['handler'] = entity.handler;
  return data;
}

extension LotteryResultRecordsExtension on LotteryResultRecords {
  LotteryResultRecords copyWith({
    int? id,
    int? lotteryId,
    String? belongDate,
    String? periodId,
    String? beginTime,
    String? endTime,
    String? openTime,
    int? totalBetCount,
    double? totalBetAmount,
    int? totalBetPerson,
    double? totalWinAmount,
    String? openResult,
    int? openStatus,
    String? createTime,
    int? periodNum,
    dynamic handler,
  }) {
    return LotteryResultRecords()
      ..id = id ?? this.id
      ..lotteryId = lotteryId ?? this.lotteryId
      ..belongDate = belongDate ?? this.belongDate
      ..periodId = periodId ?? this.periodId
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..openTime = openTime ?? this.openTime
      ..totalBetCount = totalBetCount ?? this.totalBetCount
      ..totalBetAmount = totalBetAmount ?? this.totalBetAmount
      ..totalBetPerson = totalBetPerson ?? this.totalBetPerson
      ..totalWinAmount = totalWinAmount ?? this.totalWinAmount
      ..openResult = openResult ?? this.openResult
      ..openStatus = openStatus ?? this.openStatus
      ..createTime = createTime ?? this.createTime
      ..periodNum = periodNum ?? this.periodNum
      ..handler = handler ?? this.handler;
  }
}