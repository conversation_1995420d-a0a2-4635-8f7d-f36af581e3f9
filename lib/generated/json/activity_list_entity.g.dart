import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';

ActivityListEntity $ActivityListEntityFromJson(Map<String, dynamic> json) {
  final ActivityListEntity activityListEntity = ActivityListEntity();
  final List<ActivityRecords>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ActivityRecords>(e) as ActivityRecords)
      .toList();
  if (records != null) {
    activityListEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    activityListEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    activityListEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    activityListEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    activityListEntity.pages = pages;
  }
  return activityListEntity;
}

Map<String, dynamic> $ActivityListEntityToJson(ActivityListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension ActivityListEntityExtension on ActivityListEntity {
  ActivityListEntity copyWith({
    List<ActivityRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return ActivityListEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

ActivityRecords $ActivityRecordsFromJson(Map<String, dynamic> json) {
  final ActivityRecords activityRecords = ActivityRecords();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    activityRecords.id = id;
  }
  final String? activeName = jsonConvert.convert<String>(json['activeName']);
  if (activeName != null) {
    activityRecords.activeName = activeName;
  }
  final String? beginTime = jsonConvert.convert<String>(json['beginTime']);
  if (beginTime != null) {
    activityRecords.beginTime = beginTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    activityRecords.endTime = endTime;
  }
  final int? recordType = jsonConvert.convert<int>(json['recordType']);
  if (recordType != null) {
    activityRecords.recordType = recordType;
  }
  final String? bannerImage = jsonConvert.convert<String>(json['bannerImage']);
  if (bannerImage != null) {
    activityRecords.bannerImage = bannerImage;
  }
  final String? detailImage = jsonConvert.convert<String>(json['detailImage']);
  if (detailImage != null) {
    activityRecords.detailImage = detailImage;
  }
  final int? isTop = jsonConvert.convert<int>(json['isTop']);
  if (isTop != null) {
    activityRecords.isTop = isTop;
  }
  final int? activeStatus = jsonConvert.convert<int>(json['activeStatus']);
  if (activeStatus != null) {
    activityRecords.activeStatus = activeStatus;
  }
  final int? actTypeKey = jsonConvert.convert<int>(json['actTypeKey']);
  if (actTypeKey != null) {
    activityRecords.actTypeKey = actTypeKey;
  }
  return activityRecords;
}

Map<String, dynamic> $ActivityRecordsToJson(ActivityRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['activeName'] = entity.activeName;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['recordType'] = entity.recordType;
  data['bannerImage'] = entity.bannerImage;
  data['detailImage'] = entity.detailImage;
  data['isTop'] = entity.isTop;
  data['activeStatus'] = entity.activeStatus;
  data['actTypeKey'] = entity.actTypeKey;
  return data;
}

extension ActivityRecordsExtension on ActivityRecords {
  ActivityRecords copyWith({
    int? id,
    String? activeName,
    String? beginTime,
    String? endTime,
    int? recordType,
    String? bannerImage,
    String? detailImage,
    int? isTop,
    int? activeStatus,
    int? actTypeKey,
  }) {
    return ActivityRecords()
      ..id = id ?? this.id
      ..activeName = activeName ?? this.activeName
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..recordType = recordType ?? this.recordType
      ..bannerImage = bannerImage ?? this.bannerImage
      ..detailImage = detailImage ?? this.detailImage
      ..isTop = isTop ?? this.isTop
      ..activeStatus = activeStatus ?? this.activeStatus
      ..actTypeKey = actTypeKey ?? this.actTypeKey;
  }
}

ActivityTaskListEntity $ActivityTaskListEntityFromJson(
    Map<String, dynamic> json) {
  final ActivityTaskListEntity activityTaskListEntity = ActivityTaskListEntity();
  final List<ActivityTask>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ActivityTask>(e) as ActivityTask).toList();
  if (list != null) {
    activityTaskListEntity.list = list;
  }
  return activityTaskListEntity;
}

Map<String, dynamic> $ActivityTaskListEntityToJson(
    ActivityTaskListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension ActivityTaskListEntityExtension on ActivityTaskListEntity {
  ActivityTaskListEntity copyWith({
    List<ActivityTask>? list,
  }) {
    return ActivityTaskListEntity()
      ..list = list ?? this.list;
  }
}

ActivityTask $ActivityTaskFromJson(Map<String, dynamic> json) {
  final ActivityTask activityTask = ActivityTask();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    activityTask.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    activityTask.title = title;
  }
  final double? sumAmount = jsonConvert.convert<double>(json['sumAmount']);
  if (sumAmount != null) {
    activityTask.sumAmount = sumAmount;
  }
  final int? finishAmount = jsonConvert.convert<int>(json['finishAmount']);
  if (finishAmount != null) {
    activityTask.finishAmount = finishAmount;
  }
  final double? receiveAmount = jsonConvert.convert<double>(
      json['receiveAmount']);
  if (receiveAmount != null) {
    activityTask.receiveAmount = receiveAmount;
  }
  final String? beginTime = jsonConvert.convert<String>(json['beginTime']);
  if (beginTime != null) {
    activityTask.beginTime = beginTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    activityTask.endTime = endTime;
  }
  final int? receiveStatus = jsonConvert.convert<int>(json['receiveStatus']);
  if (receiveStatus != null) {
    activityTask.receiveStatus = receiveStatus;
  }
  final int? subReceiveType = jsonConvert.convert<int>(json['subReceiveType']);
  if (subReceiveType != null) {
    activityTask.subReceiveType = subReceiveType;
  }
  final int? subStatus = jsonConvert.convert<int>(json['subStatus']);
  if (subStatus != null) {
    activityTask.subStatus = subStatus;
  }
  final bool? isProcess = jsonConvert.convert<bool>(json['isProcess']);
  if (isProcess != null) {
    activityTask.isProcess = isProcess;
  }
  return activityTask;
}

Map<String, dynamic> $ActivityTaskToJson(ActivityTask entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['title'] = entity.title;
  data['sumAmount'] = entity.sumAmount;
  data['finishAmount'] = entity.finishAmount;
  data['receiveAmount'] = entity.receiveAmount;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveStatus'] = entity.receiveStatus;
  data['subReceiveType'] = entity.subReceiveType;
  data['subStatus'] = entity.subStatus;
  data['isProcess'] = entity.isProcess;
  return data;
}

extension ActivityTaskExtension on ActivityTask {
  ActivityTask copyWith({
    int? id,
    String? title,
    double? sumAmount,
    int? finishAmount,
    double? receiveAmount,
    String? beginTime,
    String? endTime,
    int? receiveStatus,
    int? subReceiveType,
    int? subStatus,
    bool? isProcess,
  }) {
    return ActivityTask()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..sumAmount = sumAmount ?? this.sumAmount
      ..finishAmount = finishAmount ?? this.finishAmount
      ..receiveAmount = receiveAmount ?? this.receiveAmount
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveStatus = receiveStatus ?? this.receiveStatus
      ..subReceiveType = subReceiveType ?? this.subReceiveType
      ..subStatus = subStatus ?? this.subStatus
      ..isProcess = isProcess ?? this.isProcess;
  }
}