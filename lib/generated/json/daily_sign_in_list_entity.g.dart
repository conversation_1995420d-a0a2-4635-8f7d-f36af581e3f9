import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/daily_sign_in_list_entity.dart';

DailySignInList $DailySignInListFromJson(Map<String, dynamic> json) {
  final DailySignInList dailySignInList = DailySignInList();
  final List<DailySignInEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<DailySignInEntity>(e) as DailySignInEntity)
      .toList();
  if (list != null) {
    dailySignInList.list = list;
  }
  return dailySignInList;
}

Map<String, dynamic> $DailySignInListToJson(DailySignInList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension DailySignInListExtension on DailySignInList {
  DailySignInList copyWith({
    List<DailySignInEntity>? list,
  }) {
    return DailySignInList()
      ..list = list ?? this.list;
  }
}

DailySignInEntity $DailySignInEntityFromJson(Map<String, dynamic> json) {
  final DailySignInEntity dailySignInEntity = DailySignInEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    dailySignInEntity.id = id;
  }
  final String? paramName = jsonConvert.convert<String>(json['paramName']);
  if (paramName != null) {
    dailySignInEntity.paramName = paramName;
  }
  final int? paramNum = jsonConvert.convert<int>(json['paramNum']);
  if (paramNum != null) {
    dailySignInEntity.paramNum = paramNum;
  }
  final int? auditMultiple = jsonConvert.convert<int>(json['auditMultiple']);
  if (auditMultiple != null) {
    dailySignInEntity.auditMultiple = auditMultiple;
  }
  final bool? isSign = jsonConvert.convert<bool>(json['isSign']);
  if (isSign != null) {
    dailySignInEntity.isSign = isSign;
  }
  final DateTime? dateTime = jsonConvert.convert<DateTime>(json['dateTime']);
  if (dateTime != null) {
    dailySignInEntity.dateTime = dateTime;
  }
  return dailySignInEntity;
}

Map<String, dynamic> $DailySignInEntityToJson(DailySignInEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['paramName'] = entity.paramName;
  data['paramNum'] = entity.paramNum;
  data['auditMultiple'] = entity.auditMultiple;
  data['isSign'] = entity.isSign;
  data['dateTime'] = entity.dateTime.toIso8601String();
  return data;
}

extension DailySignInEntityExtension on DailySignInEntity {
  DailySignInEntity copyWith({
    int? id,
    String? paramName,
    int? paramNum,
    int? auditMultiple,
    bool? isSign,
    DateTime? dateTime,
  }) {
    return DailySignInEntity()
      ..id = id ?? this.id
      ..paramName = paramName ?? this.paramName
      ..paramNum = paramNum ?? this.paramNum
      ..auditMultiple = auditMultiple ?? this.auditMultiple
      ..isSign = isSign ?? this.isSign
      ..dateTime = dateTime ?? this.dateTime;
  }
}