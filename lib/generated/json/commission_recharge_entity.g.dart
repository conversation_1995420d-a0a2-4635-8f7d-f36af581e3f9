import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/commission_recharge_entity.dart';

CommissionRechargeEntity $CommissionRechargeEntityFromJson(
    Map<String, dynamic> json) {
  final CommissionRechargeEntity commissionRechargeEntity = CommissionRechargeEntity();
  final List<CommissionRechargeList>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CommissionRechargeList>(e) as CommissionRechargeList)
      .toList();
  if (list != null) {
    commissionRechargeEntity.list = list;
  }
  return commissionRechargeEntity;
}

Map<String, dynamic> $CommissionRechargeEntityToJson(
    CommissionRechargeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CommissionRechargeEntityExtension on CommissionRechargeEntity {
  CommissionRechargeEntity copyWith({
    List<CommissionRechargeList>? list,
  }) {
    return CommissionRechargeEntity()
      ..list = list ?? this.list;
  }
}

CommissionRechargeList $CommissionRechargeListFromJson(
    Map<String, dynamic> json) {
  final CommissionRechargeList commissionRechargeList = CommissionRechargeList();
  final int? teamLevel = jsonConvert.convert<int>(json['teamLevel']);
  if (teamLevel != null) {
    commissionRechargeList.teamLevel = teamLevel;
  }
  final double? commissionRate = jsonConvert.convert<double>(
      json['commissionRate']);
  if (commissionRate != null) {
    commissionRechargeList.commissionRate = commissionRate;
  }
  return commissionRechargeList;
}

Map<String, dynamic> $CommissionRechargeListToJson(
    CommissionRechargeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamLevel'] = entity.teamLevel;
  data['commissionRate'] = entity.commissionRate;
  return data;
}

extension CommissionRechargeListExtension on CommissionRechargeList {
  CommissionRechargeList copyWith({
    int? teamLevel,
    double? commissionRate,
  }) {
    return CommissionRechargeList()
      ..teamLevel = teamLevel ?? this.teamLevel
      ..commissionRate = commissionRate ?? this.commissionRate;
  }
}