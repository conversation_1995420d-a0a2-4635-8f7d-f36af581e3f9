import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/app_version_entity.dart';

AppVersionEntity $AppVersionEntityFromJson(Map<String, dynamic> json) {
  final AppVersionEntity appVersionEntity = AppVersionEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    appVersionEntity.id = id;
  }
  final String? version = jsonConvert.convert<String>(json['version']);
  if (version != null) {
    appVersionEntity.version = version;
  }
  final bool? forceUpdate = jsonConvert.convert<bool>(json['forceUpdate']);
  if (forceUpdate != null) {
    appVersionEntity.forceUpdate = forceUpdate;
  }
  final String? releaseNotes = jsonConvert.convert<String>(
      json['releaseNotes']);
  if (releaseNotes != null) {
    appVersionEntity.releaseNotes = releaseNotes;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    appVersionEntity.url = url;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    appVersionEntity.createTime = createTime;
  }
  return appVersionEntity;
}

Map<String, dynamic> $AppVersionEntityToJson(AppVersionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['version'] = entity.version;
  data['forceUpdate'] = entity.forceUpdate;
  data['releaseNotes'] = entity.releaseNotes;
  data['url'] = entity.url;
  data['createTime'] = entity.createTime;
  return data;
}

extension AppVersionEntityExtension on AppVersionEntity {
  AppVersionEntity copyWith({
    int? id,
    String? version,
    bool? forceUpdate,
    String? releaseNotes,
    String? url,
    String? createTime,
  }) {
    return AppVersionEntity()
      ..id = id ?? this.id
      ..version = version ?? this.version
      ..forceUpdate = forceUpdate ?? this.forceUpdate
      ..releaseNotes = releaseNotes ?? this.releaseNotes
      ..url = url ?? this.url
      ..createTime = createTime ?? this.createTime;
  }
}