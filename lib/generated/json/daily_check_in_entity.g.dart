import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';

DailyCheckInEntity $DailyCheckInEntityFromJson(Map<String, dynamic> json) {
  final DailyCheckInEntity dailyCheckInEntity = DailyCheckInEntity();
  final int? totalDays = jsonConvert.convert<int>(json['totalDays']);
  if (totalDays != null) {
    dailyCheckInEntity.totalDays = totalDays;
  }
  final List<DailyCheckInItem>? shortList = (json['defaultView'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<DailyCheckInItem>(e) as DailyCheckInItem)
      .toList();
  if (shortList != null) {
    dailyCheckInEntity.shortList = shortList;
  }
  final List<DailyCheckInItem>? fullList = (json['allView'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<DailyCheckInItem>(e) as DailyCheckInItem)
      .toList();
  if (fullList != null) {
    dailyCheckInEntity.fullList = fullList;
  }
  return dailyCheckInEntity;
}

Map<String, dynamic> $DailyCheckInEntityToJson(DailyCheckInEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['totalDays'] = entity.totalDays;
  data['defaultView'] = entity.shortList.map((v) => v.toJson()).toList();
  data['allView'] = entity.fullList.map((v) => v.toJson()).toList();
  return data;
}

extension DailyCheckInEntityExtension on DailyCheckInEntity {
  DailyCheckInEntity copyWith({
    int? totalDays,
    List<DailyCheckInItem>? shortList,
    List<DailyCheckInItem>? fullList,
  }) {
    return DailyCheckInEntity()
      ..totalDays = totalDays ?? this.totalDays
      ..shortList = shortList ?? this.shortList
      ..fullList = fullList ?? this.fullList;
  }
}

DailyCheckInItem $DailyCheckInItemFromJson(Map<String, dynamic> json) {
  final DailyCheckInItem dailyCheckInItem = DailyCheckInItem();
  final String? dayName = jsonConvert.convert<String>(json['dayName']);
  if (dayName != null) {
    dailyCheckInItem.dayName = dayName;
  }
  final int? day = jsonConvert.convert<int>(json['day']);
  if (day != null) {
    dailyCheckInItem.day = day;
  }
  final double? signInAward = jsonConvert.convert<double>(json['signInAward']);
  if (signInAward != null) {
    dailyCheckInItem.signInAward = signInAward;
  }
  final double? reSignInAward = jsonConvert.convert<double>(
      json['reSignInAward']);
  if (reSignInAward != null) {
    dailyCheckInItem.reSignInAward = reSignInAward;
  }
  final double? signInRecharge = jsonConvert.convert<double>(
      json['signInRecharge']);
  if (signInRecharge != null) {
    dailyCheckInItem.signInRecharge = signInRecharge;
  }
  final double? resignRecharge = jsonConvert.convert<double>(
      json['resignRecharge']);
  if (resignRecharge != null) {
    dailyCheckInItem.resignRecharge = resignRecharge;
  }
  final int? signInState = jsonConvert.convert<int>(json['signInState']);
  if (signInState != null) {
    dailyCheckInItem.signInState = signInState;
  }
  final dynamic signInDate = json['signInDate'];
  if (signInDate != null) {
    dailyCheckInItem.signInDate = signInDate;
  }
  final bool? isFetching = jsonConvert.convert<bool>(json['isFetching']);
  if (isFetching != null) {
    dailyCheckInItem.isFetching = isFetching;
  }
  return dailyCheckInItem;
}

Map<String, dynamic> $DailyCheckInItemToJson(DailyCheckInItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dayName'] = entity.dayName;
  data['day'] = entity.day;
  data['signInAward'] = entity.signInAward;
  data['reSignInAward'] = entity.reSignInAward;
  data['signInRecharge'] = entity.signInRecharge;
  data['resignRecharge'] = entity.resignRecharge;
  data['signInState'] = entity.signInState;
  data['signInDate'] = entity.signInDate;
  data['isFetching'] = entity.isFetching;
  return data;
}

extension DailyCheckInItemExtension on DailyCheckInItem {
  DailyCheckInItem copyWith({
    String? dayName,
    int? day,
    double? signInAward,
    double? reSignInAward,
    double? signInRecharge,
    double? resignRecharge,
    int? signInState,
    dynamic signInDate,
    bool? isFetching,
  }) {
    return DailyCheckInItem()
      ..dayName = dayName ?? this.dayName
      ..day = day ?? this.day
      ..signInAward = signInAward ?? this.signInAward
      ..reSignInAward = reSignInAward ?? this.reSignInAward
      ..signInRecharge = signInRecharge ?? this.signInRecharge
      ..resignRecharge = resignRecharge ?? this.resignRecharge
      ..signInState = signInState ?? this.signInState
      ..signInDate = signInDate ?? this.signInDate
      ..isFetching = isFetching ?? this.isFetching;
  }
}