import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';

TopUpRecordEntity $TopUpRecordEntityFromJson(Map<String, dynamic> json) {
  final TopUpRecordEntity topUpRecordEntity = TopUpRecordEntity();
  final List<TopUpRecord>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TopUpRecord>(e) as TopUpRecord).toList();
  if (records != null) {
    topUpRecordEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    topUpRecordEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    topUpRecordEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    topUpRecordEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    topUpRecordEntity.pages = pages;
  }
  return topUpRecordEntity;
}

Map<String, dynamic> $TopUpRecordEntityToJson(TopUpRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension TopUpRecordEntityExtension on TopUpRecordEntity {
  TopUpRecordEntity copyWith({
    List<TopUpRecord>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return TopUpRecordEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

TopUpRecord $TopUpRecordFromJson(Map<String, dynamic> json) {
  final TopUpRecord topUpRecord = TopUpRecord();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    topUpRecord.id = id;
  }
  final String? transactionNo = jsonConvert.convert<String>(
      json['transactionNo']);
  if (transactionNo != null) {
    topUpRecord.transactionNo = transactionNo;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    topUpRecord.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    topUpRecord.userNo = userNo;
  }
  final String? depositor = jsonConvert.convert<String>(json['depositor']);
  if (depositor != null) {
    topUpRecord.depositor = depositor;
  }
  final double? orderInitialAmount = jsonConvert.convert<double>(
      json['orderInitialAmount']);
  if (orderInitialAmount != null) {
    topUpRecord.orderInitialAmount = orderInitialAmount;
  }
  final double? orderAmount = jsonConvert.convert<double>(json['orderAmount']);
  if (orderAmount != null) {
    topUpRecord.orderAmount = orderAmount;
  }
  final double? realAmount = jsonConvert.convert<double>(json['realAmount']);
  if (realAmount != null) {
    topUpRecord.realAmount = realAmount;
  }
  final double? reduceAmount = jsonConvert.convert<double>(
      json['reduceAmount']);
  if (reduceAmount != null) {
    topUpRecord.reduceAmount = reduceAmount;
  }
  final double? reduceRate = jsonConvert.convert<double>(json['reduceRate']);
  if (reduceRate != null) {
    topUpRecord.reduceRate = reduceRate;
  }
  final double? reduceMaxLimit = jsonConvert.convert<double>(
      json['reduceMaxLimit']);
  if (reduceMaxLimit != null) {
    topUpRecord.reduceMaxLimit = reduceMaxLimit;
  }
  final double? finalAmount = jsonConvert.convert<double>(json['finalAmount']);
  if (finalAmount != null) {
    topUpRecord.finalAmount = finalAmount;
  }
  final double? serviceChargeRate = jsonConvert.convert<double>(
      json['serviceChargeRate']);
  if (serviceChargeRate != null) {
    topUpRecord.serviceChargeRate = serviceChargeRate;
  }
  final double? serviceCharge = jsonConvert.convert<double>(
      json['serviceCharge']);
  if (serviceCharge != null) {
    topUpRecord.serviceCharge = serviceCharge;
  }
  final double? serviceChargeMaxLimit = jsonConvert.convert<double>(
      json['serviceChargeMaxLimit']);
  if (serviceChargeMaxLimit != null) {
    topUpRecord.serviceChargeMaxLimit = serviceChargeMaxLimit;
  }
  final String? cashinWayCode = jsonConvert.convert<String>(
      json['cashinWayCode']);
  if (cashinWayCode != null) {
    topUpRecord.cashinWayCode = cashinWayCode;
  }
  final String? cashinWayName = jsonConvert.convert<String>(
      json['cashinWayName']);
  if (cashinWayName != null) {
    topUpRecord.cashinWayName = cashinWayName;
  }
  final String? cashinTypeCode = jsonConvert.convert<String>(
      json['cashinTypeCode']);
  if (cashinTypeCode != null) {
    topUpRecord.cashinTypeCode = cashinTypeCode;
  }
  final String? cashinTypeName = jsonConvert.convert<String>(
      json['cashinTypeName']);
  if (cashinTypeName != null) {
    topUpRecord.cashinTypeName = cashinTypeName;
  }
  final String? cashinChannel = jsonConvert.convert<String>(
      json['cashinChannel']);
  if (cashinChannel != null) {
    topUpRecord.cashinChannel = cashinChannel;
  }
  final String? cardNo = jsonConvert.convert<String>(json['cardNo']);
  if (cardNo != null) {
    topUpRecord.cardNo = cardNo;
  }
  final String? cardRealName = jsonConvert.convert<String>(
      json['cardRealName']);
  if (cardRealName != null) {
    topUpRecord.cardRealName = cardRealName;
  }
  final String? requestTime = jsonConvert.convert<String>(json['requestTime']);
  if (requestTime != null) {
    topUpRecord.requestTime = requestTime;
  }
  final int? orderStatus = jsonConvert.convert<int>(json['orderStatus']);
  if (orderStatus != null) {
    topUpRecord.orderStatus = orderStatus;
  }
  final double? integral = jsonConvert.convert<double>(json['integral']);
  if (integral != null) {
    topUpRecord.integral = integral;
  }
  final int? isTodayFirst = jsonConvert.convert<int>(json['isTodayFirst']);
  if (isTodayFirst != null) {
    topUpRecord.isTodayFirst = isTodayFirst;
  }
  final String? operateTime = jsonConvert.convert<String>(json['operateTime']);
  if (operateTime != null) {
    topUpRecord.operateTime = operateTime;
  }
  final String? operator = jsonConvert.convert<String>(json['operator']);
  if (operator != null) {
    topUpRecord.operator = operator;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    topUpRecord.remark = remark;
  }
  final String? serialNumber = jsonConvert.convert<String>(
      json['serialNumber']);
  if (serialNumber != null) {
    topUpRecord.serialNumber = serialNumber;
  }
  final double? exchangeRate = jsonConvert.convert<double>(
      json['exchangeRate']);
  if (exchangeRate != null) {
    topUpRecord.exchangeRate = exchangeRate;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    topUpRecord.currency = currency;
  }
  return topUpRecord;
}

Map<String, dynamic> $TopUpRecordToJson(TopUpRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['transactionNo'] = entity.transactionNo;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['depositor'] = entity.depositor;
  data['orderInitialAmount'] = entity.orderInitialAmount;
  data['orderAmount'] = entity.orderAmount;
  data['realAmount'] = entity.realAmount;
  data['reduceAmount'] = entity.reduceAmount;
  data['reduceRate'] = entity.reduceRate;
  data['reduceMaxLimit'] = entity.reduceMaxLimit;
  data['finalAmount'] = entity.finalAmount;
  data['serviceChargeRate'] = entity.serviceChargeRate;
  data['serviceCharge'] = entity.serviceCharge;
  data['serviceChargeMaxLimit'] = entity.serviceChargeMaxLimit;
  data['cashinWayCode'] = entity.cashinWayCode;
  data['cashinWayName'] = entity.cashinWayName;
  data['cashinTypeCode'] = entity.cashinTypeCode;
  data['cashinTypeName'] = entity.cashinTypeName;
  data['cashinChannel'] = entity.cashinChannel;
  data['cardNo'] = entity.cardNo;
  data['cardRealName'] = entity.cardRealName;
  data['requestTime'] = entity.requestTime;
  data['orderStatus'] = entity.orderStatus;
  data['integral'] = entity.integral;
  data['isTodayFirst'] = entity.isTodayFirst;
  data['operateTime'] = entity.operateTime;
  data['operator'] = entity.operator;
  data['remark'] = entity.remark;
  data['serialNumber'] = entity.serialNumber;
  data['exchangeRate'] = entity.exchangeRate;
  data['currency'] = entity.currency;
  return data;
}

extension TopUpRecordExtension on TopUpRecord {
  TopUpRecord copyWith({
    int? id,
    String? transactionNo,
    int? userId,
    String? userNo,
    String? depositor,
    double? orderInitialAmount,
    double? orderAmount,
    double? realAmount,
    double? reduceAmount,
    double? reduceRate,
    double? reduceMaxLimit,
    double? finalAmount,
    double? serviceChargeRate,
    double? serviceCharge,
    double? serviceChargeMaxLimit,
    String? cashinWayCode,
    String? cashinWayName,
    String? cashinTypeCode,
    String? cashinTypeName,
    String? cashinChannel,
    String? cardNo,
    String? cardRealName,
    String? requestTime,
    int? orderStatus,
    double? integral,
    int? isTodayFirst,
    String? operateTime,
    String? operator,
    String? remark,
    String? serialNumber,
    double? exchangeRate,
    String? currency,
  }) {
    return TopUpRecord()
      ..id = id ?? this.id
      ..transactionNo = transactionNo ?? this.transactionNo
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..depositor = depositor ?? this.depositor
      ..orderInitialAmount = orderInitialAmount ?? this.orderInitialAmount
      ..orderAmount = orderAmount ?? this.orderAmount
      ..realAmount = realAmount ?? this.realAmount
      ..reduceAmount = reduceAmount ?? this.reduceAmount
      ..reduceRate = reduceRate ?? this.reduceRate
      ..reduceMaxLimit = reduceMaxLimit ?? this.reduceMaxLimit
      ..finalAmount = finalAmount ?? this.finalAmount
      ..serviceChargeRate = serviceChargeRate ?? this.serviceChargeRate
      ..serviceCharge = serviceCharge ?? this.serviceCharge
      ..serviceChargeMaxLimit = serviceChargeMaxLimit ??
          this.serviceChargeMaxLimit
      ..cashinWayCode = cashinWayCode ?? this.cashinWayCode
      ..cashinWayName = cashinWayName ?? this.cashinWayName
      ..cashinTypeCode = cashinTypeCode ?? this.cashinTypeCode
      ..cashinTypeName = cashinTypeName ?? this.cashinTypeName
      ..cashinChannel = cashinChannel ?? this.cashinChannel
      ..cardNo = cardNo ?? this.cardNo
      ..cardRealName = cardRealName ?? this.cardRealName
      ..requestTime = requestTime ?? this.requestTime
      ..orderStatus = orderStatus ?? this.orderStatus
      ..integral = integral ?? this.integral
      ..isTodayFirst = isTodayFirst ?? this.isTodayFirst
      ..operateTime = operateTime ?? this.operateTime
      ..operator = operator ?? this.operator
      ..remark = remark ?? this.remark
      ..serialNumber = serialNumber ?? this.serialNumber
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..currency = currency ?? this.currency;
  }
}

TransactFilterWayList $TransactFilterWayListFromJson(
    Map<String, dynamic> json) {
  final TransactFilterWayList transactFilterWayList = TransactFilterWayList();
  final List<TransactFilterWay>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<TransactFilterWay>(e) as TransactFilterWay)
      .toList();
  if (list != null) {
    transactFilterWayList.list = list;
  }
  return transactFilterWayList;
}

Map<String, dynamic> $TransactFilterWayListToJson(
    TransactFilterWayList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension TransactFilterWayListExtension on TransactFilterWayList {
  TransactFilterWayList copyWith({
    List<TransactFilterWay>? list,
  }) {
    return TransactFilterWayList()
      ..list = list ?? this.list;
  }
}

TransactFilterWay $TransactFilterWayFromJson(Map<String, dynamic> json) {
  final TransactFilterWay transactFilterWay = TransactFilterWay();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    transactFilterWay.id = id;
  }
  final String? wayCode = jsonConvert.convert<String>(json['wayCode']);
  if (wayCode != null) {
    transactFilterWay.wayCode = wayCode;
  }
  final String? wayName = jsonConvert.convert<String>(json['wayName']);
  if (wayName != null) {
    transactFilterWay.wayName = wayName;
  }
  final bool? isSel = jsonConvert.convert<bool>(json['isSel']);
  if (isSel != null) {
    transactFilterWay.isSel = isSel;
  }
  return transactFilterWay;
}

Map<String, dynamic> $TransactFilterWayToJson(TransactFilterWay entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['wayCode'] = entity.wayCode;
  data['wayName'] = entity.wayName;
  data['isSel'] = entity.isSel;
  return data;
}

extension TransactFilterWayExtension on TransactFilterWay {
  TransactFilterWay copyWith({
    int? id,
    String? wayCode,
    String? wayName,
    bool? isSel,
  }) {
    return TransactFilterWay()
      ..id = id ?? this.id
      ..wayCode = wayCode ?? this.wayCode
      ..wayName = wayName ?? this.wayName
      ..isSel = isSel ?? this.isSel;
  }
}

TransactFilterTypeList $TransactFilterTypeListFromJson(
    Map<String, dynamic> json) {
  final TransactFilterTypeList transactFilterTypeList = TransactFilterTypeList();
  final List<TransactFilterType>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<TransactFilterType>(e) as TransactFilterType)
      .toList();
  if (list != null) {
    transactFilterTypeList.list = list;
  }
  return transactFilterTypeList;
}

Map<String, dynamic> $TransactFilterTypeListToJson(
    TransactFilterTypeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension TransactFilterTypeListExtension on TransactFilterTypeList {
  TransactFilterTypeList copyWith({
    List<TransactFilterType>? list,
  }) {
    return TransactFilterTypeList()
      ..list = list ?? this.list;
  }
}

TransactFilterType $TransactFilterTypeFromJson(Map<String, dynamic> json) {
  final TransactFilterType transactFilterType = TransactFilterType();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    transactFilterType.id = id;
  }
  final String? typeCode = jsonConvert.convert<String>(json['typeCode']);
  if (typeCode != null) {
    transactFilterType.typeCode = typeCode;
  }
  final String? typeName = jsonConvert.convert<String>(json['typeName']);
  if (typeName != null) {
    transactFilterType.typeName = typeName;
  }
  final String? wayCode = jsonConvert.convert<String>(json['wayCode']);
  if (wayCode != null) {
    transactFilterType.wayCode = wayCode;
  }
  final bool? isSel = jsonConvert.convert<bool>(json['isSel']);
  if (isSel != null) {
    transactFilterType.isSel = isSel;
  }
  return transactFilterType;
}

Map<String, dynamic> $TransactFilterTypeToJson(TransactFilterType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['typeCode'] = entity.typeCode;
  data['typeName'] = entity.typeName;
  data['wayCode'] = entity.wayCode;
  data['isSel'] = entity.isSel;
  return data;
}

extension TransactFilterTypeExtension on TransactFilterType {
  TransactFilterType copyWith({
    int? id,
    String? typeCode,
    String? typeName,
    String? wayCode,
    bool? isSel,
  }) {
    return TransactFilterType()
      ..id = id ?? this.id
      ..typeCode = typeCode ?? this.typeCode
      ..typeName = typeName ?? this.typeName
      ..wayCode = wayCode ?? this.wayCode
      ..isSel = isSel ?? this.isSel;
  }
}