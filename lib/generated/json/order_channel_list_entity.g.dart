import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/order_channel_list_entity.dart';

OrderChannelListEntity $OrderChannelListEntityFromJson(
    Map<String, dynamic> json) {
  final OrderChannelListEntity orderChannelListEntity = OrderChannelListEntity();
  final OrderChannelListPage? page = jsonConvert.convert<OrderChannelListPage>(
      json['page']);
  if (page != null) {
    orderChannelListEntity.page = page;
  }
  final double? totalBetAmount = jsonConvert.convert<double>(
      json['totalBetAmount']);
  if (totalBetAmount != null) {
    orderChannelListEntity.totalBetAmount = totalBetAmount;
  }
  final double? totalWinToday = jsonConvert.convert<double>(
      json['totalWinToday']);
  if (totalWinToday != null) {
    orderChannelListEntity.totalWinToday = totalWinToday;
  }
  final double? totalSendAmount = jsonConvert.convert<double>(
      json['totalSendAmount']);
  if (totalSendAmount != null) {
    orderChannelListEntity.totalSendAmount = totalSendAmount;
  }
  final double? totalRevenue = jsonConvert.convert<double>(
      json['totalRevenue']);
  if (totalRevenue != null) {
    orderChannelListEntity.totalRevenue = totalRevenue;
  }
  return orderChannelListEntity;
}

Map<String, dynamic> $OrderChannelListEntityToJson(
    OrderChannelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page.toJson();
  data['totalBetAmount'] = entity.totalBetAmount;
  data['totalWinToday'] = entity.totalWinToday;
  data['totalSendAmount'] = entity.totalSendAmount;
  data['totalRevenue'] = entity.totalRevenue;
  return data;
}

extension OrderChannelListEntityExtension on OrderChannelListEntity {
  OrderChannelListEntity copyWith({
    OrderChannelListPage? page,
    double? totalBetAmount,
    double? totalWinToday,
    double? totalSendAmount,
    double? totalRevenue,
  }) {
    return OrderChannelListEntity()
      ..page = page ?? this.page
      ..totalBetAmount = totalBetAmount ?? this.totalBetAmount
      ..totalWinToday = totalWinToday ?? this.totalWinToday
      ..totalSendAmount = totalSendAmount ?? this.totalSendAmount
      ..totalRevenue = totalRevenue ?? this.totalRevenue;
  }
}

OrderChannelListPage $OrderChannelListPageFromJson(Map<String, dynamic> json) {
  final OrderChannelListPage orderChannelListPage = OrderChannelListPage();
  final List<OrderChannelListPageRecords>? records = (json['records'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<OrderChannelListPageRecords>(
          e) as OrderChannelListPageRecords).toList();
  if (records != null) {
    orderChannelListPage.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    orderChannelListPage.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    orderChannelListPage.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    orderChannelListPage.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    orderChannelListPage.pages = pages;
  }
  return orderChannelListPage;
}

Map<String, dynamic> $OrderChannelListPageToJson(OrderChannelListPage entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension OrderChannelListPageExtension on OrderChannelListPage {
  OrderChannelListPage copyWith({
    List<OrderChannelListPageRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return OrderChannelListPage()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

OrderChannelListPageRecords $OrderChannelListPageRecordsFromJson(
    Map<String, dynamic> json) {
  final OrderChannelListPageRecords orderChannelListPageRecords = OrderChannelListPageRecords();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    orderChannelListPageRecords.id = id;
  }
  final String? channelUniqueId = jsonConvert.convert<String>(
      json['channelUniqueId']);
  if (channelUniqueId != null) {
    orderChannelListPageRecords.channelUniqueId = channelUniqueId;
  }
  final int? thirdPlatformId = jsonConvert.convert<int>(
      json['thirdPlatformId']);
  if (thirdPlatformId != null) {
    orderChannelListPageRecords.thirdPlatformId = thirdPlatformId;
  }
  final String? thirdPlatformName = jsonConvert.convert<String>(
      json['thirdPlatformName']);
  if (thirdPlatformName != null) {
    orderChannelListPageRecords.thirdPlatformName = thirdPlatformName;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    orderChannelListPageRecords.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    orderChannelListPageRecords.userNo = userNo;
  }
  final int? agentId = jsonConvert.convert<int>(json['agentId']);
  if (agentId != null) {
    orderChannelListPageRecords.agentId = agentId;
  }
  final int? generalAgentId = jsonConvert.convert<int>(json['generalAgentId']);
  if (generalAgentId != null) {
    orderChannelListPageRecords.generalAgentId = generalAgentId;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    orderChannelListPageRecords.gameClassCode = gameClassCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    orderChannelListPageRecords.gameClassName = gameClassName;
  }
  final String? categoryCode = jsonConvert.convert<String>(
      json['categoryCode']);
  if (categoryCode != null) {
    orderChannelListPageRecords.categoryCode = categoryCode;
  }
  final String? categoryName = jsonConvert.convert<String>(
      json['categoryName']);
  if (categoryName != null) {
    orderChannelListPageRecords.categoryName = categoryName;
  }
  final String? gameCode = jsonConvert.convert<String>(json['gameCode']);
  if (gameCode != null) {
    orderChannelListPageRecords.gameCode = gameCode;
  }
  final String? channelAccount = jsonConvert.convert<String>(
      json['channelAccount']);
  if (channelAccount != null) {
    orderChannelListPageRecords.channelAccount = channelAccount;
  }
  final String? startTime = jsonConvert.convert<String>(json['startTime']);
  if (startTime != null) {
    orderChannelListPageRecords.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    orderChannelListPageRecords.endTime = endTime;
  }
  final String? gameName = jsonConvert.convert<String>(json['gameName']);
  if (gameName != null) {
    orderChannelListPageRecords.gameName = gameName;
  }
  final String? roomName = jsonConvert.convert<String>(json['roomName']);
  if (roomName != null) {
    orderChannelListPageRecords.roomName = roomName;
  }
  final dynamic tableName = json['tableName'];
  if (tableName != null) {
    orderChannelListPageRecords.tableName = tableName;
  }
  final double? sendAmount = jsonConvert.convert<double>(json['sendAmount']);
  if (sendAmount != null) {
    orderChannelListPageRecords.sendAmount = sendAmount;
  }
  final double? winAmount = jsonConvert.convert<double>(json['winAmount']);
  if (winAmount != null) {
    orderChannelListPageRecords.winAmount = winAmount;
  }
  final double? betAmount = jsonConvert.convert<double>(json['betAmount']);
  if (betAmount != null) {
    orderChannelListPageRecords.betAmount = betAmount;
  }
  final double? revenue = jsonConvert.convert<double>(json['revenue']);
  if (revenue != null) {
    orderChannelListPageRecords.revenue = revenue;
  }
  final String? matchNumber = jsonConvert.convert<String>(json['matchNumber']);
  if (matchNumber != null) {
    orderChannelListPageRecords.matchNumber = matchNumber;
  }
  final double? returnRate = jsonConvert.convert<double>(json['returnRate']);
  if (returnRate != null) {
    orderChannelListPageRecords.returnRate = returnRate;
  }
  final double? returnAmount = jsonConvert.convert<double>(
      json['returnAmount']);
  if (returnAmount != null) {
    orderChannelListPageRecords.returnAmount = returnAmount;
  }
  final int? gameResult = jsonConvert.convert<int>(json['gameResult']);
  if (gameResult != null) {
    orderChannelListPageRecords.gameResult = gameResult;
  }
  final dynamic recordOriginal = json['recordOriginal'];
  if (recordOriginal != null) {
    orderChannelListPageRecords.recordOriginal = recordOriginal;
  }
  final String? pullTime = jsonConvert.convert<String>(json['pullTime']);
  if (pullTime != null) {
    orderChannelListPageRecords.pullTime = pullTime;
  }
  return orderChannelListPageRecords;
}

Map<String, dynamic> $OrderChannelListPageRecordsToJson(
    OrderChannelListPageRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['channelUniqueId'] = entity.channelUniqueId;
  data['thirdPlatformId'] = entity.thirdPlatformId;
  data['thirdPlatformName'] = entity.thirdPlatformName;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['agentId'] = entity.agentId;
  data['generalAgentId'] = entity.generalAgentId;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameClassName'] = entity.gameClassName;
  data['categoryCode'] = entity.categoryCode;
  data['categoryName'] = entity.categoryName;
  data['gameCode'] = entity.gameCode;
  data['channelAccount'] = entity.channelAccount;
  data['startTime'] = entity.startTime;
  data['endTime'] = entity.endTime;
  data['gameName'] = entity.gameName;
  data['roomName'] = entity.roomName;
  data['tableName'] = entity.tableName;
  data['sendAmount'] = entity.sendAmount;
  data['winAmount'] = entity.winAmount;
  data['betAmount'] = entity.betAmount;
  data['revenue'] = entity.revenue;
  data['matchNumber'] = entity.matchNumber;
  data['returnRate'] = entity.returnRate;
  data['returnAmount'] = entity.returnAmount;
  data['gameResult'] = entity.gameResult;
  data['recordOriginal'] = entity.recordOriginal;
  data['pullTime'] = entity.pullTime;
  return data;
}

extension OrderChannelListPageRecordsExtension on OrderChannelListPageRecords {
  OrderChannelListPageRecords copyWith({
    int? id,
    String? channelUniqueId,
    int? thirdPlatformId,
    String? thirdPlatformName,
    int? userId,
    String? userNo,
    int? agentId,
    int? generalAgentId,
    String? gameClassCode,
    String? gameClassName,
    String? categoryCode,
    String? categoryName,
    String? gameCode,
    String? channelAccount,
    String? startTime,
    String? endTime,
    String? gameName,
    String? roomName,
    dynamic tableName,
    double? sendAmount,
    double? winAmount,
    double? betAmount,
    double? revenue,
    String? matchNumber,
    double? returnRate,
    double? returnAmount,
    int? gameResult,
    dynamic recordOriginal,
    String? pullTime,
  }) {
    return OrderChannelListPageRecords()
      ..id = id ?? this.id
      ..channelUniqueId = channelUniqueId ?? this.channelUniqueId
      ..thirdPlatformId = thirdPlatformId ?? this.thirdPlatformId
      ..thirdPlatformName = thirdPlatformName ?? this.thirdPlatformName
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..agentId = agentId ?? this.agentId
      ..generalAgentId = generalAgentId ?? this.generalAgentId
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..categoryCode = categoryCode ?? this.categoryCode
      ..categoryName = categoryName ?? this.categoryName
      ..gameCode = gameCode ?? this.gameCode
      ..channelAccount = channelAccount ?? this.channelAccount
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..gameName = gameName ?? this.gameName
      ..roomName = roomName ?? this.roomName
      ..tableName = tableName ?? this.tableName
      ..sendAmount = sendAmount ?? this.sendAmount
      ..winAmount = winAmount ?? this.winAmount
      ..betAmount = betAmount ?? this.betAmount
      ..revenue = revenue ?? this.revenue
      ..matchNumber = matchNumber ?? this.matchNumber
      ..returnRate = returnRate ?? this.returnRate
      ..returnAmount = returnAmount ?? this.returnAmount
      ..gameResult = gameResult ?? this.gameResult
      ..recordOriginal = recordOriginal ?? this.recordOriginal
      ..pullTime = pullTime ?? this.pullTime;
  }
}