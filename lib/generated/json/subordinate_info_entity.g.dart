import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/subordinate_info_entity.dart';

SubordinateInfoEntity $SubordinateInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SubordinateInfoEntity subordinateInfoEntity = SubordinateInfoEntity();
  final int? subordinateCount = jsonConvert.convert<int>(
      json['subordinateCount']);
  if (subordinateCount != null) {
    subordinateInfoEntity.subordinateCount = subordinateCount;
  }
  final double? commissionEarned = jsonConvert.convert<double>(
      json['commissionEarned']);
  if (commissionEarned != null) {
    subordinateInfoEntity.commissionEarned = commissionEarned;
  }
  return subordinateInfoEntity;
}

Map<String, dynamic> $SubordinateInfoEntityToJson(
    SubordinateInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subordinateCount'] = entity.subordinateCount;
  data['commissionEarned'] = entity.commissionEarned;
  return data;
}

extension SubordinateInfoEntityExtension on SubordinateInfoEntity {
  SubordinateInfoEntity copyWith({
    int? subordinateCount,
    double? commissionEarned,
  }) {
    return SubordinateInfoEntity()
      ..subordinateCount = subordinateCount ?? this.subordinateCount
      ..commissionEarned = commissionEarned ?? this.commissionEarned;
  }
}