import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';

UserVipEntity $UserVipEntityFromJson(Map<String, dynamic> json) {
  final UserVipEntity userVipEntity = UserVipEntity();
  final String? vipTitle = jsonConvert.convert<String>(json['vipTitle']);
  if (vipTitle != null) {
    userVipEntity.vipTitle = vipTitle;
  }
  final int? vipLevel = jsonConvert.convert<int>(json['vipLevel']);
  if (vipLevel != null) {
    userVipEntity.vipLevel = vipLevel;
  }
  final double? integral = jsonConvert.convert<double>(json['integral']);
  if (integral != null) {
    userVipEntity.integral = integral;
  }
  final int? nextLevelIntegral = jsonConvert.convert<int>(
      json['nextLevelIntegral']);
  if (nextLevelIntegral != null) {
    userVipEntity.nextLevelIntegral = nextLevelIntegral;
  }
  return userVipEntity;
}

Map<String, dynamic> $UserVipEntityToJson(UserVipEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['vipTitle'] = entity.vipTitle;
  data['vipLevel'] = entity.vipLevel;
  data['integral'] = entity.integral;
  data['nextLevelIntegral'] = entity.nextLevelIntegral;
  return data;
}

extension UserVipEntityExtension on UserVipEntity {
  UserVipEntity copyWith({
    String? vipTitle,
    int? vipLevel,
    double? integral,
    int? nextLevelIntegral,
  }) {
    return UserVipEntity()
      ..vipTitle = vipTitle ?? this.vipTitle
      ..vipLevel = vipLevel ?? this.vipLevel
      ..integral = integral ?? this.integral
      ..nextLevelIntegral = nextLevelIntegral ?? this.nextLevelIntegral;
  }
}