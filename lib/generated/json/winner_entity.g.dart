import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/winner_entity.dart';

WinnerEntityList $WinnerEntityListFromJson(Map<String, dynamic> json) {
  final WinnerEntityList winnerEntityList = WinnerEntityList();
  final List<WinnerEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WinnerEntity>(e) as WinnerEntity).toList();
  if (list != null) {
    winnerEntityList.list = list;
  }
  return winnerEntityList;
}

Map<String, dynamic> $WinnerEntityListToJson(WinnerEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension WinnerEntityListExtension on WinnerEntityList {
  WinnerEntityList copyWith({
    List<WinnerEntity>? list,
  }) {
    return WinnerEntityList()
      ..list = list ?? this.list;
  }
}

WinnerEntity $WinnerEntityFromJson(Map<String, dynamic> json) {
  final WinnerEntity winnerEntity = WinnerEntity();
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  if (nickName != null) {
    winnerEntity.nickName = nickName;
  }
  final String? sendAmount = jsonConvert.convert<String>(json['sendAmount']);
  if (sendAmount != null) {
    winnerEntity.sendAmount = sendAmount;
  }
  final String? gameName = jsonConvert.convert<String>(json['gameName']);
  if (gameName != null) {
    winnerEntity.gameName = gameName;
  }
  final String? gameId = jsonConvert.convert<String>(json['gameId']);
  if (gameId != null) {
    winnerEntity.gameId = gameId;
  }
  final String? thirdPlatformId = jsonConvert.convert<String>(
      json['thirdPlatformId']);
  if (thirdPlatformId != null) {
    winnerEntity.thirdPlatformId = thirdPlatformId;
  }
  return winnerEntity;
}

Map<String, dynamic> $WinnerEntityToJson(WinnerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['nickName'] = entity.nickName;
  data['sendAmount'] = entity.sendAmount;
  data['gameName'] = entity.gameName;
  data['gameId'] = entity.gameId;
  data['thirdPlatformId'] = entity.thirdPlatformId;
  return data;
}

extension WinnerEntityExtension on WinnerEntity {
  WinnerEntity copyWith({
    String? nickName,
    String? sendAmount,
    String? gameName,
    String? gameId,
    String? thirdPlatformId,
  }) {
    return WinnerEntity()
      ..nickName = nickName ?? this.nickName
      ..sendAmount = sendAmount ?? this.sendAmount
      ..gameName = gameName ?? this.gameName
      ..gameId = gameId ?? this.gameId
      ..thirdPlatformId = thirdPlatformId ?? this.thirdPlatformId;
  }
}