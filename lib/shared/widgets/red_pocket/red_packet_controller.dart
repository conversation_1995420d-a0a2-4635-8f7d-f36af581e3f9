import 'dart:async';

import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/chat.dart';


class RedPacketController {
  final TickerProviderStateMixin tickerProvider;
  Listenable? repaint;

  Path? goldPath;

  // 控制红包摇晃的动画控制器
  late AnimationController angleController;
  // 控制红包消失时的平移动画控制器
  late AnimationController translateController;
  // 控制红包整体缩放的动画控制器
  late AnimationController scaleController;
  late Animation<double> translateCtrl;
  late Animation<Color?> colorCtrl;
  late Animation<double> angleCtrl;
  bool isAdd = false;
  bool showOpenText = true;
  bool showOpenBtn = true;

  Function? onFinish;
  Function(double? amount)? onOpen;

  RedPacketController({required this.tickerProvider}) {
    initAnimation();
  }

  void initAnimation() {
    // 红包摇晃角度的动画控制器
    angleController = AnimationController(duration: const Duration(milliseconds: 300), vsync: tickerProvider);
    // 控制红包消失时的平移动画控制器
    translateController = AnimationController(duration: const Duration(milliseconds: 800), vsync: tickerProvider);
    // 控制红包整体缩放的动画控制器
    scaleController = AnimationController(duration: const Duration(milliseconds: 500), vsync: tickerProvider)
      ..forward();

    // 红包摇晃角度的动画值，从1.0到0.0
    angleCtrl = angleController.drive(Tween(begin: 1.0, end: 0.0));
    // 红包消失时的位移动画值，从0.0到1.0
    translateCtrl = translateController.drive(Tween(begin: 0.0, end: 1.0));
    // 红包颜色渐变动画，从红色到透明
    colorCtrl = translateController.drive(ColorTween(begin: Colors.redAccent, end: const Color(0x00FF5252)));

    // 当平移动画完成时触发结束回调
    translateController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        onFinish?.call();
      }
    });
    // 合并角度和平移动画的监听器，用于触发重绘
    repaint = Listenable.merge([angleController, translateController]);
  }

  void stop(double? amount) async {
    if (angleController.isAnimating) {
      if (angleController.status == AnimationStatus.forward) {
        await angleController.forward();
        angleController.reverse();
      } else if (angleController.status == AnimationStatus.reverse) {
        angleController.reverse();
      }

      tickerProvider.setState(() {
        showOpenBtn = false;
      });
      translateController.forward();
      onOpen?.call(amount);
    }
  }

  void clickGold(TapUpDetails details, redEnvelopeId, seq) async {
    if (checkClickGold(details.globalPosition)) {
      if (angleController.isAnimating) {
        stop(null);
      } else {
        angleController.repeat(reverse: true);
        tickerProvider.setState(() {
          showOpenText = false;
        });

        double? amount = await ChatApi.grabRedEnvelope(
          redEnvelopeId: redEnvelopeId,
          seq: seq,
        );

        await Future.delayed(const Duration(seconds: 1));
        stop(amount);
      }
    }
  }

  bool checkClickGold(Offset point) {
    return goldPath?.contains(point) == true;
  }

  void handleClick(Offset point) async {
    if (checkClickGold(point)) {
      return;
    }
    await scaleController.reverse();
    onFinish?.call();
  }

  void dispose() {
    angleController.dispose();
    translateController.dispose();
  }
}
