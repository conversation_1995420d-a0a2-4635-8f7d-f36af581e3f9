

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';

class GameSearchTextField extends StatelessWidget {
  final String hintText;
  final GestureTapCallback onTapSearch;
  final ValueChanged<String>? onChanged;

  const GameSearchTextField({super.key,
    required this.hintText,
    required this.onTapSearch,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return CommonTextField(
      onChanged: onChanged,
      maxHeight: 42.gw,
      radius: 21.gw,
      hintText: hintText,
      prefixIconPadding: EdgeInsets.symmetric(horizontal: 15.gw),
      suffixIconPadding: EdgeInsets.only(right: 4.gw),
      prefixIcon: Image.asset("assets/images/tiktok/icon_textField_search.png", width: 14.gw, height: 14.gw),
      suffixIcon: CommonButton(title: 'search'.tr(), radius: 17.gw, width: 73.gw, height: 33.gw, onPressed: onTapSearch,),
    );
  }

}
