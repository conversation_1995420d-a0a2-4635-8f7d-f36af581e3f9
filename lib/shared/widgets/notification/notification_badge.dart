import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/4_mine/notifications/notification_cubit.dart';
import 'package:wd/features/page/4_mine/notifications/notification_state.dart';

class NotificationBadge extends StatelessWidget {
  const NotificationBadge({super.key, this.child});

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<NotificationsCubit, NotificationState, int?>(
      selector: (state) => state.unreadCount,
      builder: (context, unreadCount) {
        if (unreadCount == null || unreadCount == 0) return child ?? const SizedBox.shrink();
        final text = unreadCount > 99 ? '99+' : unreadCount.toString();
        if (child != null) {
          return Stack(
            alignment: Alignment.topLeft,
            clipBehavior: Clip.none,
            children: [
              child!,
              Positioned(
                left: -5,
                top: -5,
                child: buildBadge(context, text),
              ),
            ],
          );
        }
        return buildBadge(context, text);
      },
    );
  }

  Widget buildBadge(BuildContext context, String text) {
    return Container(
      decoration: BoxDecoration(
        color: context.colorTheme.tabItemBgA,
        shape: BoxShape.circle,
        border: Border.all(color: context.colorTheme.borderE, width: 1),
      ),
      padding: const EdgeInsets.all(3),
      child: Text(
        text,
        style: TextStyle(color: context.colorTheme.btnBgPrimary, fontSize: 10.gw),
      ),
    );
  }
}
