import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';

class TransactTagWidget extends StatelessWidget {
  final String title;

  const TransactTagWidget({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (StringUtil.isEmpty(title.trim())) {
      return const SizedBox.shrink();
    }
    return Container(
        height: 21.gw,
        padding: EdgeInsets.symmetric(horizontal: 8.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.tabItemBgA,
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Center(
          child: Text(
            title,
            style: context.textTheme.primary,
          ),
        ));
  }
}
