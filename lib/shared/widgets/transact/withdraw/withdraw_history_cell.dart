import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_card.dart';

class WithdrawHistoryCell extends StatelessWidget {
  final WithdrawRecord model;

  const WithdrawHistoryCell({super.key, required this.model});

  static Widget buildOrderStatusWidget(BuildContext context, {required int orderStatus}) {
    // orderStatus 订单状态（0：待审核，1：已通过，2：已取消, 3: 已拒绝）
    String title;
    Color backgroundColor;
    Color textColor;

    switch (orderStatus) {
      case 0:
        title = "withdraw_status_pending".tr();
        backgroundColor = context.colorTheme.textHighlight.withOpacity(0.11);
        textColor = context.colorTheme.textHighlight;
        break;
      case 1:
        title = "withdraw_status_approved".tr();
        backgroundColor = const Color(0xff38ED14).withOpacity(0.11);
        textColor = const Color(0xff38ED14);
        break;
      case 2:
        title = "withdraw_status_cancelled".tr();
        backgroundColor = const Color(0xffec8c89).withOpacity(0.11);
        textColor = const Color(0xffec8c89);
        break;
      case 3:
        title = "withdraw_status_rejected".tr();
        backgroundColor = const Color(0xffdc4a3a).withOpacity(0.11);
        textColor = const Color(0xffdc4a3a);
        break;
      default:
        title = "withdraw_status_pending".tr();
        backgroundColor = context.colorTheme.textHighlight.withOpacity(0.11);
        textColor = context.colorTheme.textHighlight;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.gw),
      ),
      child: Text(
        title,
        style: context.textTheme.regular.fs12.w500.copyWith(color: textColor),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String title, String content, {bool showBorder = true}) {
    return Container(
      decoration: BoxDecoration(
        border: showBorder
            ? Border(
                bottom: BorderSide(
                  color: context.colorTheme.borderA,
                  width: 1,
                ),
              )
            : null,
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: context.textTheme.regular.fs13.w500,
          ),
          Flexible(
            child: Text(
              content,
              style: context.textTheme.regular.fs13.w500,
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderRow(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "withdraw_order_no".tr(),
            style: context.textTheme.regular.fs13.w500,
          ),
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    model.transactionNo,
                    style: context.textTheme.regular.fs13.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.gw),
                GestureDetector(
                  onTap: () {
                    ClipboardTool.setData(model.transactionNo);
                  },
                  child: Icon(
                    Icons.copy,
                    color: context.colorTheme.textRegular,
                    size: 16.gw,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CommonCard(
      margin: 8.gw,
      radius: 12.gw,
      padding: 0,
      color: context.colorTheme.highlightForeground,
      child: Column(
        children: [
          // Header section
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.colorTheme.borderA,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.gw),
                topRight: Radius.circular(12.gw),
              ),
            ),
            padding: EdgeInsets.all(16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "withdraw".tr(),
                  style: context.textTheme.secondary.fs20.w500.ffAne,
                ),
                buildOrderStatusWidget(context, orderStatus: model.orderStatus),
              ],
            ),
          ),
          // Information rows
          _buildInfoRow(context, "withdraw_requested_amount".tr(), model.orderAmount.toString()),
          if (model.orderInitialAmount != model.finalAmount && model.orderStatus == 1)
            _buildInfoRow(context, "withdraw_credited_amount".tr(), model.finalAmount.toString()),
          _buildInfoRow(context, "act_event_type".tr(), "${model.cashoutWayName}-${model.cashoutTypeName}"),
          _buildInfoRow(context, "time".tr(), model.requestTime),
          _buildOrderRow(context),
        ],
      ),
    );
  }
}
