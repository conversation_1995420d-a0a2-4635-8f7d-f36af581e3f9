
import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import '../blur_container.dart';

class TabBarPromotion extends StatelessWidget {
  final TabController _tabController;
  final List<Widget> tabs;
  final List<Widget> children;

  const TabBarPromotion({super.key, required TabController tabController, required this.tabs, required this.children})
      : _tabController = tabController;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            TabBar(
              controller: _tabController,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 3.gw,
                ),
              ),
              dividerColor: const Color(0xffCACDDB),
              labelStyle: TextStyle(
                fontSize: 16.fs,
                color: Theme.of(context).primaryColor,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 16.fs,
                color: const Color(0xff3B4165),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorColor: Theme.of(context).primaryColor,
              labelPadding: EdgeInsets.symmetric(horizontal: 10.gw),
              indicatorPadding: EdgeInsets.symmetric(horizontal: 50.gw, vertical: 10.gw),
              indicatorWeight: 3.gw,
              tabs: tabs,
            ),
          ],
        ),
        SizedBox(height: 10.gw),
        Expanded(
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }
}

class TabHeader extends StatelessWidget {
  final List<Widget> children;
  final double? width, borderRadius;

  const TabHeader({super.key, required this.children, this.width, this.borderRadius});

  @override
  Widget build(BuildContext context) {
    return BlurContainer(
      width: double.infinity,
      height: 35.gw,
      borderRadius: BorderRadius.circular(borderRadius ?? 8.gw),
      borderColor: Colors.white,
      borderWidth: 1,
      gradientColors: const [
        Color(0xFFECEFF6),
        Color(0xFFFEFEFF),
      ],
      boxShadow: const BoxShadow(
        color: Color(0x1A000000), // 阴影颜色，带透明度
        offset: Offset(0, 2), // 阴影偏移
        blurRadius: 2, // 模糊半径
      ),
      child: Row(
        // mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }
}

class TableHeaderCell extends StatelessWidget {
  final String text;
  final int flex;
  final bool isBold;
  final TextStyle? textStyle;
  final bool showRightBorder;

  const TableHeaderCell({
    super.key,
    required this.text,
    required this.flex,
    this.isBold = false,
    this.textStyle,
    this.showRightBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex,
      child: Container(
        decoration: showRightBorder
            ? const BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Color(0xFFC3B7AE),
                    width: 0.7,
                  ),
                ),
              )
            : null,
        child: Center(
          child: Text(
            text.isEmpty ? "-----" : text,
            textAlign: TextAlign.center,
            style: textStyle?.copyWith(
                  fontWeight: isBold ? FontWeight.bold : null,
                ) ??
                TextStyle(
                  fontWeight: isBold ? FontWeight.bold : null,
                ),
          ),
        ),
      ),
    );
  }
}
