import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class PlatformMenuCell extends StatelessWidget {
  final String title;
  final String iconUrl;
  final VoidCallback onTap;

  const PlatformMenuCell({
    super.key,
    required this.title,
    required this.iconUrl,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(color: context.colorTheme.foregroundColor, borderRadius: BorderRadius.circular(8.gw)),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Positioned(
              left: 6,
              right: 6,
              top: 6,
              bottom: 25,
              child: AspectRatio(
                aspectRatio: 97 / 82,
                child: Container(
                  decoration: BoxDecoration(
                    color: context.colorTheme.borderC,
                    borderRadius: BorderRadius.circular(4.gw),
                  ),
                  padding: EdgeInsets.all(10.gw),
                  child: AppImage(
                    imageUrl: iconUrl,
                    placeholder: Image.asset("assets/images/home/<USER>"),
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            ),
            SizedBox(height: 9.gw),
            Positioned.fill(
                child: Image.asset(
              "assets/images/home/<USER>",
              fit: BoxFit.fill,
            )),
            Positioned(
              bottom: 2,
              left: 6,
              right: 6,
              child: AneText(
                title,
                maxLines: 1,
                style: context.textTheme.primary,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            )
          ],
        ),
      ),
    );
  }
}
