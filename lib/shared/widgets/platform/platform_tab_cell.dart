import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class PlatformTabCellViewModel {
  final String title;
  final bool isHot;
  final String activeIcon;

  PlatformTabCellViewModel({
    required this.title,
    this.isHot = false,
    required this.activeIcon,
  });

  factory PlatformTabCellViewModel.fromGameTypeV2(GameTypeV2 gameType) {
    return PlatformTabCellViewModel(
      title: gameType.name,
      isHot: gameType.isHot,
      // activeIcon: gameType.classIcon,
      activeIcon: "assets/images/home/<USER>/home_tab_kind_${_getIconPathWithName(gameType.name)}.png",
    );
  }

  factory PlatformTabCellViewModel.fromActivityGameTypeViewModel(ActivityGameTypeViewModel model) {
    return PlatformTabCellViewModel(
      title: model.categoryName,
      activeIcon: model.icon,
    );
  }
  factory PlatformTabCellViewModel.fromActivityTaskTypeViewModel(ActivityTaskTypeViewModel model) {
    return PlatformTabCellViewModel(
      title: model.categoryName,
      activeIcon: model.icon,
    );
  }

  static String _getIconPathWithName(String name) {
    switch (name) {
      case "热门":
      case "Popular":
        return "hot";
      case "捕鱼":
      case "Fishing":
        return "by";
      case "斗鸡":
      case "CF":
        return "chicken";
      case "彩票":
      case "CP":
      case "Lottery":
      case "永利彩票":
      case "金沙彩票":
        return "cp";
      case "永利棋牌":
      case "金沙棋牌":
        return "ylqp";
      case "电子":
      case "Slot":
      case "Slots":
        return "dz";
      case "电竞":
      case "DJ":
        return "game";
      case "棋牌":
      case "Cards":
      case "Card":
      case "Chess":
        return "qp";
      case "体育":
      case "Sport":
      case "Sports":
        return "sport";
      case "视讯":
      case "真人":
      case "Living":
      case "Live":
        return "zr";
    }
    return "all";
  }
}

class PlatformTabCell extends StatelessWidget {
  final PlatformTabCellViewModel model;
  final bool isSel;

  const PlatformTabCell({
    super.key,
    required this.model,
    required this.isSel,
  });

  @override
  Widget build(BuildContext context) {
    String bgImagePath = "assets/images/home/<USER>/bg_home_platform_${isSel ? '' : 'un'}sel.png";

    return SizedBox(
      width: 60.gw,
      height: 69.gw,
      child: Stack(
        children: [
          /// 背景图
          Positioned(
            bottom: 0,
            child: Image.asset(bgImagePath, width: 60.gw, height: 60.gw),
          ),
          AppImage(
            imageUrl: model.activeIcon,
            width: 60.gw,
            height: 64.gw,
          ),

          Positioned(
              bottom: 2.gw,
              left: 0,
              right: 0,
              child: Center(
                  child: AneText(
                model.title,
                style: context.textTheme.secondary.copyWith(
                  color: isSel ? context.theme.primaryColor : null,
                ),
              ))),
          if (model.isHot)
            Positioned(
                top: 0,
                right: 1.gw,
                child: Image.asset(
                  "assets/images/home/<USER>/icon_home_tab_kind_isHot.png",
                  width: 14.gw,
                  height: 18.gw,
                ))
        ],
      ),
    );
  }
}
