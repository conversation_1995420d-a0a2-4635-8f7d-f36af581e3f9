import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/image_cache_manager.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

class PlatformListCellViewModel {
  final String title;
  final String category;
  final BoxFit imageFit;
  final double width;
  final double height;
  final bool isClosed;

  /// 场馆logo
  final String logoUrl;

  /// 封面大图
  final String cgUrl;

  const PlatformListCellViewModel({
    required this.title,
    required this.logoUrl,
    required this.cgUrl,
    required this.category,
    required this.width,
    required this.height,
    this.imageFit = BoxFit.contain,
    required this.isClosed,
  });

  factory PlatformListCellViewModel.fromAny({
    GameTypeV2? gameType,
    required GamePlatformV2 platform,
    double? width,
    double? height,
    BoxFit? imageFit,
  }) {
    final fit = imageFit ?? BoxFit.contain;


    // GameTypeV2 + GamePlatformV2
    if (gameType is GameTypeV2) {
      return PlatformListCellViewModel(
        title: platform.name,
        logoUrl: platform.logoUrl,
        cgUrl: GlobalConfig().systemConfig.gamePicBaseUrl + platform.mainImgUrl,
        imageFit: fit,
        category: gameType.code,
        width: width ?? double.infinity,
        height: height ?? double.infinity,
        isClosed: platform.isAegis == 1,
      );
    }

    // 单独 GamePlatform（无 gameType）
    if (gameType == null) {
      return PlatformListCellViewModel(
        title: platform.name,
        logoUrl: getPlatformLogoUrl(
          platform.code,
          platformName: platform.type == 1 ? platform.logoUrl : null,
        ),
        cgUrl: platform.mainImgUrl,
        imageFit: fit,
        category: platform.code,
        width: double.infinity,
        height: double.infinity,
        isClosed: false,
      );
    }

    throw ArgumentError("Invalid combination of gameType and platform");
  }
}

class PlatformListCell extends StatelessWidget {
  final PlatformListCellViewModel model;
  final VoidCallback onTap;

  const PlatformListCell({
    super.key,
    required this.model,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: model.width,
        height: model.height,
        decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage("assets/images/home/<USER>"),
              fit: BoxFit.fill,
            )),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            /// 场馆大图
            Positioned(
              left: 4,
              right: 4,
              top: 4,
              child: AspectRatio(
                aspectRatio: 1,
                child: AppImage(
                  imageUrl: model.cgUrl,
                  radius: 6.gw,
                  fit: model.imageFit,
                  cacheManager: !kIsWeb ? ImageCacheManager() : null,
                ),
              ),
            ),


            Positioned(
              bottom: 2,
              left: 0,
              right: 0,
              child: Container(
                  alignment: Alignment.center,
                  height: 29.gw,
                  padding: EdgeInsets.fromLTRB(10.gw, 4.gw, 10.gw, 0),
                  child: Text(
                    model.title,
                    maxLines: 1,
                    style:context.textTheme.title,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  )),
            ),

            /// 场馆logo
            Positioned(
                right: 4,
                top: 4,
                child: Container(
                  width: 45.gw,
                  height: 48.3.gw,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("assets/images/home/<USER>"),
                        fit: BoxFit.fill,
                      )),
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(bottom: 8.gw),
                  child: AppImage(
                    imageUrl: model.logoUrl,
                    placeholder: const SizedBox.shrink(),
                    fit: model.imageFit,
                    width: 30.gw,
                    height: 30.gw,
                    cacheManager: ImageCacheManager(),
                  ),
                )),

            if (model.isClosed)
              Container(
                width: model.width,
                height: model.height - 5.gw,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(10.gw),
                ),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 2.gw),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(3.gw),
                    ),
                    child: Text(
                      "暂未开放",
                      style: TextStyle(color: Colors.white.withOpacity(0.5), fontSize: 14.fs),
                    ),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
