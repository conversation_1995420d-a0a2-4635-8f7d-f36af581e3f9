import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';

class TextRow extends StatelessWidget {
  final String? title;
  final TextStyle? titleStyle;
  final String? content;
  final TextStyle? contentStyle;
  final double contentGap;
  final Widget? leftWidget;
  final Widget? rightWidget;
  final double? height;
  final bool showSeparator;
  final EdgeInsetsGeometry padding;

  const TextRow({
    super.key,
    this.title,
    this.titleStyle,
    this.leftWidget,
    this.content,
    this.contentStyle,
    this.contentGap = 10,
    this.rightWidget,
    this.height,
    this.showSeparator = false,
    this.padding = const EdgeInsets.only(top: 10),
  });

  @override
  Widget build(BuildContext context) {
    final border = showSeparator
        ? const Border(
            bottom: BorderSide(
              color: Color(0xFFF0F0F0), // #F0F0F0
              width: 0.5, // 0.5px 下划线
            ),
          )
        : null;

    return Container(
      decoration: BoxDecoration(border: border),
      height: height,
      padding: padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (leftWidget != null)
            leftWidget!
          else
            Text(
              title ?? '',
              style: titleStyle ?? Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 15.fs),
            ),
          SizedBox(width: contentGap),
          if (rightWidget != null)
            rightWidget!
          else
            Expanded(
              child: Text(
                content ?? '',
                textAlign: TextAlign.end,
                style: contentStyle ?? Theme.of(context).textTheme.titleMedium?.copyWith(),
              ),
            ),
        ],
      ),
    );
  }
}
