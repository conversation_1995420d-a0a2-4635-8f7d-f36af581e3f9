import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';

class CommonSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const CommonSwitch({super.key, required this.value, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: 0.6,
      child: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: context.theme.primaryColor,
        inactiveThumbColor: context.colorTheme.textSecondary,
        inactiveTrackColor: context.colorTheme.textSecondary.withOpacity(0.3),
      ),
    );
  }
}
