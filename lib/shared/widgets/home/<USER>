import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class HomeBalanceInfoWidget extends StatelessWidget {
  final String balance;
  final GestureTapCallback onTap;
  final AnimationController controller;
  final Color? color;

  const HomeBalanceInfoWidget({
    super.key,
    required this.balance,
    required this.onTap,
    required this.controller,
    this.color = const Color(0xff6A7391),
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "¥$balance",
            style: context.textTheme.primary.fs16.w600,
          ),
          SizedBox(width: 4.gw),
          RotationTransition(
            turns: Tween<double>(begin: 0, end: 1).chain(CurveTween(curve: Curves.easeInOutCubic)).animate(controller),
            child: Image.asset(
              "assets/images/common/icon_refresh.png",
              width: 16,
              height: 16,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
