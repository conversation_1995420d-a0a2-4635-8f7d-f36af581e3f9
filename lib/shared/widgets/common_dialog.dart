import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

class CommonDialog {
  static show({
    required BuildContext context,
    String? title,
    String? content,
    VoidCallback? complete,
    String? cancelBtnTitle,
    String? sureBtnTitle,
    bool showCancelBtn = true,
  }) async {
    return await showDialog(
      context: context,
      builder: (context) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: PointerInterceptor(
              child: Container(
                width: 300.gw,
                padding: EdgeInsets.fromLTRB(15.gw, 17.gw, 15.gw, 10.gw),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: context.colorTheme.foregroundColor,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (title != null) Text(title, style: context.textTheme.secondary.fs16.w500),
                    if (content != null)
                      Padding(
                        padding: EdgeInsets.only(top: title != null ? 20.gw : 0),
                        child: Text(
                          content,
                          style: context.textTheme.regular,
                        ),
                      ),
                    SizedBox(height: 30.gw),
                    Divider(height: 1, color: Theme.of(context).dividerColor),
                    SizedBox(height: 10.gw),
                    Row(
                      children: [
                        if (showCancelBtn)
                          Expanded(
                            child: CommonButton(
                              height: 45.gw,
                              style: CommonButtonStyle.tertiary,
                              title: cancelBtnTitle ?? "cancel".tr(),
                              fontSize: 14.gw,
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                Navigator.of(context).pop();
                              },
                            ),
                          ),
                        if (showCancelBtn) SizedBox(width: 10.gw),
                        Expanded(
                          child: CommonButton(
                            height: 45.gw,
                            title: sureBtnTitle ?? "ok".tr(),
                            fontSize: 14.gw,
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              Navigator.of(context).pop();
                              if (complete != null) {
                                complete();
                              }
                            },
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
