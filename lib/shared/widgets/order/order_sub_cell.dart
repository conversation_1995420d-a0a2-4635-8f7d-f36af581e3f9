import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/order_channel_list_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class GSOrderSubViewModel {
  String title;
  String id;
  String dateStr;
  String orderNo; // 游戏返水金额
  double betAmount; // 下单金额
  double winAmount; // 盈亏金额

  GSOrderSubViewModel({
    required this.title,
    required this.id,
    required this.dateStr,
    required this.orderNo,
    required this.betAmount,
    required this.winAmount,
  });

  factory GSOrderSubViewModel.formOrderChannelListPageRecord(OrderChannelListPageRecords model) {
    return GSOrderSubViewModel(
        title: model.gameName,
        id: model.thirdPlatformId.toString(),
        dateStr: model.startTime,
        orderNo: model.channelUniqueId,
        betAmount: model.betAmount,
        winAmount: model.winAmount);
  }
}

class GSOrderSubListCell extends StatelessWidget {
  final VoidCallback? onPressed;
  GSOrderSubViewModel model;

  GSOrderSubListCell({super.key, required this.model, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
      decoration: BoxDecoration(
        color: const Color(0xFF101010),
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        children: [
          // Header section with game name and date
          _buildHeaderSection(context),
          // Information rows
          _buildInfoSection(context),
          // Result section
          _buildResultSection(context),
        ],
      ),
    );
  }

  /// Build header section with game name and date badge
  Widget _buildHeaderSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF212121),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.gw),
          topRight: Radius.circular(12.gw),
        ),
      ),
      padding: EdgeInsets.all(16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            model.title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.gw,
              fontWeight: FontWeight.w500,
              fontFamily: 'AnekDevanagari',
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: const Color(0xFF161616),
              borderRadius: BorderRadius.circular(6.gw),
            ),
            child: Text(
              model.dateStr,
              style: TextStyle(
                color: const Color(0xFFB4B3B3),
                fontSize: 14.gw,
                fontFamily: 'AnekDevanagari',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build information section with venue ID, order no, and bet amount
  Widget _buildInfoSection(BuildContext context) {
    return Column(
      children: [
        _buildInfoRow(
          context: context,
          label: 'venue_id'.tr(),
          value: model.id,
        ),
        _buildInfoRow(
          context: context,
          label: 'order_no'.tr(),
          value: model.orderNo,
        ),
        _buildInfoRow(
          context: context,
          label: 'bet_amount'.tr(),
          value: model.betAmount.toString(),
        ),
      ],
    );
  }

  /// Build individual info row
  Widget _buildInfoRow({
    required BuildContext context,
    required String label,
    required String value,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF212121),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AneText(
            label,
            style: context.textTheme.title,
          ),
          AneText(
            value,
            textAlign: TextAlign.right,
            style: context.textTheme.title,
          ),
        ],
      ),
    );
  }

  /// Build result section with status badge and amount
  Widget _buildResultSection(BuildContext context) {
    final isLoss = model.winAmount < 0;

    return Container(
      padding: EdgeInsets.all(16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AneText(
            'result'.tr(),
            style: context.textTheme.title,
          ),
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 2.gw),
                decoration: BoxDecoration(
                  color: isLoss ? const Color(0xFFF44E56).withOpacity(0.1) : const Color(0xFF67AC5C).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.gw),
                ),
                child: AneText(
                  isLoss ? 'loss'.tr() : 'win'.tr(),
                  style: context.textTheme.title
                      .copyWith(color: isLoss ? const Color(0xFFF44E56) : const Color(0xFF67AC5C)),
                ),
              ),
              SizedBox(width: 12.gw),
              AneText(
                model.winAmount.formattedMoney,
                style: context.textTheme.title.copyWith(
                  color: isLoss ? const Color(0xFFF44E56) : const Color(0xFF67AC5C),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
