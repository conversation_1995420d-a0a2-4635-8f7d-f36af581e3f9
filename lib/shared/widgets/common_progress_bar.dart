import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

class CommonProgressBar extends StatelessWidget {
  final int total;
  final int current;
  const CommonProgressBar({
    super.key,
    required this.total,
    required this.current,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        border: Border.all(
          color: context.colorTheme.btnBorderTertiary,
          width: 0.5.gw,
        ),
      ),
      height: 12.gw,
      width: 23.84.gw * total,
      child: ListView.builder(
        itemCount: total,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          final isFirst = index == 0;
          final isLast = index == total - 1;
          final isNotCompleted = index >= current;
          return ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(isFirst ? 4 : 0),
              topRight: Radius.circular(isLast ? 4 : 0),
              bottomLeft: Radius.circular(isFirst ? 4 : 0),
              bottomRight: Radius.circular(isLast ? 4 : 0),
            ),
            child: AppImage(
              imageUrl:
                  isNotCompleted ? 'assets/images/mine/vip/vip_level_bg.png' : 'assets/images/mine/vip/vip_level.png',
              width: 23.84.gw,
            ),
          );
        },
      ),
    );
  }
}
