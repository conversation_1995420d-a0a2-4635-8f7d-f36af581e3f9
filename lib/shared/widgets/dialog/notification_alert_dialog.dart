
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:wd/core/models/entities/jump_model.dart';
import 'package:wd/core/models/entities/notification_alert_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../features/page/main/screens/main_screen_cubit.dart';

bool _isShowingNotificationAlert = false;

class NotificationAlertDialog {
  List<NotificationAlertEntity> notifications;

  NotificationAlertDialog({required this.notifications});

  show() async {
    if (_isShowingNotificationAlert) return;
    _isShowingNotificationAlert = true;
    try {
      await showDialog(
        context: sl<NavigatorService>().navigatorKey.currentState!.context,
        barrierDismissible: false,
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (BuildContext context) {
          return _NotificationAlertDialogContent(notifications: notifications);
        },
      );
    } finally {
      _isShowingNotificationAlert = false;
    }
  }
}

class _NotificationAlertDialogContent extends StatefulWidget {
  final List<NotificationAlertEntity> notifications;

  const _NotificationAlertDialogContent({required this.notifications});

  @override
  _NotificationAlertDialogContentState createState() => _NotificationAlertDialogContentState();
}

class _NotificationAlertDialogContentState extends State<_NotificationAlertDialogContent> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 360.gw,
        height: 612.gw,
        padding: EdgeInsets.only(top: 238.gw),
        decoration: const BoxDecoration(
          color: Colors.transparent,
          image: DecorationImage(
            image: AssetImage('assets/images/alert/bg_notice.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          children: [
            AneText("announcement".tr(), style: context.textTheme.primary.fs24.w600),
            SizedBox(height: 12.gw),
            _buildContentSection(), // 构建内容区域
            SizedBox(height: 20.gw),
            _buildButtonSection(), // 构建底部按钮区域
          ],
        ),
      ),
    );
  }

  /// 构建主要内容区域，包含左侧标签列表和右侧内容
  Widget _buildContentSection() {
    return SizedBox(
      height: 249.gw,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTabList(), // 左侧标签列表
          const Spacer(),
          _buildContentArea(),// 右侧内容区域
          SizedBox(width: 12.gw,)
        ],
      ),
    );
  }

  /// 构建左侧标签列表
  Widget _buildTabList() {
    return SizedBox(
      width: 150.gw,
      child: ListView.separated(
        shrinkWrap: true,
        itemBuilder: _buildTabItem, // 构建单个标签项
        separatorBuilder: (_, __) => SizedBox(height: 17.gw), // 标签之间的间距
        itemCount: widget.notifications.length,
      ),
    );
  }

  /// 构建单个标签项
  Widget _buildTabItem(BuildContext context, int index) {
    final title = widget.notifications[index].noticeTitle;
    final isSel = _currentIndex == index;
    final textColor = isSel ? Colors.white : const Color(0xffAB7435);

    return InkWell(
      onTap: () => setState(() => _currentIndex = index), // 点击切换标签
      child: Container(
        width: 150.gw,
        height: 37.gw,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.horizontal(right: Radius.circular(20.gw)),
          color: isSel ? context.theme.primaryColor : context.colorTheme.foregroundColor,
          border:  isSel ? Border.all(width: 1, color: context.theme.primaryColorLight ) : null,
        ),
        child: AneText(
          title,
          style: context.textTheme.title.copyWith(color: isSel ? context.theme.bottomNavigationBarTheme.backgroundColor : null,),
        ),
      ),
    );
  }

  /// 构建右侧内容区域
  Widget _buildContentArea() {
    return SizedBox(
      width: 186.gw,
      height: 246.gw,
      child: noticeContent(), // 根据内容类型显示不同内容
    );
  }

  /// 构建底部按钮区域
  Widget _buildButtonSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Row(
        children: [
          _buildNeverShowButton(), // "不再显示"按钮
          const Spacer(),
          _buildCloseButton(), // "关闭"按钮
        ],
      ),
    );
  }

  /// 构建"不再显示"按钮
  Widget _buildNeverShowButton() {
    return CommonButton(
      title: "do_not_show_again".tr(),
      style: CommonButtonStyle.secondary,
      width: 162.gw,
      height: 42.gw,
      onPressed: _handleNeverShow,
    );
  }

  /// 构建"关闭"按钮
  Widget _buildCloseButton() {

    return CommonButton(
      title: "do_close".tr(),
      width: 162.gw,
      height: 42.gw,
      onPressed:() {
        sl<NavigatorService>().pop(); // 关闭弹窗
        context.read<MainScreenCubit>().onChangeShowNoticeDialog(false); // 更新状态
      }
    );
  }

  /// 处理"不再显示"按钮点击事件
  /// 保存当前时间戳，标记通知在24小时内不再显示
  Future<void> _handleNeverShow() async {
    sl<NavigatorService>().pop();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
      'notification_suppressed_until',
      DateTime.now().add(const Duration(hours: 24)).millisecondsSinceEpoch,
    );
  }

  Widget noticeContent() {
    final noticeContent = widget.notifications[_currentIndex].noticeContent;
    if (noticeContent.isHtmlString) {
      return _buildHtmlContent(noticeContent);
    }
    return _buildImageContent(noticeContent);
  }

  void _handleUserTapContent() {
    // sl<NavigatorService>().pop();
    SystemUtil.onJump(JumpModel.fromNotificationAlertEntity(widget.notifications[_currentIndex]));
  }

  Widget _buildImageContent(String imageUrl) {
    return InkWell(
      onTap: _handleUserTapContent,
      child: AppImage(
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        imageUrl: imageUrl,
        radius: 5,
        placeholder: Center(
          child: SizedBox(
            width: 15.gw,
            height: 15.gw,
            child: const CircularProgressIndicator(),
          ),
        ),
      ),
    );
  }

  Widget _buildHtmlContent(String content) {
    final scrollController = ScrollController();
    return Theme(
      data: Theme.of(context).copyWith(
        scrollbarTheme: ScrollbarThemeData(
          thumbColor: WidgetStateProperty.all(_NotificationAlertDialogConstants.scrollbarColor
              .withOpacity(_NotificationAlertDialogConstants.scrollbarOpacity)),
          minThumbLength: _NotificationAlertDialogConstants.scrollbarMinThumbLength.gw,
        ),
      ),
      child: Stack(
        children: [
          _buildScrollableContent(scrollController, content),
          if (widget.notifications[_currentIndex].jumpStatus != 14) _buildDetailButton(),
        ],
      ),
    );
  }

  Widget _buildScrollableContent(ScrollController controller, String content) {
    return Container(
      clipBehavior: Clip.hardEdge,
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_NotificationAlertDialogConstants.scrollbarRadius),
      ),
      child: RawScrollbar(
        controller: controller,
        thumbVisibility: true,
        trackVisibility: true,
        thickness: _NotificationAlertDialogConstants.scrollbarThickness.gw,
        radius: const Radius.circular(_NotificationAlertDialogConstants.scrollbarRadius),
        interactive: true,
        child: SingleChildScrollView(
          padding: EdgeInsets.zero,
          controller: controller,
          child: Html(
            data: content,
            style: {
              "body": Style(
                padding: HtmlPaddings.zero,
                margin: Margins.zero,
              ),
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDetailButton() {
    return Positioned(
      bottom: 0.gw,
      right: 0.gw,
      child: InkWell(
        onTap: _handleUserTapContent,
        child: Container(
          // height: 28.gw,
          padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: _NotificationAlertDialogConstants.buttonGradientColors,
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(_NotificationAlertDialogConstants.scrollbarRadius),
              bottomRight: Radius.circular(_NotificationAlertDialogConstants.scrollbarRadius),
            ),
            border: Border.all(
              color: _NotificationAlertDialogConstants.buttonBorderColor,
              width: 0.2,
            ),
          ),
          child: const Center(
            child: Text(
              '查看详情',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _NotificationAlertDialogConstants {
  static const Color scrollbarColor = Color(0xffa1a1a1);
  static const double scrollbarOpacity = 0.7;
  static const double scrollbarThickness = 4.0;
  static const double scrollbarRadius = 8.0;
  static const double scrollbarMinThumbLength = 10.0;

  static const Color buttonBorderColor = Color(0xFFFCD3AD);
  static const List<Color> buttonGradientColors = [
    Color(0xFFB8997B),
    Color(0xFFD8C6B4),
  ];
}

class CustomPageIndicator extends StatelessWidget {
  final int currentPage;
  final int pageCount;

  const CustomPageIndicator({
    super.key,
    required this.currentPage,
    required this.pageCount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(pageCount, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 2.0),
          height: 8.0,
          width: currentPage == index ? 12.0 : 8.0,
          decoration: BoxDecoration(
            color: currentPage == index ? Theme.of(context).colorScheme.primary : Colors.white.withOpacity(0.8),
            shape: BoxShape.circle,
          ),
        );
      }),
    );
  }
}
