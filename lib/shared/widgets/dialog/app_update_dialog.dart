import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:wd/core/models/entities/app_version_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../core/base/base_will_pop.dart';
import 'app_update_help_dialog.dart';

class AppUpdateDialog {
  final AppVersionEntity model;
  OverlayEntry? _overlayEntry;

  AppUpdateDialog({required this.model});

  void show() {
    final context = sl<NavigatorService>().navigatorKey.currentContext!;
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => Material(
        color: Colors.black.withOpacity(0.4),
        child: _AppUpdateDialogContent(
          model: model,
          onDismiss: () {
            _overlayEntry?.remove();
            _overlayEntry = null;
          },
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void dismiss() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class _AppUpdateDialogContent extends StatefulWidget {
  final AppVersionEntity model;
  final VoidCallback onDismiss;

  const _AppUpdateDialogContent({
    required this.model,
    required this.onDismiss,
  });

  @override
  State<_AppUpdateDialogContent> createState() =>
      _AppUpdateDialogContentState();
}

class _AppUpdateDialogContentState extends State<_AppUpdateDialogContent> {
  int _downloadProgress = 0;
  bool isDownloading = false;

  @override
  Widget build(BuildContext context) {
    return BaseWillPopPage(
      isAllowBack: false,
      child: Center(
        child: _buildDialogContent(context),
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      width: 360.gw,
      height: 646.gw,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        image: DecorationImage(
          image: AssetImage('assets/images/dialog/bg_app_update.png'),
          fit: BoxFit.fill,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(40.gw, 35.gw, 40.gw, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 228.gw),
            Text(
              textAlign: TextAlign.left,
              'update_new_version_available'.tr(),
              style: context.textTheme.primary.fs24.w600,
            ),
            Text(
              textAlign: TextAlign.left,
              widget.model.version,
              style: context.textTheme.primary.fs16.w500,
            ),
            SizedBox(height: 12.gw),
            Container(
              height: 1.gw,
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              color: context.colorTheme.borderA,
            ),
            SizedBox(height: 16.gw),
            _buildUpdateContentSection(context),
            SizedBox(height: 24.gw),
            if (isDownloading) ...[
              ProgressBar(progress: _downloadProgress.toDouble()),
            ] else ...[
              _buildActionButtons(context),
            ],
            SizedBox(height: 16.gw),
            _buildCurrentVersionText(context),
          ],
        ),
      ),
    );
  }

  /// Creates the update content section
  Widget _buildUpdateContentSection(BuildContext context) {
    return SizedBox(
      height: 150.gw,
      width: 299.gw,
      child: Theme(
        data: Theme.of(context).copyWith(
          scrollbarTheme: ScrollbarThemeData(
            thumbColor: WidgetStateProperty.all(
              const Color(0xffffffff).withOpacity(0.5),
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Scrollbar(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: Text(
                          widget.model.releaseNotes,
                          style: context.textTheme.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Creates the action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 10.gw),
      child: Row(
        children: [
          if (!widget.model.forceUpdate) ...[
            Expanded(
              child: _buildSkipUpdateButton(context),
            ),
            SizedBox(width: 20.gw),
          ],
          Expanded(
            child: _buildUpdateButton(context),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton(BuildContext context) {
    return InkWell(
      onTap: () => _handleUpdatePressed(context),
      child: Container(
        height: 42.gw,
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        decoration: ShapeDecoration(
          color: const Color(0xFFFFD038),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 1,
              color: Color(0xFFFFE157),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'update_update_now'.tr(),
              style: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkipUpdateButton(BuildContext context) {
    return InkWell(
      onTap: () async {
        widget.onDismiss();
      },
      child: Container(
        height: 42.gw,
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        decoration: ShapeDecoration(
          color: const Color(0xFF4F58A7),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 1,
              color: Color(0xFF5F69C1),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'update_not_now'.tr(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.fs,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handles the update button press
  void _handleUpdatePressed(BuildContext context) async {
    if (isDownloading) return;
    setState(() {
      isDownloading = true;
    });

    try {
      final flag = await SystemUtil.manageAppDownload(
        url: widget.model.url,
        updateProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
          });
        },
      );

      if (!flag) {
        if (mounted) {
          showHelpOverlay(context);
        }
      }
    } catch (e) {
      LogD("App_Update_Dialog Error: ${e.toString()}");
      if (mounted) {
        showHelpOverlay(context);
      }
    } finally {
      setState(() {
        isDownloading = false;
      });
    }
  }

  /// Creates the current version text
  Widget _buildCurrentVersionText(BuildContext context) {
    return Center(
      child: Text(
        "${'update_current_version'.tr()}：${SystemUtil.getVersion()}",
        style: context.textTheme.title.fs16.w500,
      ),
    );
  }
}

class ProgressBar extends StatelessWidget {
  final double progress;

  final double height;

  final LinearGradient? gradient;

  const ProgressBar({
    super.key,
    required this.progress,
    this.height = 5.0,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // _buildProgressText(),
        SizedBox(height: 4.gw),
        _buildProgressBarRow(),
        SizedBox(height: 8.gw),
        _buildProgressStatus(context),
      ],
    );
  }

  /// Creates the progress bar with filled and unfilled sections
  Widget _buildProgressBarRow() {
    return Row(
      children: [
        _buildFilledProgressSection(),
        _buildUnfilledProgressSection(),
      ],
    );
  }

  Widget _buildProgressStatus(BuildContext context) {
    return Center(
      child: Text(
        '${'update_updating'.tr()}...',
        style: context.textTheme.primary.fs16.w500,
      ),
    );
  }

  /// Creates the filled portion of the progress bar
  Widget _buildFilledProgressSection() {
    return Flexible(
      flex: progress.toInt(),
      fit: FlexFit.tight,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xffFFD038),
          borderRadius: _getFilledSectionBorderRadius(),
        ),
        child: SizedBox(height: height),
      ),
    );
  }

  /// Creates the unfilled portion of the progress bar
  Widget _buildUnfilledProgressSection() {
    return Flexible(
      fit: FlexFit.tight,
      flex: 100 - progress.toInt(),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xffD9D9D9),
          borderRadius: _getUnfilledSectionBorderRadius(),
        ),
        child: SizedBox(height: height),
      ),
    );
  }

  /// Default gradient for the progress bar
  LinearGradient _defaultGradient() {
    return const LinearGradient(
      colors: [
        Color(0xffFFD038),
        Color(0xffC69863),
      ],
    );
  }

  BorderRadius _getFilledSectionBorderRadius() {
    return progress == 100
        ? const BorderRadius.all(Radius.circular(4))
        : const BorderRadius.only(
            bottomLeft: Radius.circular(4), topLeft: Radius.circular(4));
  }

  BorderRadius _getUnfilledSectionBorderRadius() {
    return progress == 0
        ? const BorderRadius.all(Radius.circular(4))
        : const BorderRadius.only(
            bottomRight: Radius.circular(4), topRight: Radius.circular(4));
  }
}
