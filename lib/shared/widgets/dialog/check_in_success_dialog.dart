
import 'package:flutter/material.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';

class CheckInSuccessDialog {
  final num money;
  final int days;

  CheckInSuccessDialog({
    required this.money,
    required this.days,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _CheckInSuccessDialogContent(
          money: money,
          days: days,
        );
      },
    );
  }
}

class _CheckInSuccessDialogContent extends StatelessWidget {
  final num money;
  final int days;

  const _CheckInSuccessDialogContent({
    required this.money,
    required this.days,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 315.gw,
          height: 351.gw,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFF4EEE9),
                Color(0xFFF4EFE9),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 46.gw,
                child: Stack(
                  alignment: Alignment.topCenter,
                  clipBehavior: Clip.none,
                  children: [
                    Positioned(
                        top: -6.gw,
                        child: Image.asset(
                          "assets/images/check_in/bg_success_title.png",
                          width: 207.gw,
                          height: 46.gw,
                        )),
                    Text(
                      "签到成功",
                      style: TextStyle(
                        fontSize: 20.fs,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(height: 12.gw),
              _buildGoldImageWidget(),
              SizedBox(height: 10.gw),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '已获得',
                    style: TextStyle(
                      fontSize: 16.fs,
                      color: const Color(0xff5D370B),
                    ),
                  ),
                  Text(
                    ' $money ',
                    style: TextStyle(
                      fontSize: 32.gw,
                      fontFamily: 'Impact',
                      color: const Color(0xFFC0662E),
                    ),
                  ),
                  Text(
                    '金币',
                    style: TextStyle(
                      fontSize: 16.fs,
                      color: const Color(0xff5D370B),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '已累积签到$days天',
                style: TextStyle(
                  fontSize: 12.fs,
                  color: const Color(0xB25D370B),
                ),
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: Navigator.of(context).pop,
                child: Container(
                    width: 197.gw,
                    height: 40.gw,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20.gw)),
                      color: const Color(0xffB9936D),
                      border: Border.all(
                        width: 1,
                        color: Colors.white,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '立即收下',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.fs,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _buildGoldImageWidget() {
    return HotPushImage(
      imagePath: "assets/images/check_in/icon_gold_success.png",
      base64String: Base64Image.checkInSuccessCoinIconData,
      height: 132.gw,
    );
  }
}
