import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/lobby_api.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';

import '../red_pocket/red_packet_controller.dart';
import '../red_pocket/red_packet_painter.dart';

class LobbyRedPacketDialog {
  OverlayEntry? entry;

  void show(BuildContext context) {
    entry = OverlayEntry(
      builder: (context) => LobbyRedPacket(
        onFinish: _removeRedPacket,
      ),
    );
    Overlay.of(context).insert(entry!);
  }

  void _removeRedPacket() {
    entry?.remove();
    entry = null;
  }
}

class LobbyRedPacket extends StatefulWidget {
  final Function? onFinish;

  const LobbyRedPacket({
    super.key,
    this.onFinish,
  });

  @override
  State createState() => _LobbyRedPacketState();
}

class _LobbyRedPacketState extends State<LobbyRedPacket> with TickerProviderStateMixin {
  late RedPacketController controller;
  final String _message = "恭喜发财,大吉大利";

  @override
  void initState() {
    super.initState();
    controller = RedPacketController(tickerProvider: this);
    controller.onFinish = widget.onFinish;
    controller.showOpenText = controller.showOpenBtn = true;
  }

  Future<void> _fetchRedPacket({required Future<void> Function(bool) afterRequest}) async {
    try {
      // 获取红包金额
      final amount = await LobbyApi.receiveLobbyRedPacket();

      if (amount != null) {
        // 导航到详情页面
          sl<NavigatorService>().push(
            AppRouter.chatRedEnvelopeDetail,
            transition: TransitionType.none,
            opaque: false,
            arguments: {
              "title": "系统红包",
              "content": "恭喜发财，大吉大利",
              "amount": amount,
            },
          );

      }
      // 执行动画和延迟
      await afterRequest(amount != null);

      // 完成回调
      widget.onFinish?.call();
    } catch (e) {
      // 错误处理
      debugPrint('Red packet error: $e');
    }
  }

  void _handleEnvelopeOpen(TapUpDetails details) async {
    if (controller.checkClickGold(details.globalPosition) && !controller.angleController.isAnimating) {
        controller.angleController.repeat(reverse: true);
        setState(() {
          controller.showOpenText = false;
        });
        await _fetchRedPacket(afterRequest: (flag) async {
          controller.stop(null);
          if (flag) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
        });
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0x88000000),
      child: GestureDetector(
        child: ScaleTransition(
          scale: Tween<double>(begin: 0, end: 1.0).animate(
            CurvedAnimation(parent: controller.scaleController, curve: Curves.fastOutSlowIn),
          ),
          child: buildRedPacket(),
        ),
        onPanDown: (d) => controller.handleClick(d.globalPosition),
      ),
    );
  }

  Widget buildRedPacket() {
    return GestureDetector(
      onTapUp: _handleEnvelopeOpen,
      child: CustomPaint(
        size: Size(1.gsw, 1.gsh),
        painter: RedPacketPainter(controller: controller),
        child: AnimatedBuilder(
          animation: controller.translateController,
          builder: (context, child) => Container(
            padding: EdgeInsets.only(top: 0.3.gsh * (1 - controller.translateCtrl.value), left: 0.15.gsw, right: 0.15.gsw),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "系统红包",
                  style: TextStyle(
                    fontSize: 16.fs,
                    color: const Color(0xFFF8E7CB),
                    fontWeight: SystemUtil.isWeb() ? FontWeight.w500 : FontWeight.w600,
                  ),
                ),
                SizedBox(height: 15.gw),
                Text(
                  _message,
                  style: TextStyle(fontSize: 18.fs, color: const Color(0xFFF8E7CB)),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
