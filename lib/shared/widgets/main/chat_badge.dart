
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/features/page/3_chat/chat_cubit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitConversation/tim_ui_kit_conversation_total_unread.dart';


class ChatBadge extends StatelessWidget {
  const ChatBadge({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<UserCubit, UserState, bool>(
      selector: (state) => state.isLogin,
      builder: (context, isLogin) {
        if (!isLogin) {
          return const SizedBox.shrink();
        }
        return BlocSelector<ChatCubit, ChatState, bool>(
          selector: (state) => state.isTencentInitialized,
          builder: (context, state) {
            if (!state) return const SizedBox.shrink();
            return TIMUIKitConversationTotalUnread(
              builder: (unreadCount) {
                return unreadCount == 0
                    ? const SizedBox.shrink()
                    : Container(
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        padding: const EdgeInsets.all(4),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(color: Colors.white),
                        ),
                      );
              },
            );
          },
        );
      },
    );
  }
}
