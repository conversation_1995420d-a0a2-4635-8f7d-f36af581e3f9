import 'package:flutter/material.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';

class VideoSeekLimitAlert extends StatelessWidget {
  final VoidCallback onClickLogin;

  const VideoSeekLimitAlert({super.key, required this.onClickLogin});

  @override
  Widget build(BuildContext context) {
    final isLogin = sl<UserCubit>().state.isLogin;
    final contentText = isLogin ? "打码送观影券，解锁无限观影特权" : "登录后可观看更多视频";
    final btnTitle = isLogin ? "玩游戏" : "去登录";

    final headImage = 'assets/images/home/<USER>'b' : 'a'}.png';
    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        Positioned.fill(
            child: Container(
                decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          color: Colors.white,
        ))),
        Container(
          width: 200.gw,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            image: const DecorationImage(image: AssetImage("assets/images/home/<USER>"), fit: BoxFit.fill),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 38.gw),
              Text(
                contentText,
                style: TextStyle(
                  color: const Color(0xff323232),
                  fontSize: 12.fs,
                ),
              ),
              SizedBox(height: 16.gw),
              CommonButton(
                bgImgPath: 'assets/images/button/bg_button_golden_85w.png',
                titlePadding: EdgeInsets.only(top: 1.gw, bottom: 6.gw),
                backgroundColor: Colors.transparent,
                fontSize: 14.fs,
                title: btnTitle,
                width: 85.gw,
                height: 32.gw,
                onPressed: onClickLogin,
              ),
              // SizedBox(height: 14.gw),
            ],
          ),
        ),
        Positioned(
          top: isLogin ? -25.gw :-36.gw, // 调整图标位置
          child: Image.asset(
            headImage,
            width: isLogin ? 100.gw : 72.gw,
            height: 72.gw,
          ),
        ),
      ],
    );
  }
}
