import 'dart:math' as math;

import 'package:flutter/gestures.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';

import 'nested_scroll_coordinator.dart';

/// 滑动位置信息
class NestedScrollPosition extends ScrollPosition implements ScrollActivityDelegate {
  NestedScrollPosition({
    required super.physics,
    required super.context,
    double initialPixels = 0.0,
    super.keepScrollOffset,
    super.oldPosition,
    super.debugLabel,
    required this.coordinator,
  }) {
    correctPixels(initialPixels);
    goIdle();
  }

  final NestedScrollCoordinator coordinator;
  ScrollDragController? _currentDrag;
  double _heldPreviousVelocity = 0.0;

  @override
  AxisDirection get axisDirection => context.axisDirection;

  @override
  double setPixels(double newPixels) {
    assert(activity?.isScrolling ?? false);
    return super.setPixels(newPixels);
  }

  @override
  void absorb(ScrollPosition other) {
    super.absorb(other);
    if (other is! NestedScrollPosition) {
      goIdle();
      return;
    }
    activity?.updateDelegate(this);
    _userScrollDirection = other._userScrollDirection;
    assert(_currentDrag == null);
    if (other._currentDrag != null) {
      _currentDrag = other._currentDrag;
      _currentDrag!.updateDelegate(this);
      other._currentDrag = null;
    }
  }

  @override
  void applyNewDimensions() {
    super.applyNewDimensions();
    context.setCanDrag(physics.shouldAcceptUserOffset(this));
  }

  double applyClampedDragUpdate(double delta) {
    assert(delta != 0.0);
    final double min =
    delta < 0.0 ? -double.infinity : math.min(minScrollExtent, pixels);
    final double max =
    delta > 0.0 ? double.infinity : math.max(maxScrollExtent, pixels);
    final double oldPixels = pixels;
    final double newPixels = (pixels - delta).clamp(min, max);
    final double clampedDelta = newPixels - pixels;
    if (clampedDelta == 0.0) {
      return delta;
    }
    final double overScroll = physics.applyBoundaryConditions(this, newPixels);
    final double actualNewPixels = newPixels - overScroll;
    final double offset = actualNewPixels - oldPixels;
    if (offset != 0.0) {
      forcePixels(actualNewPixels);
      didUpdateScrollPositionBy(offset);
    }
    return delta + offset;
  }

  double applyFullDragUpdate(double delta) {
    assert(delta != 0.0);
    final double oldPixels = pixels;
    final double newPixels =
        pixels - physics.applyPhysicsToUserOffset(this, delta);
    if (oldPixels == newPixels) {
      return 0.0;
    }
    final double overScroll = physics.applyBoundaryConditions(this, newPixels);
    final double actualNewPixels = newPixels - overScroll;
    if (actualNewPixels != oldPixels) {
      forcePixels(actualNewPixels);
      didUpdateScrollPositionBy(actualNewPixels - oldPixels);
    }
    return overScroll;
  }

  @override
  void applyUserOffset(double delta) {
    final userScrollDirection =
    delta > 0.0 ? ScrollDirection.forward : ScrollDirection.reverse;
    if (debugLabel != coordinator.pageLabel) {
      coordinator.applyUserOffset(delta, userScrollDirection, this);
      return;
    }
    updateUserScrollDirection(userScrollDirection);
    setPixels(pixels - physics.applyPhysicsToUserOffset(this, delta));
  }

  @override
  void beginActivity(ScrollActivity? newActivity) {
    _heldPreviousVelocity = 0.0;
    super.beginActivity(newActivity);
    _currentDrag?.dispose();
    _currentDrag = null;
    if (!(activity?.isScrolling ?? false)) {
      updateUserScrollDirection(ScrollDirection.idle);
    }
  }

  @protected
  @visibleForTesting
  void updateUserScrollDirection(ScrollDirection value) {
    if (_userScrollDirection == value) return;
    _userScrollDirection = value;
    didUpdateScrollDirection(value);
  }

  @override
  ScrollHoldController hold(VoidCallback holdCancelCallback) {
    final previousVelocity = activity?.velocity ?? 0.0;
    final holdActivity = HoldScrollActivity(
      delegate: this,
      onHoldCanceled: holdCancelCallback,
    );
    beginActivity(holdActivity);
    _heldPreviousVelocity = previousVelocity;
    return holdActivity;
  }

  @override
  Drag drag(DragStartDetails details, VoidCallback dragCancelCallback) {
    final drag = ScrollDragController(
      delegate: this,
      details: details,
      onDragCanceled: dragCancelCallback,
      carriedVelocity: physics.carriedMomentum(_heldPreviousVelocity),
      motionStartDistanceThreshold: physics.dragStartDistanceMotionThreshold,
    );
    beginActivity(DragScrollActivity(this, drag));
    assert(_currentDrag == null);
    _currentDrag = drag;
    return drag;
  }

  @override
  void goIdle() {
    beginActivity(IdleScrollActivity(this));
  }

  @override
  void goBallistic(double velocity, [bool fromCoordinator = false]) {
    if (debugLabel != coordinator.pageLabel) {
      if (velocity > 0.0) {
        coordinator.goBallistic(velocity);
      }
    } else {
      if (fromCoordinator && velocity <= 0.0) return;
      if (coordinator.pageExpand == PageExpandState.Expanding) return;
    }
    final simulation = physics.createBallisticSimulation(this, velocity);
    if (simulation != null) {
      beginActivity(BallisticScrollActivity(this, simulation, context.vsync, false));
    } else {
      goIdle();
    }
  }

  @override
  bool applyContentDimensions(double minScrollExtent, double maxScrollExtent,
      [bool fromCoordinator = false]) {
    if (debugLabel == coordinator.pageLabel && !fromCoordinator) {
      return coordinator.applyContentDimensions(
          minScrollExtent, maxScrollExtent, this);
    }
    return super.applyContentDimensions(minScrollExtent, maxScrollExtent);
  }

  @override
  void pointerScroll(double delta) {
    if (delta == 0.0) return;

    final userScrollDirection =
    delta > 0.0 ? ScrollDirection.forward : ScrollDirection.reverse;
    updateUserScrollDirection(userScrollDirection);

    final double offset = physics.applyPhysicsToUserOffset(this, delta);
    final double target = pixels + offset;

    // 强制 clamp 到边界
    final double clamped = target.clamp(minScrollExtent, maxScrollExtent);

    if (clamped != pixels) {
      final double deltaActual = clamped - pixels;
      forcePixels(clamped);
      didUpdateScrollPositionBy(deltaActual);
    }
  }

  @override
  ScrollDirection get userScrollDirection => _userScrollDirection;
  ScrollDirection _userScrollDirection = ScrollDirection.idle;

  @override
  Future<void> animateTo(
      double to, {
        required Duration duration,
        required Curve curve,
      }) {
    final devicePixelRatio = WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;
    final toleranceDistance = 1.0 / devicePixelRatio;
    if ((to - pixels).abs() < toleranceDistance) {
      jumpTo(to);
      return Future.value();
    }
    final activity = DrivenScrollActivity(
      this,
      from: pixels,
      to: to,
      duration: duration,
      curve: curve,
      vsync: context.vsync,
    );
    beginActivity(activity);
    return activity.done;
  }

  @override
  void jumpTo(double value) {
    goIdle();
    if (pixels != value) {
      final oldPixels = pixels;
      forcePixels(value);
      notifyListeners();
      didStartScroll();
      didUpdateScrollPositionBy(pixels - oldPixels);
      didEndScroll();
    }
    goBallistic(0.0);
  }

  @Deprecated('This will lead to bugs.')
  @override
  void jumpToWithoutSettling(double value) {
    goIdle();
    if (pixels != value) {
      final oldPixels = pixels;
      forcePixels(value);
      notifyListeners();
      didStartScroll();
      didUpdateScrollPositionBy(pixels - oldPixels);
      didEndScroll();
    }
  }

  @override
  void dispose() {
    _currentDrag?.dispose();
    _currentDrag = null;
    super.dispose();
  }

  @override
  void debugFillDescription(List<String> description) {
    super.debugFillDescription(description);
    description.add('${context.runtimeType}');
    description.add('$physics');
    description.add('$activity');
    description.add('$userScrollDirection');
  }
}
