// lib/shared/widgets/webview/webview_native.dart
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/media_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/sheet/media_picker_sheet.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart' as webview_flutter_android;
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class WebView extends StatefulWidget {
  final String initialUrl;

  const WebView({super.key, required this.initialUrl});

  @override
  NativeWebViewState createState() => NativeWebViewState();
}

class NativeWebViewState extends State<WebView> {
  late final WebViewController _controller;
  int _progress = 0;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  @override
  void dispose() {
    try {
      // 在 dispose 之前尝试清理 WebView
      if (mounted) {
        _controller.loadRequest(Uri.parse('about:blank'));
        _controller.clearCache();
      }
    } catch (e) {
      print('WebView dispose error: $e');
    } finally {
      super.dispose();
    }
  }

  void _initializeWebView() async {

    try {

      late final PlatformWebViewControllerCreationParams params;

      if (WebViewPlatform.instance is WebKitWebViewPlatform) {
        params = WebKitWebViewControllerCreationParams(
          allowsInlineMediaPlayback: true,
          mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
        );
      } else {
        params = const PlatformWebViewControllerCreationParams();
      }

      _controller = WebViewController.fromPlatformCreationParams(params)
        ..setJavaScriptMode(JavaScriptMode.unrestricted) // 允许执行JavaScript
        ..setBackgroundColor(Colors.black) // 设置 WebView 背景色
        // ..addJavaScriptChannel(
        //   'recharge',
        //   onMessageReceived: (message) {
        //     sl<NavigatorService>().push(AppRouter.liveTopUp);
        //   },
        // )
        ..addJavaScriptChannel(
          'backToApp',
          onMessageReceived: (message) {
            sl<NavigatorService>().pop();
          },
        )
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (url) => _updateProgress(0),
            onNavigationRequest: (NavigationRequest request) {
              // 确保所有的链接都在当前的 WebView 中加载
              if (request.url.startsWith('http')) {
                return NavigationDecision.navigate;
              } else {
                // 拒绝其他不需要的导航请求（如尝试打开新的窗口）
                return NavigationDecision.prevent;
              }
            },
            onPageFinished: (url) {
              _updateProgress(100);

              /// 注入 JavaScript，确保所有链接都在当前的 WebView 中打开
              _controller.runJavaScript("""
              (function() {
                // 修改所有链接的 target 属性
                var links = document.querySelectorAll('a[target="_blank"]');
                for (var i = 0; i < links.length; i++) {
                  links[i].setAttribute('target', '_self');
                }
            
                document.querySelectorAll('video, audio').forEach(media => {
                  media.setAttribute('autoplay', 'true');
                });

                // 视频内联播放设置
                document.querySelectorAll('video').forEach(video => {
                  video.setAttribute('playsinline', 'true');
                  video.setAttribute('webkit-playsinline', 'true');
                });

                // 处理新窗口打开
                window.open = function(url) {
                  window.location.href = url;
                };
                
                // 处理链接点击
                document.addEventListener('click', function(event) {
                  var target = event.target;
                  if (target && target.nodeName === 'A' && target.getAttribute('target') === '_blank') {
                    event.preventDefault();
                    window.location.href = target.href;
                  }
                });
                
                // 模拟点击overlay
                // var overlay = document.querySelector("#overlay");
                // if (overlay) overlay.click();
              })();
            """);
            },
            onProgress: (progress) => _updateProgress(progress),
          ),
        )
        ..loadRequest(Uri.parse(widget.initialUrl));

      if (Platform.isAndroid) {
        final controller = (_controller.platform as webview_flutter_android.AndroidWebViewController);
        await controller.setMediaPlaybackRequiresUserGesture(false);
        await controller.setOnShowFileSelector(_androidFilePicker);
      }
    } catch (e) {
      LogE("_initializeWebView Error: $e");
    }

  }

  Future<List<String>> _androidFilePicker(webview_flutter_android.FileSelectorParams params) async {
    if (params.acceptTypes.any((type) => type == 'image/*')) {
      // 处理图片选择
      return MediaUtil.handleImagePicker(
        imageQuality: 90,
        maxWidth: 500,
      );
    } else if (params.acceptTypes.any((type) => type == 'video/*')) {
      // 处理视频选择
      return MediaUtil.handleVideoPicker(maxSizeMB: 50);
    } else {
      // 处理其他类型文件
      final result = await showModalBottomSheet<List<String>>(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => MediaPickerSheet(
          onCamera: () => MediaUtil.handleCameraCapture(context),
          onGallery: () => MediaUtil.handleMediaPicker(context, maxAssets: 1),
          onFile: () => MediaUtil.handleFilePicker(maxSizeMB: 100),
        ),
      );

      return result ?? [];
    }
  }

  void _updateProgress(int progress) {
    if (mounted) {
      setState(() {
        _progress = progress;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        if (_progress < 100)
          LinearProgressIndicator(
            backgroundColor: Colors.transparent,
            value: _progress / 100,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
      ],
    );
  }
}
