import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import 'package:rxdart/rxdart.dart';
import 'package:flutter/material.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';

class WebView extends StatefulWidget {
  final String initialUrl;

  const WebView({super.key, required this.initialUrl});

  @override
  _WebViewState createState() => _WebViewState();
}

class _WebViewState extends State<WebView> {
  late UniqueKey _iframeKey;
  late String viewType;

  final _messageController = PublishSubject<String>();

  @override
  void initState() {
    super.initState();

    _iframeKey = UniqueKey();

    viewType = 'web-view$_iframeKey';
    ui_web.platformViewRegistry.registerViewFactory(
      viewType,
      (int viewId) {
        final html.IFrameElement element = html.IFrameElement()
          ..style.width = '100%' // 明确设置宽度
          ..style.height = '100%' // 明确设置高度
          ..src = widget.initialUrl
          ..style.border = 'none'
          ..allow = 'clipboard-read; clipboard-write'; // Enable clipboard access

        return element;
      },
    );

    html.window.addEventListener('message', listen, true);

    _messageController.debounceTime(const Duration(milliseconds: 500)).listen((data) {
      // if (data == 'recharge') {
      //   sl<NavigatorService>().push(AppRouter.liveTopUp);
      // } else

      if (data == 'backToApp') {
        sl<NavigatorService>().pop();
      }
    });
  }

  @override
  void dispose() {
    html.window.removeEventListener('message', listen, true);
    _messageController.close();
    super.dispose();
  }

  void listen(html.Event event) {
    var data = (event as html.MessageEvent).data;
    _messageController.add(data);
  }

  @override
  Widget build(BuildContext context) {
    return HtmlElementView(viewType: viewType);
  }
}
