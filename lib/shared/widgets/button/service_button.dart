import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';

class ServiceButton extends StatelessWidget {
  final Color? textColor;
  const ServiceButton({super.key, this.textColor});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return InkWell(
      onTap: () => SystemUtil.contactService(),
      child: Row(
        children: [
          Image.asset(Assets.iconSupport, width: 20.gw, height: 20.gw),
          SizedBox(width: 5.gw),
          Text(
            'support'.tr(),
            style: TextStyle(
              fontSize: 12.fs,
              color: textColor ?? const Color(0xff6A7391),
            ),
          ),
        ],
      ),
    );
  }
}
