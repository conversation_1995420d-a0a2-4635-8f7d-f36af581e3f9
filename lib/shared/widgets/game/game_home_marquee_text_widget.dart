import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/models/entities/game_notice_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_marquee_text.dart';

class GameHomeMarqueeTextWidget extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final double? height;
  final List<GameNoticeEntity> data;

  const GameHomeMarqueeTextWidget({super.key, required this.data, this.margin, this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        border: Border.all(color: context.theme.dividerColor, width: 1),
        borderRadius: BorderRadius.all(Radius.circular(10.gw)),
      ),
      height: height ?? 34.gw,
      padding: EdgeInsets.symmetric(horizontal: 4.gw),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            "assets/images/home/<USER>",
            width: 28.gw,
            height: 26.gw,
          ),
          SizedBox(width: 10.gw),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: 5.gw),
              child: CommonMarqueeText(
                items: data
                    .map((element) => TextSpan(
                        text: element.noticeTitle,
                        style: context.textTheme.highlight.ffAne))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
