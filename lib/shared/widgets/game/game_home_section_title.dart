

import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class GameHomeSectionTitle extends StatelessWidget {
  final String title;
  const GameHomeSectionTitle({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 52.gw,
      alignment: Alignment.center,
      child: SizedBox(
        height: 20.gw,
        child: Row(
          children: [
            Image.asset("assets/images/home/<USER>", width: 23.gw, height: 20.gw,),
            SizedBox(width: 8.gw),
            Text(title, style: context.textTheme.primary.fs20.w500.ffAne),
            SizedBox(width: 10.gw),
            Expanded(child: Container(decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.colorTheme.textPrimary.withOpacity(0.2),
                  Colors.transparent,
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ), height: 1)),
          ],
        ),
      ),
    );
  }
}