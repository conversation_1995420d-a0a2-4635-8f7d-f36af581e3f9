import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/view_models/popular_section_view_model.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/game/popular/game_home_popular_list_view.dart';
import 'package:wd/features/page/1_game_home/game_home_cubit.dart';

import '../../../../features/page/1_game_home/game_home_state.dart';

class GameHomePopularSection extends StatefulWidget {
  /// 热门
  PopularSectionViewModel? popularModel;

  /// 最近
  PopularSectionViewModel? recentlyModel;

  /// 收藏
  PopularSectionViewModel? favModel;

  /// 点击事件回调
  final Function(GameV2) onGameTap;
  final Function(GamePlatformV2) onVenueTap;
  final Function(GameV2) onGameFavTap;

  GameHomePopularSection({
    super.key,
    this.popularModel,
    this.recentlyModel,
    this.favModel,
    required this.onGameTap,
    required this.onVenueTap,
    required this.onGameFavTap,
  });

  @override
  State<StatefulWidget> createState() => _GameHomePopularSectionState();
}

class PopularPageConfig {
  final GameHomePopularType type;
  final List<GameV2>? Function() getGameList;
  final List<GamePlatformV2>? Function() getVenueList;

  const PopularPageConfig({
    required this.type,
    required this.getGameList,
    required this.getVenueList,
  });
}

class _GameHomePopularSectionState extends State<GameHomePopularSection>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final _tabs = GameHomePopularType.values;
  double _currentHeight = 0;
  late GameHomeCubit _cubit;

  List<PopularPageConfig> get _pageConfigs {
    final list = [
      PopularPageConfig(
        type: GameHomePopularType.popular,
        getGameList: () => widget.popularModel?.gameList,
        getVenueList: () => widget.popularModel?.venueList,
      ),
      PopularPageConfig(
        type: GameHomePopularType.recent,
        getGameList: () => widget.recentlyModel?.gameList,
        getVenueList: () => widget.recentlyModel?.venueList,
      ),
      PopularPageConfig(
        type: GameHomePopularType.favorites,
        getGameList: () => widget.favModel?.gameList,
        getVenueList: () => null,
      ),
    ];

    // 确保与 TabController 长度一致
    return sl<UserCubit>().state.isLogin ? list : [list.first];
  }

  void _updateHeight(GameHomePopularType type) {
    double height = 0;
    switch (type) {
      case GameHomePopularType.popular:
        height = widget.popularModel?.sectionHeight ?? 200.gw;
      case GameHomePopularType.recent:
        height = widget.recentlyModel?.sectionHeight ?? 200.gw;
      case GameHomePopularType.favorites:
        height = widget.favModel?.sectionHeight ?? 200.gw;
    }

    if (mounted && height != _currentHeight) {
      setState(() {
        _currentHeight = height;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _cubit = context.read<GameHomeCubit>();
    _cubit.initPopularControllers(this);
    _updateHeight(GameHomePopularType.values[_cubit.state.currentPopularIndex]);
  }

  @override
  void didUpdateWidget(GameHomePopularSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当数据更新时，重新计算高度
    _updateHeight(GameHomePopularType.values[_cubit.state.currentPopularIndex]);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocBuilder<GameHomeCubit, GameHomeState>(
      builder: (context, state) {
        // 如果需要更新高度，则更新
        if (state.needUpdatePopularHeight) {
          _updateHeight(GameHomePopularType.values[state.currentPopularIndex]);
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: _currentHeight,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTabBar(),
              Expanded(
                child: PageView(
                  controller: _cubit.popularPageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: _cubit.onPopularPageChanged,
                  children: _pageConfigs.map((config) => _buildPage(config)).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPage(PopularPageConfig config) {
    return GameHomePopularListView(
      type: config.type,
      gameList: config.getGameList(),
      venueList: config.getVenueList(),
      onGameTap: widget.onGameTap,
      onVenueTap: widget.onVenueTap,
      onGameFavTap: widget.onGameFavTap,
    );
  }

  _buildTabBar() {
    return BlocConsumer<UserCubit, UserState>(
      listenWhen: (previous, current) => current.isLogin != previous.isLogin,
      listener: (context, state) {
        _cubit.initPopularControllers(this);
      },
      buildWhen: (previous, current) => current.isLogin != previous.isLogin,
      builder: (context, state) {
        final tabs = state.isLogin
            ? _tabs // 登录时显示所有 Tab
            : [_tabs.first]; // 未登录时只显示第一个 Tab

        return CommonTabBar(
          tabs.map((e) => CommonTabBarItem(title: e.tabTitle, imageUrl: e.tabImagePath)).toList(),
          currentIndex: _cubit.state.currentPopularIndex,
          onTap: (index) => _cubit.onTabPageChanging(index),
        );

      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
