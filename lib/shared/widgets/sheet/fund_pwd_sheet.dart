import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FundPwdSheet {
  final BuildContext context;
  final TextEditingController passwordController = TextEditingController();

  FundPwdSheet(this.context);

  Future<String?> show() async {
    // 计算各个组件的高度
    final headerHeight = 24.gw + 16.gw; // Header的高度 (文字 + Padding)
    final passwordFieldsHeight = 48.gw; // 密码输入框的高度
    final passwordFieldsPadding = 15.gw; // 密码输入框的Padding
    final numberPadHeight = 216.gw; // 数字键盘的高度（根据GridView的item高度和行数计算）

    // 总高度
    final totalHeight = headerHeight + passwordFieldsHeight + passwordFieldsPadding + numberPadHeight + 25.gw * 2;

    return await showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: false,
      // useSafeArea: true,
      // Enable safe area
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () => Navigator.of(context).pop(), // 点击背景关闭对话框
          child: Container(
            // height: totalHeight + MediaQuery.of(context).viewInsets.bottom, // 计算得到的总高度
            padding: EdgeInsets.only(top: 16.gw),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
            ),
            child: GestureDetector(
              onTap: () {}, // 阻止点击事件穿透
              child: StatefulBuilder(
                builder: (BuildContext context, StateSetter setState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildHeader(context, height: headerHeight),
                      SizedBox(height: 25.gw),
                      _buildPasswordFields(setState, height: passwordFieldsHeight),
                      SizedBox(height: 25.gw),
                      _buildNumberPad(setState, height: numberPadHeight),
                      Container(height: MediaQuery.of(context).viewPadding.bottom, color: const Color(0xfff5f5f5)),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, {required double height}) {
    return SizedBox(
      height: height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(width: 24.gw), // For alignment
          Text(
            'hint_enter_fund_password'.tr(),
            style: TextStyle(
              fontSize: 18.fs,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Padding(
              padding: EdgeInsets.only(right: 15.gw),
              child: Icon(Icons.close, color: Colors.black, size: 24.gw),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordFields(StateSetter setState, {required double height}) {
    return SizedBox(
      height: height,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(6, (index) {
            return Container(
              width: 45.gw,
              height: 48.gw,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xfff5f5f5),
                border: Border.all(
                    width: 1,
                    color: passwordController.text.length == index ? const Color(0xff006ef6) : Colors.transparent),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Align(
                alignment: Alignment.center,
                child: passwordController.text.length > index
                    ? Icon(
                        Icons.circle,
                        size: 15.gw,
                        color: Colors.black,
                      ) // 使用一个Icon或其他居中的Widget代替Text
                    : const SizedBox.shrink(),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildNumberPad(StateSetter setState, {required double height}) {
    return Container(
      height: height,
      color: const Color(0xfff5f5f5),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 12,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 0.1,
          crossAxisSpacing: 0.1,
          childAspectRatio: 2.3 / 1, // Set the aspect ratio here
        ),
        itemBuilder: (context, index) {
          if (index == 9) {
            return const SizedBox.shrink(); // Empty space for 0 button
          } else if (index == 10) {
            return _buildNumberButton('0', setState);
          } else if (index == 11) {
            return _buildDeleteButton(setState);
          } else {
            return _buildNumberButton('${index + 1}', setState);
          }
        },
      ),
    );
  }

  Widget _buildNumberButton(String number, StateSetter setState) {
    return GestureDetector(
      onTap: () {
        if (passwordController.text.length < 6) {
          passwordController.text += number;
          setState(() {});

          if (passwordController.text.length == 6) {
            Navigator.of(context).pop(passwordController.text); // Return the password
          }
        }
      },
      child: Container(
        margin: EdgeInsets.all(1.gw),
        alignment: Alignment.center,
        color: Colors.white,
        child: Text(
          number,
          style:
              TextStyle(fontSize: 24.fs, fontFamily: 'WeChatSansStd', color: Colors.black, fontWeight: FontWeight.w300),
        ),
      ),
    );
  }

  Widget _buildDeleteButton(StateSetter setState) {
    return GestureDetector(
      onTap: () {
        if (passwordController.text.isNotEmpty) {
          passwordController.text = passwordController.text.substring(0, passwordController.text.length - 1);
          setState(() {});
        }
      },
      child: Container(
        margin: EdgeInsets.all(1.gw),
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/common/icon_keyboard_del.png",
          width: 24.gw,
          height: 24.gw,
        ),
      ),
    );
  }
}
