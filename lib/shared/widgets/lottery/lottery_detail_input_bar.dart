import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/features/page/0_home/home_lottery/lottery_detail/lottery_detail_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/core/utils/screenUtil.dart';

class LotteryDetailInputBar extends StatefulWidget {
  final TextEditingController controller;

  const LotteryDetailInputBar({super.key, required this.controller});

  @override
  State<StatefulWidget> createState() => LotteryDetailInputBarState();
}

class LotteryDetailInputBarState extends State<LotteryDetailInputBar> {
  late TextEditingController currentTextController;
  final FocusNode _focusNode = FocusNode();
  double inputToolBarHeight = 49.gw;
  double fastButtonHeight = 55.gw;

  double keyboardHeight = 239.gw;
  late double fullKeyboardHeight;
  bool showKeyboard = false;
  bool showFastButton = false;

  @override
  void initState() {
    currentTextController = widget.controller;
    fullKeyboardHeight = fastButtonHeight + keyboardHeight;
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  void hideKeyboardAndFastButton() {

    sl<NavigatorService>().unFocus();
    setState(() {
      showKeyboard = false;
      showFastButton = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        /// 键盘
        if (showKeyboard) _getKeyBoard(),
        if (showFastButton) _buildFastButtons(),
        Container(
          width: GSScreenUtil().screenWidth,
          height: inputToolBarHeight,
          decoration: BoxDecoration(
              color: const Color(0xffA182A9).withOpacity(0.44), // 使用十六进制颜色 #A182A970
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              )),
          child: Row(
            children: [
              const SizedBox(width: 14),
              GSTextImageButton(
                text: "清空",
                imageAssets: "assets/images/home/<USER>/lottery_tool_delete.png",
                textStyle: TextStyle(
                  color: Colors.white,
                  fontSize: 12.fs,
                  fontWeight: FontWeight.w500,
                ),
                interval: 2,
                position: GSTextImageButtonPosition.top,
                onPressed: () => BlocProvider.of<LotteryDetailCubit>(context).resetData(),
              ),
              const SizedBox(width: 19),
              Expanded(
                  child: Container(
                height: 31.gw,
                padding: const EdgeInsets.symmetric(horizontal: 9),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3), // 背景色 #000000
                  borderRadius: BorderRadius.circular(4),
                ),
                child: TextField(
                  focusNode: _focusNode,
                  controller: currentTextController,
                  showCursor: true,
                  cursorColor: Colors.white,
                  onTap: () {
                    setState(() {
                      showKeyboard = true;
                    });
                  },
                  readOnly: true,
                  style: TextStyle(
                    color: Colors.white, // 输入文字颜色
                    fontSize: 12.fs, // 文字大小 12
                  ),
                  decoration: InputDecoration(
                    hintText: '请输入金额',
                    // 提示文本
                    hintStyle: TextStyle(
                      color: Colors.white.withOpacity(0.6), // 提示文本颜色 #FFFFFF
                      fontSize: 12.fs, // 提示文本大小 12
                    ),
                    isCollapsed: true,
                    contentPadding: EdgeInsets.zero,
                    // 控制上下居中

                    border: InputBorder.none, // 隐藏边框
                  ),
                ),
              )),
              const SizedBox(width: 19),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  setState(() {
                    showFastButton = !showFastButton;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 11.5),
                  height: 31.gw,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    color: Color(0xFFBB88A6),
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                  ),
                  child: Text(
                    '快捷金额', // 按钮标题
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500, // 字体粗细 w500
                      fontSize: 16.fs, // 文字大小（可根据需求调整）
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 19),
            ],
          ),
        ),
      ],
    );
  }

  /// 键盘
  _getKeyBoard() {
    return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15),
        ),
        child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3), // 模糊程度
            child: Container(
              height: (showFastButton ? fullKeyboardHeight : keyboardHeight) + inputToolBarHeight,
              width: GSScreenUtil().screenWidth,
              padding: EdgeInsets.only(top: 10.gw),
              decoration: BoxDecoration(
                color: const Color(0xff7A7A7A).withOpacity(0.6), // 黑色半透明背景
              ),
              child: Column(
                children: [
                  Container(
                      // color: Colors.red,
                      height: keyboardHeight,
                      alignment: Alignment.center,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                              child: Wrap(
                            spacing: 0, // 主轴(水平)方向间距
                            runSpacing: 0, // 纵轴（垂直）方向间距
                            alignment: WrapAlignment.center, //沿主轴方向居中
                            children: [
                              _keyButton(text: '1'),
                              _keyButton(text: '2'),
                              _keyButton(text: '3'),
                              _keyButton(text: '4'),
                              _keyButton(text: '5'),
                              _keyButton(text: '6'),
                              _keyButton(text: '7'),
                              _keyButton(text: '8'),
                              _keyButton(text: '9'),
                              _keyButton(text: '.'),
                              _keyButton(text: '0'),
                              _keyButton(
                                onPressed: () => _handlerClickAction('del'),
                                widget: Image.asset(
                                  'assets/images/home/<USER>/btn_keyboard_delete.png',
                                  width: 32,
                                  height: 32,
                                ),
                              ),
                            ],
                          )),
                          SizedBox(
                            width: 57.gw,
                            child: Column(
                              children: [
                                Expanded(
                                  child: _keyboardTextImageButton(
                                    text: "隐藏",
                                    img: "assets/images/home/<USER>/btn_keyboard_hide.png",
                                    onPressed: () => _handlerClickAction('hide'),
                                  ),
                                ),
                                SizedBox(height: 24.gw),
                                Expanded(
                                  child: _keyboardTextImageButton(
                                    text: "清除",
                                    img: "assets/images/home/<USER>/btn_keyboard_clear.png",
                                    onPressed: () => _handlerClickAction('clear'),
                                  ),
                                ),
                                SizedBox(height: 24.gw),
                                Expanded(
                                  child: _keyboardTextImageButton(
                                    text: "确认",
                                    img: "assets/images/home/<USER>/btn_keyboard_return.png",
                                    onPressed: () => _handlerClickAction('okay'),
                                  ),
                                ),
                                SizedBox(height: 14.gw),
                              ],
                            ),
                          ),
                          SizedBox(width: 7.gw),
                        ],
                      )),
                ],
              ),
            )));
  }

  _keyboardTextImageButton({required String text, required String img, required VoidCallback onPressed}) {
    return InkWell(
        onTap: onPressed,
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                img,
                width: 24,
                height: 24,
              ),
              const SizedBox(height: 2),
              Text(text,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.fs,
                    fontWeight: FontWeight.w500,
                  )),
            ],
          ),
        ));
  }

  _keyButton({
    String? text,
    Widget? widget,
    Color? bgColor,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    Color? textColor,
    VoidCallback? onPressed,
  }) {
    var sizeW = (GSScreenUtil().screenWidth) / 4;
    var sizeH = 59.0.gw;
    if (width != null) sizeW = width;
    if (height != null) sizeH = height;

    return ElevatedButton(
        onPressed: onPressed ?? () => _handlerClickAction(text!),
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: bgColor ?? Colors.transparent,
          minimumSize: Size(sizeW, sizeH),
          maximumSize: Size(sizeW, sizeH),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          overlayColor: Colors.black.withOpacity(0.1),
          splashFactory: NoSplash.splashFactory,
          animationDuration: const Duration(milliseconds: 50), // 取消按下时的动画，直接显示 overlayColor
        ),
        child: widget ??
            Text(
              text ?? '',
              style: TextStyle(
                fontSize: fontSize ?? 30.fs,
                color: textColor ?? Colors.white,
                fontWeight: fontWeight ?? FontWeight.w600,
              ),
            ));
  }

  _handlerClickAction(String text) {
    HapticFeedback.lightImpact();

    if (text == 'hide' || text == 'okay') {
      hideKeyboardAndFastButton();
    } else if (text == 'del') {
      if (currentTextController.text.isNotEmpty) {
        final textSelection = currentTextController.selection.isValid
            ? currentTextController.selection
            : TextSelection.collapsed(offset: currentTextController.text.length);
        
        if (textSelection.start > 0) {
          final newText = currentTextController.text.replaceRange(
            textSelection.start - 1,
            textSelection.start,
            "",
          );
          
          currentTextController.text = newText;
          currentTextController.selection = TextSelection.collapsed(
            offset: textSelection.start - 1,
          );
        }
      }
    } else if (text == 'clear') {
      currentTextController.clear();
    } else {
      final textSelection = currentTextController.selection.isValid
          ? currentTextController.selection
          : TextSelection.collapsed(offset: currentTextController.text.length);
      
      final newText = currentTextController.text.replaceRange(
        textSelection.start,
        textSelection.end,
        text,
      );
      
      if (double.tryParse(newText) != null) {
        currentTextController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: textSelection.start + text.length),
        );
      }
    }
    
    final amount = double.tryParse(currentTextController.text) ?? 0;
    BlocProvider.of<LotteryDetailCubit>(context).updateSingleOrderAmount(amount);
  }

  _buildFastButtons() {
    List dataList = ["5", "10", "25", "50", "100", "500", "1000", "5000", "10000"];

    Widget toolBar = Container(
      width: GSScreenUtil().screenWidth,
      color: showKeyboard ? Colors.transparent : const Color(0xff7A7A7A).withOpacity(0.6),
      height: fastButtonHeight + inputToolBarHeight,
      padding: EdgeInsets.only(top: 15.gw),
      child: Column(
        children: [
          SizedBox(
            height: 31,
            child: ListView.separated(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.only(left: 14.gw),
              itemBuilder: (context, index) {
                final text = dataList[index];
                return InkWell(
                    onTap: () {
                      currentTextController.text = text;
                      BlocProvider.of<LotteryDetailCubit>(context)
                          .updateSingleOrderAmount(double.tryParse(currentTextController.text) ?? 0);
                    },
                    child: Container(
                      constraints: const BoxConstraints(
                        minWidth: 62,
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(text,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18.fs,
                            fontWeight: FontWeight.w700,
                          )),
                    ));
              },
              separatorBuilder: (context, index) {
                return const SizedBox(width: 9);
              },
              itemCount: dataList.length,
            ),
          ),
        ],
      ),
    );

    return showKeyboard
        ? toolBar
        : ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
            child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3), // 模糊程度
                child: toolBar),
          );
  }
}
