import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/0_tiktok/video_home_cubit.dart';
import 'package:wd/features/page/0_tiktok/video_home_state.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/indicator/video_filckr_indicator.dart';
import 'package:wd/shared/widgets/tiktok/home/<USER>';
import 'package:wd/shared/widgets/tiktok/home/<USER>';
import 'package:wd/shared/widgets/tiktok/home_winner_list_view.dart';

import 'tik_tok_video_gesture.dart';

///
/// TikTok风格的一个视频页组件，覆盖在video上，提供以下功能：
/// 播放按钮的遮罩
/// 单击事件
/// 点赞事件回调（每次）
/// 长宽比控制
/// 底部padding（用于适配有沉浸式底部状态栏时）
///
class TikTokVideoPage extends StatelessWidget {
  final Widget? video;
  final double aspectRatio;
  final String? tag;
  final String videoImage;
  final bool showRenderCover; // 是否显示封面
  final double bottomPadding;

  final Widget? rightButtonColumn;
  final Widget? userInfoWidget;
  final Widget? sliderWidget;

  final bool hidePauseIcon;

  final Function? onAddFavorite;
  final Function? onSingleTap;

  const TikTokVideoPage({
    super.key,
    this.bottomPadding = 16,
    this.tag,
    required this.videoImage,
    required this.showRenderCover,
    this.rightButtonColumn,
    this.userInfoWidget,
    this.sliderWidget,
    this.onAddFavorite,
    this.onSingleTap,
    this.video,
    this.aspectRatio = 9 / 16.0,
    this.hidePauseIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    // 右边的按钮列表
    Widget rightButtons = rightButtonColumn ?? Container();
    // 用户信息
    Widget userInfo = userInfoWidget ?? Container();
    Widget slider = sliderWidget ?? Container();
    // 视频播放页
    Widget videoContainer = Stack(
      children: <Widget>[
        Container(
          height: double.infinity,
          width: double.infinity,
          color: Colors.transparent,
          alignment: Alignment.center,
          child: AspectRatio(
            aspectRatio: aspectRatio,
            child: video,
          ),
        ),
        Offstage(
          offstage: !showRenderCover,
          child: Container(
            color: Colors.black,
            child: Image.network(
              videoImage,
              fit: BoxFit.contain,
              alignment: Alignment.center,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
        ),
        Offstage(
          offstage: !showRenderCover,
          child: const VideoFlickrIndicator(),
        ),
        TikTokVideoGesture(
          onAddFavorite: onAddFavorite,
          onSingleTap: onSingleTap,
          child: Container(
            color: Colors.transparent,
            height: double.infinity,
            width: double.infinity,
          ),
        ),
        if (!hidePauseIcon)
          IgnorePointer(
            child: Container(
              height: double.infinity,
              width: double.infinity,
              alignment: Alignment.center,
              child: Image.asset(
                "assets/images/tiktok/btn_play.png",
                width: 80.gw,
                height: 80.gw,
              ),
            ),
          ),
      ],
    );
    Widget body = Stack(
      children: <Widget>[
        videoContainer,
        Positioned(right: 10.gw, bottom: 49.gw, child: rightButtons),
        Container(
          height: double.infinity,
          width: double.infinity,
          alignment: Alignment.bottomLeft,
          child: userInfo,
        ),

        /// 用户中奖记录
        BlocBuilder<VideoHomeCubit, VideoHomeState>(
          builder: (context, state) {
            if (state.winnerList.isEmpty) {
              return Container();
            }
            return Positioned(
              left: 10.gw,
              bottom: 55.gw,
              child: HomeWinnerListView(
                width: GSScreenUtil().screenWidth - 80,
                dataList: state.winnerList,
                onTapCellGame: (model) {
                  if (int.tryParse(model.gameId) != null && int.tryParse(model.thirdPlatformId) != null) {
                    AuthUtil.checkIfLogin(() {
                      /// 这里只有三方游戏，没有彩票
                      GameUtil().fetchGameLoginData(
                        gameId: int.parse(model.gameId),
                        platformId: int.parse(model.thirdPlatformId),
                      );
                    });
                  }
                },
              ),
            );
          },
        ),

        /// 未登录按钮
        BlocBuilder<VideoHomeCubit, VideoHomeState>(
            buildWhen: (previous, current) => current.isShowUnlockVideoBanner != previous.isShowUnlockVideoBanner,
            builder: (context, state) {
              if (state.isShowUnlockVideoBanner) {
                return Positioned(
                  bottom: 15.gw,
                  left: 0,
                  child: TikTokLimitBanner(
                    onClickPlay: () {
                      sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
                    },
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

        if (kDebug)
          Center(
            child: Text(
              tag!,
              style: TextStyle(color: Colors.red, fontSize: 55.fs, fontWeight: FontWeight.w700),
            ),
          ),

        /// 播放进度条
        Positioned(
          bottom: 0,
          left: 4.gw,
          right: 4.gw,
          child: slider,
        ),

        BlocSelector<UserCubit, UserState, bool>(
            selector: (state) => state.isLogin,
            builder: (context, isLogin) {
              return !isLogin
                  ? Positioned(
                      bottom: 6.gw,
                      left: 0,
                      right: 0,
                      child: TikTokUnLoginBanner(
                        onClickLogin: () {
                          AuthUtil.checkIfLogin(() {});
                        },
                      ))
                  : const SizedBox.shrink();
            }),
      ],
    );
    return body;
  }

  _buildLoginTipsWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text("login_unlock_video_tips_b".tr(), style: TextStyle(fontSize: 13.fs, color: Colors.white.withOpacity(0.7))),
        SizedBox(height: 8.gw),
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                AuthUtil.checkIfLogin(() {});
              },
              child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 2.gw),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xB2FFFFFF), // 边框颜色：#FFFFFFB2，带有透明度
                      width: 1.5, // 边框宽度
                    ),
                    borderRadius: BorderRadius.circular(5.gw), // 设置圆角
                  ),
                  child:
                      Text('login_now'.tr(), style: TextStyle(fontSize: 16.fs, color: Colors.white.withOpacity(0.7)))),
            ),
          ],
        )
      ],
    );
  }
}

class VideoUserInfo extends StatelessWidget {
  final String desc;

  // final Function onGoodGift;
  const VideoUserInfo({
    super.key,
    required this.bottomPadding,
    required this.desc,
  });

  final double bottomPadding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 14.gw,
        bottom: bottomPadding,
      ),
      margin: EdgeInsets.only(right: 80.gw),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            desc,
            style: TextStyle(fontSize: 16.fs, color: Colors.white),
          ),
        ],
      ),
    );
  }
}
