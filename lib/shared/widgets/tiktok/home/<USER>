import 'package:flutter/material.dart';
import 'package:wd/core/config/bottom_nav_config.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class TikTokTabBar extends StatelessWidget {
  final List<BottomNavConfig> data;
  final ValueChanged<BottomNavConfig> onTabSwitch;
  final BottomNavType currentTabType;
  final bool isWhiteColor;

  const TikTokTabBar({
    super.key,
    required this.data,
    required this.onTabSwitch,
    required this.currentTabType,
    required this.isWhiteColor,
  });

  bool get currentIsTiktokPage => currentTabType == BottomNavType.videoHome && !isWhiteColor;

  List<Widget> _buildBottomNavigationBarItems() {
    return data.map((model) {
      final isSel = currentTabType == model.type;
      return Expanded(
        child: TabBarItem(
          model: model,
          isSel: isSel,
          currentIsTiktokPage: currentIsTiktokPage,
          onTap: () => onTabSwitch.call(model),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final double paddingBottom = MediaQuery.of(context).padding.bottom;
    return Container(
      height: 49.gw + paddingBottom,
      padding: EdgeInsets.only(bottom: paddingBottom),
      color: context.theme.bottomNavigationBarTheme.backgroundColor,
      alignment: Alignment.center,
      child: Row(children: _buildBottomNavigationBarItems()),
    );
  }
}

class TabBarItem extends StatelessWidget {
  final BottomNavConfig model;
  final bool isSel;
  final bool currentIsTiktokPage;
  final GestureTapCallback? onTap;

  const TabBarItem({
    super.key,
    required this.model,
    required this.isSel,
    required this.currentIsTiktokPage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textSelColor = model.type == BottomNavType.debugUIKit ? Colors.blueAccent : context.theme.bottomNavigationBarTheme.selectedItemColor;
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIcon(),
          const SizedBox(height: 1),
          Text(
            model.title,
            style: TextStyle(
                color: isSel
                    ? textSelColor
                    : context.theme.bottomNavigationBarTheme.unselectedItemColor,
                fontSize: 12.gw),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return SizedBox(
      width: model.size.width,
      height: model.size.height,
      child: Center(
        child: Image.asset(isSel ? model.selectIcon : model.normalIcon),
      ),
    );
  }
}
