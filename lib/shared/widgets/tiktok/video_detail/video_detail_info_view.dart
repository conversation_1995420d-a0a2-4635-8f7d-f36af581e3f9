import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:like_button/like_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

typedef OnClickLikeButton = Future<bool?> Function();

class VideoDetailInfoView extends StatefulWidget {
  final VideoDetailEntity model;
  final OnClickLikeButton onClickLike;
  final VoidCallback onClickShare;

  const VideoDetailInfoView({super.key, required this.model, required this.onClickLike, required this.onClickShare});

  @override
  State<StatefulWidget> createState() => _VideoDetailInfoViewState();
}

class _VideoDetailInfoViewState extends State<VideoDetailInfoView> {
  final Color likeColor = const Color(0xffd0316c);
  late int _likeCount = widget.model.likes;
  late bool _isLiked = widget.model.isLiked;

  @override
  Widget build(BuildContext context) {
    final tags = [
      if (widget.model.videoTags.isNotEmpty) widget.model.videoTags,
      if (widget.model.videoCategory.isNotEmpty) widget.model.videoCategory,
    ];

    final result = tags.join(', ');

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.5.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(12.gw)),
        color: context.colorTheme.highlightForeground,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: AneText(widget.model.videoTitle, style: context.textTheme.secondary)),
              SizedBox(width: 10.gw),
              _getLikeBtn(),
              SizedBox(width: 11.gw),
              _getShareBtn(),
            ],
          ),

          // 标签
          AneText(
            result,
            style: context.textTheme.title.fs12,
          ),
        ],
      ),
    );
  }

  _getLikeBtn() {
    return Container(
      height: 32.gw,
      padding: EdgeInsets.symmetric(horizontal: 10.gw),
      decoration: BoxDecoration(color: context.colorTheme.foregroundColor, borderRadius: BorderRadius.circular(18.gw)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          LikeButton(
              onTap: (isLiked) async {
                final flag = await widget.onClickLike();
                if (flag == true) {
                  setState(() {
                    _isLiked = !_isLiked;
                    _likeCount = isLiked ? _likeCount - 1 : _likeCount + 1;
                  });
                }

                return flag;
              },
              size: 21.gw,
              isLiked: _isLiked,
              circleColor: CircleColor(start: likeColor, end: likeColor),
              likeBuilder: (isLike) {
                return isLike == true
                    ? Image.asset('assets/images/tiktok/btn_like_heart.png')
                    : Image.asset(
                        'assets/images/tiktok/btn_like_heart.png',
                        color: const Color(0xffb7bccb),
                      );
              },
              bubblesColor: BubblesColor(
                dotPrimaryColor: likeColor,
                dotSecondaryColor: likeColor,
                dotThirdColor: likeColor,
                dotLastColor: likeColor,
              )),
          SizedBox(width: 3.gw),
          AneText(_likeCount.likesString(), style: context.textTheme.secondary)
        ],
      ),
    );
  }

  _getShareBtn() {
    return InkWell(
      onTap: widget.onClickShare,
      child: SizedBox(
        width: 32.gw,
        height: 32.gw,
        child: Image.asset(
          "assets/images/tiktok/btn_video_detail_share.png",
          width: 32.gw,
          height: 32.gw,
        ),
      ),
    );
  }
}
