import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:wd/core/models/entities/winner_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/screenUtil.dart';

class HomeWinnerListView extends StatefulWidget {
  final double width;
  final List<WinnerEntity> dataList;
  final void Function(WinnerEntity) onTapCellGame;

  const HomeWinnerListView({
    super.key,
    required this.width,
    required this.dataList,
    required this.onTapCellGame,
  });

  @override
  State<StatefulWidget> createState() => _HomeWinnerListViewState();
}

class _HomeWinnerListViewState extends State<HomeWinnerListView> with SingleTickerProviderStateMixin {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  late ScrollController _scrollController;
  double _scrollOffset = 0;
  List<WinnerEntity> _displayedItems = [];
  Timer? _insertionTimer;
  bool _showNewMessageButton = false;
  bool _isAtBottom = true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    _scrollController.addListener(() {
      if (!mounted) return;
      
      setState(() {
        _scrollOffset = _scrollController.offset;
        // 使用 pixels 和 viewportDimension 来判断是否在顶部
        final double pixels = _scrollController.position.pixels;
        final double minScrollExtent = _scrollController.position.minScrollExtent;
        final double maxScrollExtent = _scrollController.position.maxScrollExtent;

        _isAtBottom = pixels <= minScrollExtent + 10.0;
        _showNewMessageButton = !_isAtBottom && _displayedItems.isNotEmpty;
      });
    });

    _displayedItems = [];
    _startInsertingItems();
  }

  void _startInsertingItems() {
    _insertionTimer?.cancel();

    _insertionTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      if (_isAtBottom && _displayedItems.length < widget.dataList.length) {
        setState(() {
          final newItem = widget.dataList[_displayedItems.length];
          // 检查是否已存在该数据
          // if (!_displayedItems.contains(newItem)) {
            _displayedItems.insert(0, newItem);
            _listKey.currentState?.insertItem(
              0,
              duration: const Duration(milliseconds: 700),
            );


          //   // 当列表超过40条时，只保留前20条
          //   if (_displayedItems.length > 40) {
          //     final newList = _displayedItems.sublist(0, 20);
          //     // 移除多余的项
          //     for (var i = 10; i < _displayedItems.length; i++) {
          //       _listKey.currentState?.removeItem(
          //         10,
          //         (context, animation) => const SizedBox(),
          //         duration: const Duration(milliseconds: 0),
          //       );
          //     }
          //     _displayedItems = newList;
          //   }
          // }
        });
      }
    });
  }

  @override
  void didUpdateWidget(HomeWinnerListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 只检查数据是否更新
    if (widget.dataList != oldWidget.dataList && widget.dataList.isNotEmpty) {
      // 如果当前显示的数据超过20条，移除前20条
      if (_displayedItems.length > 20) {
        final newList = _displayedItems.sublist(0, 10);
        // 移除多余的项
        for (var i = 10; i < _displayedItems.length; i++) {
          _listKey.currentState?.removeItem(
            10,
            (context, animation) => const SizedBox(),
            duration: const Duration(milliseconds: 0),
          );
        }
        _displayedItems = newList;
      }
      _startInsertingItems();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _insertionTimer?.cancel();
    super.dispose();
  }

  Widget _buildItem(BuildContext context, int index, Animation<double> animation) {
    final model = _displayedItems[index];

    return AnimatedBuilder(
      animation: _scrollController,
      builder: (context, child) {
        double scale = 1.0;

        try {
          // 获取当前item在可视区域内的位置
          final RenderObject? renderObject = context.findRenderObject();
          if (renderObject != null && renderObject is RenderBox) {
            final RenderAbstractViewport viewport = RenderAbstractViewport.of(renderObject);
            // 获取item在viewport中的位置
            final RevealedOffset offsetToReveal = viewport.getOffsetToReveal(renderObject, 0.0);

            // 计算item在可视区域内的相对位置
            final double viewportDimension = _scrollController.position.viewportDimension;
            final double itemOffset = offsetToReveal.offset - _scrollController.offset;

            // 使用线性插值计算缩放比例
            const double maxScale = 1.0;
            const double minScale = 0.8;
            final double maxDistance = 150.gw;

            if (itemOffset <= maxDistance) {
              // 使用线性插值计算scale
              scale = minScale + (maxScale - minScale) * (1 - itemOffset / maxDistance);
            } else {
              scale = minScale;
            }
                    }
        } catch (e) {
          LogE("HomeWinnerList._buildItem.error: $e");
        }


        return Transform.scale(
          scale: scale,
          alignment: Alignment.bottomLeft,
          child: AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: animation.value,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.0, 1.0),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.linear,
                      ),
                    ),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  ),
                ),
              );
            },
            child: Padding(
              padding: EdgeInsets.only(bottom: 10.gw),
              child: WinnerCell(
                userName: model.nickName,
                gameName: model.gameName,
                amount: model.sendAmount,
                onTapGame: () => widget.onTapCellGame(model),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      child: Column(
        children: [
          ShaderMask(
            shaderCallback: (rect) {
              return LinearGradient(
                colors: [
                  Colors.white.withOpacity(0),
                  Colors.white.withOpacity(0.7),
                  Colors.white,
                  Colors.white,
                ],
                stops: const [0, 0.15, 0.85, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ).createShader(rect);
            },
            child: Stack(
              children: [
                // 底层透明层，用于传递手势到PageView
                Positioned.fill(
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
                // 上层动画列表
                SizedBox(
                  height: 150.gw,
                  child: AnimatedList(
                    key: _listKey,
                    controller: _scrollController,
                    physics: const AlwaysScrollableScrollPhysics(),
                    reverse: true,
                    initialItemCount: _displayedItems.length,
                    itemBuilder: (context, index, animation) {
                      return _buildItem(context, index, animation);
                    },
                  ),
                ),
              ],
            ),
          ),
          if (_showNewMessageButton)
            Align(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: _scrollToTop,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 6.gw),
                  margin: EdgeInsets.symmetric(vertical: 10.gw),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.keyboard_double_arrow_up, size: 16.gw, color: Colors.white),
                      SizedBox(width: 4.gw),
                      Text(
                        '有新消息',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.fs,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
    setState(() {
      _showNewMessageButton = false;
      _isAtBottom = true;
    });
  }
}


class WinnerCell extends StatelessWidget {
  final String userName;
  final String gameName;
  final String amount;
  final GestureTapCallback onTapGame;

  const WinnerCell({
    super.key,
    required this.userName,
    required this.gameName,
    required this.amount,
    required this.onTapGame,
  });

  @override
  Widget build(BuildContext context) {
    double intAmount = double.tryParse(amount) ?? 0.0;
    String winText = "小试牛刀";
    if (intAmount > 10000) {
      winText = "大杀四方";
    } else if (intAmount > 5000) {
      winText = "所向披靡";
    } else if (intAmount > 2000) {
      winText = "披荆斩棘";
    } else if (intAmount > 500) {
      winText = "风生水起";
    } else if (intAmount > 200) {
      winText = "大展拳脚";
    }

    // 处理金额显示，去掉 .00
    final displayAmount = amount.endsWith('.00') ? amount.substring(0, amount.length - 3) : amount;

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 2.gw),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.4),
          borderRadius: const BorderRadius.all(Radius.circular(100)),
        ),
        child: Text.rich(
          TextSpan(children: [
            TextSpan(text: "$userName 刚刚在", style: TextStyle(color: Colors.white, fontSize: 14.fs)),
            TextSpan(
              text: "【$gameName】",
              style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 14.fs),
              recognizer: TapGestureRecognizer()..onTap = onTapGame,
            ),
            TextSpan(text: "$winText，赢了", style: TextStyle(color: Colors.white, fontSize: 14.fs)),
            TextSpan(text: displayAmount, style: TextStyle(color: const Color(0xFFFF3B30), fontSize: 14.fs)),
            TextSpan(text: "元", style: TextStyle(color: const Color(0xFFFF3B30), fontSize: 14.fs)),
          ]),
          softWrap: true,
        ),
      ),
    );
  }
}
