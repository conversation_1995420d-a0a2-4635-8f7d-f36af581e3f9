import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/shared/widgets/button/service_button.dart';


class MineScrollFadeAppBar extends StatefulWidget {
  final ScrollController scrollController;
  final String title;
  final double maxScrollOffset;
  final double height;
  final Color color;
  final TextStyle? titleStyle;

  const MineScrollFadeAppBar({
    super.key,
    required this.scrollController,
    this.title = '我的',
    this.maxScrollOffset = 50.0,
    this.height = 44,
    this.color = Colors.white,
    this.titleStyle,
  });

  @override
  State<StatefulWidget> createState() => _MineScrollFadeAppBarState();

}
class _MineScrollFadeAppBarState extends State<MineScrollFadeAppBar> {

  double _computeOpacity() {
    final offset = widget.scrollController.hasClients ? widget.scrollController.offset : 0;
    return (offset / widget.maxScrollOffset).clamp(0.0, 1.0);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.scrollController,
      builder: (_, __) {
        final opacity = _computeOpacity();
        return Stack(
          children: [
            // Main app bar content that ignores pointer when transparent
            IgnorePointer(
              ignoring: opacity < 0.1,
              child: Container(
                height: widget.height,
                width: double.infinity,
                padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top, left: 16, right: 16),
                decoration: BoxDecoration(
                  color: widget.color.withOpacity(opacity),
                  boxShadow: [
                    if (opacity > 0.2)
                      BoxShadow(
                        color: Colors.black.withOpacity(opacity * 0.1),
                        blurRadius: 4,
                        offset: Offset(0, 4 * opacity), // 往下偏移
                        spreadRadius: 0,
                      ),
                  ],
                ),
                child: Center(
                  child: Text(
                    widget.title,
                    style: widget.titleStyle ??
                        TextStyle(
                          color: context.theme.appBarTheme.titleTextStyle!.color!.withOpacity(opacity),
                          fontSize: 18.fs,
                        ),
                  ),
                ),
              ),
            ),
            // Support button that's always interactive
            Positioned(
              top: MediaQuery.of(context).padding.top,
              right: 16,
              child: SizedBox(
                height: widget.height,
                child: Center(
                  child: ServiceButton(textColor: const Color(0xff6A7391).withOpacity(opacity < 0.1 ? 1.0 : opacity),),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
