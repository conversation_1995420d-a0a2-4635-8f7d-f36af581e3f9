import 'package:flutter/material.dart';

class LocaleEntity {
  final String name;
  final String languageCode;
  final String countryCode;
  final bool isSelected;
  final String flagIcon;

  const LocaleEntity({
    required this.name,
    required this.languageCode,
    required this.countryCode,
    required this.isSelected,
    required this.flagIcon,
  });

  Locale toLocale() => Locale(languageCode, countryCode);

  LocaleEntity copyWith({
    String? name,
    String? languageCode,
    String? countryCode,
    bool? isSelected,
    String? flagIcon,
  }) {
    return LocaleEntity(
      name: name ?? this.name,
      languageCode: languageCode ?? this.languageCode,
      countryCode: countryCode ?? this.countryCode,
      isSelected: isSelected ?? this.isSelected,
      flagIcon: flagIcon ?? this.flagIcon,
    );
  }
}
