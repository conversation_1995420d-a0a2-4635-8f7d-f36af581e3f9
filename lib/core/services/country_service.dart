import '../models/country.dart';
import '../models/apis/user.dart';
import '../models/entities/country_entity.dart';
import '../utils/log_util.dart';

class CountryService {
  static CountryService? _instance;
  static CountryService get instance => _instance ??= CountryService._();
  CountryService._();

  List<Country>? _countries;
  Future<List<Country>>? _loadingFuture;

  /// Get all countries from API
  Future<List<Country>> getCountries() async {
    // Return cached data if available
    if (_countries != null) return _countries!;

    // Return existing loading future if already in progress
    if (_loadingFuture != null) return _loadingFuture!;

    // Start new loading process
    _loadingFuture = _loadCountriesFromApi();
    final result = await _loadingFuture!;
    _loadingFuture = null; // Clear loading future
    return result;
  }

  /// Internal method to load countries from API
  Future<List<Country>> _loadCountriesFromApi() async {
    try {
      // Get from API
      final CountryEntity? countryEntity = await UserApi.fetchAreaCodes();

      if (countryEntity != null && countryEntity.list != null && countryEntity.list!.isNotEmpty) {
        _countries = countryEntity.list!.map((countryList) => Country.fromCountryList(countryList)).toList();

        return _countries!;
      } else {
        _countries = _getDefaultCountries();
        return _countries!;
      }
    } catch (e) {
      LogE('❌ API failed: $e, using default countries');
      // If API fails, return default countries
      _countries = _getDefaultCountries();
      return _countries!;
    }
  }

  /// Get country by area code
  Future<Country?> getCountryByAreaCode(int areaCode) async {
    final countries = await getCountries();
    try {
      return countries.firstWhere((country) => country.areaCode == areaCode);
    } catch (e) {
      return null;
    }
  }

  /// Get country by country code
  Future<Country?> getCountryByCode(String code) async {
    final countries = await getCountries();
    try {
      return countries.firstWhere((country) => country.code.toLowerCase() == code.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  /// Get default country (China)
  Future<Country> getDefaultCountry() async {
    final countries = await getCountries();
    try {
      return countries.firstWhere((country) => country.code == 'CN');
    } catch (e) {
      return const Country(name: 'China', code: 'CN', areaCode: 86, flag: '🇨🇳');
    }
  }

  /// Search countries by name
  Future<List<Country>> searchCountries(String query) async {
    if (query.isEmpty) return await getCountries();

    final countries = await getCountries();
    final lowerQuery = query.toLowerCase();

    return countries.where((country) {
      return country.name.toLowerCase().contains(lowerQuery) ||
          country.code.toLowerCase().contains(lowerQuery) ||
          country.areaCode.toString().contains(query);
    }).toList();
  }

  /// Get default countries list (fallback)
  List<Country> _getDefaultCountries() {
    return const [
      Country(name: 'China', code: 'CN', areaCode: 86, flag: '🇨🇳'),
      Country(name: 'United States', code: 'US', areaCode: 1, flag: '🇺🇸'),
      Country(name: 'United Kingdom', code: 'GB', areaCode: 44, flag: '🇬🇧'),
      Country(name: 'Canada', code: 'CA', areaCode: 1, flag: '🇨🇦'),
      Country(name: 'Australia', code: 'AU', areaCode: 61, flag: '🇦🇺'),
      Country(name: 'Japan', code: 'JP', areaCode: 81, flag: '🇯🇵'),
      Country(name: 'South Korea', code: 'KR', areaCode: 82, flag: '🇰🇷'),
      Country(name: 'Singapore', code: 'SG', areaCode: 65, flag: '🇸🇬'),
      Country(name: 'Hong Kong', code: 'HK', areaCode: 852, flag: '🇭🇰'),
      Country(name: 'Taiwan', code: 'TW', areaCode: 886, flag: '🇹🇼'),
    ];
  }
}
