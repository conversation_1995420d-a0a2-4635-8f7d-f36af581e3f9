import 'dart:async';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/global_config.dart';

// Don't touch here, false for release environment（默认为false正式版本，勿手动修改）
// Don't touch here, false for release environment（默认为false正式版本，勿手动修改）
// Don't touch here, false for release environment（默认为false正式版本，勿手动修改）
const kDebug = bool.fromEnvironment('DEBUG', defaultValue: false);

/// flavor
const kChannel = String.fromEnvironment('CHANNEL', defaultValue: 'WD');

enum AppFlavor {
  kWD;

  static AppFlavor fromChannel() {
    switch (kChannel) {
      case 'WD':
        return AppFlavor.kWD;
      default:
        return AppFlavor.kWD;
    }
  }
}


/// 网易验证码appKey
const kWangYiVerityKey = 'ebd8f41476634306aecfdbdec07f869e';

/// 在此处调整支持的语言
const kLocaleList = [
  LocaleEntity(name: "简体中文", languageCode: "zh", countryCode: "CN", isSelected: false, flagIcon: Assets.chineseSimplifiedIcon),
  LocaleEntity(name: "English", languageCode: "en", countryCode: "US", isSelected: false, flagIcon: Assets.englishIcon),
];

// The obtained license URL
const Player_Flutter_LICENSE_URL = "https://license.vod-pro.com/license/v2/1329004948_1/v_cube.license";
// The obtained license key
const Player_Flutter_LICENSE_KEY = "f0e2d08d5f272848b61ad3441d153a86";
Completer<bool> isLicenseSuc = Completer();

/// 游戏场馆图片
String getPlatformCGUrl(code) {
  return "https://zhz-resource.s3.me-central-1.amazonaws.com/platform/cg/$code.png";
}

/// 游戏logo图片
String getPlatformLogoUrl(code, {String? platformName}) {
  if (platformName != null) {
    return "https://zhz-resource.s3.me-central-1.amazonaws.com/platform/logo/$platformName.png";
  }
  return GameUtil().platformInfoMap[code] ?? '';
}

/// 银行卡小图标
String getBankImageStr(bankCode) {
  return "https://zhz-resource.s3.me-central-1.amazonaws.com/bankIcon/$bankCode.png";
}

/// Get Sponsor image url 赞助商
String getSponsorImageUrl(String imageName) {
  return "https://zhz-resource.s3.me-central-1.amazonaws.com/tinified/$imageName";
}

/// 直播url
String getLiveUrl({required String token, required String liveUrl}) {
  token = Uri.encodeComponent(token);
  liveUrl = Uri.encodeComponent(liveUrl);
  if (kDebug) {
    /// 测试环境
    return "https://flutter.184628.com/live/?url=$liveUrl&token=$token";
  }

  /// 生产环境
  return "https://wd676.com/live/?url=$liveUrl&token=$token";
}

/// App下载链接
get kAppDownloadLinkStr {
  String baseUrl = GlobalConfig.getAppDownloadLink();

  return "$baseUrl$kInviteUrlPath";
}

get kInviteUrlPath {
  if (GlobalConfig().inviteCode?.isNotEmpty ?? false) {
    return "/?channel=${GlobalConfig().channelCode}&invite=${GlobalConfig().inviteCode}";
  }
  return "";
}
