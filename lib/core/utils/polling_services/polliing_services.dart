import 'dart:async';
import 'package:wd/core/utils/log_util.dart';

const String kGSOperationWithdrawOrderPolling = "kGSOperationWithdrawOrderPolling"; // 提现订单状态轮询
const String kGSVideoWinnerList = "kGSVideoWinnerList"; // 视频页-赢家列表
const String kGSGameHomeBonusPool = "kGSGameHomeBonusPool"; // 游戏首页-奖金池
const String kGSTencentOnlineSupport = "kGSTencentOnlineSupport"; // 腾讯IM-客服在线状态

/// 全局轮询服务：统一管理轮询任务
///
/// Global polling service: manage polling tasks in a unified way.
class PollingService {
  final Map<String, PollingTask> _tasks = {};

  /// 启动一个轮询任务。
  ///
  /// Start a polling task.
  ///
  /// [id]: 任务唯一标识 Unique task id
  /// [onPoll]: 轮询回调，返回true继续，false停止 Polling callback, return true to continue, false to stop
  /// [interval]: 轮询间隔 polling interval
  /// [shouldStop]: 返回true时立即停止轮询 Return true to stop polling immediately
  /// [shouldPause]: 返回true时本次跳过轮询 Return true to skip this poll
  /// [keepAlive]: 是否常驻，stopAll时不受影响 Whether to keep alive, not affected by stopAll
  void startPolling({
    required String id,
    required Future<bool> Function() onPoll,
    Duration interval = const Duration(seconds: 5),
    bool Function()? shouldStop,
    bool Function()? shouldPause,
    bool keepAlive = false,
  }) {
    if (_tasks.containsKey(id)) {
      _tasks[id]?.stop();
    }

    final task = PollingTask(
      id: id,
      onPoll: onPoll,
      interval: interval,
      shouldStop: shouldStop,
      shouldPause: shouldPause,
      keepAlive: keepAlive,
    );
    _tasks[id] = task;
    task.start();
  }

  /// 停止指定任务。
  ///
  /// Stop a specific polling task by id.
  void stopPolling(String id) {
    _tasks[id]?.stop();
    _tasks.remove(id);
  }

  /// 停止所有除保持常驻的任务。
  ///
  /// Stop all polling tasks except those with keepAlive=true.
  void stopAll() {
    final idsToRemove = <String>[];
    for (final entry in _tasks.entries) {
      if (!entry.value.keepAlive) {
        entry.value.stop();
        idsToRemove.add(entry.key);
      }
    }
    for (final id in idsToRemove) {
      _tasks.remove(id);
    }
  }

  /// 检查某个任务是否正在运行。
  ///
  /// Check if a polling task is running by id.
  bool isPolling(String id) => _tasks[id]?.isRunning ?? false;

  /// 重启任务（用于重新进入页面等场景）。
  ///
  /// Restart a polling task (useful for re-entering a page, etc).
  /// 返回true表示重启成功，false表示未找到 Return true if restarted, false if not found
  bool restartPolling(String id) {
    final task = _tasks[id];
    if (task != null) {
      task.stop();
      task.start();
      return true;
    }
    return false;
  }
}

/// 单个轮询任务的定义。
///
/// Definition of a single polling task.
class PollingTask {
  final String id; // 任务唯一标识 Task id
  final Duration interval; // 轮询间隔 Polling interval
  final Future<bool> Function() onPoll; // 轮询回调 Polling callback
  final bool Function()? shouldStop; // 停止条件 Stop condition
  final bool Function()? shouldPause; // 暂停条件 Pause condition
  final bool keepAlive; // 是否常驻 Keep alive flag

  Timer? _timer;

  PollingTask({
    required this.id,
    required this.onPoll,
    this.interval = const Duration(seconds: 5),
    this.shouldStop,
    this.shouldPause,
    this.keepAlive = false,
  });

  /// 启动轮询。
  ///
  /// Start polling.
  void start() {
    // 立即执行一次 Immediately execute once
    _tick();
    _timer = Timer.periodic(interval, (_) => _tick());
  }

  /// 停止轮询。
  ///
  /// Stop polling.
  void stop() {
    _timer?.cancel();
    _timer = null;
  }

  /// 是否正在运行。
  ///
  /// Whether the polling task is running.
  bool get isRunning => _timer?.isActive ?? false;

  /// 触发执行。
  ///
  /// Trigger polling execution.
  void _tick() async {
    if (shouldStop?.call() == true) {
      stop();
      return;
    }
    if (shouldPause?.call() == true) {
      return;
    }
    try {
      final flag = await onPoll();
      if (!flag) stop();
    } catch (e, s) {
      LogE('PollingTask[[33m$id[0m] onPoll error: $e\n$s');
    }
  }
}
