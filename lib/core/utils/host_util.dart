import 'package:basic_utils/basic_utils.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/extension/future_list_extensions.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

import 'log_util.dart';

// RSA 公钥（PEM 格式）
const String publicKeyBase64 = kDebug
    ? """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtsaobhul1CQo0YsFnG2cC3PZ06NfiAO5kDhl+RhBVA9X6fE6tdG6Dv4Ta8n+Dfn6fR+tpGMti6LJc3pDaS6F6h8gkvi02IM7cqzKEwj4iGZe8rJULYdmChnmzVyOk1m9rLjy73zTy1nYntugpSGM4lp5d5Ji9hA/s4e6Z2786ugnPXIxL7XDHDmMuZjRo2nSZImc7/QXjLSof0l6Eu3TJ7H1A3Nf9UVbu2zS4Q5Q3U3Q63eCMIZnR7Cq168hwzSrl+I7sxqm/MNz9VVLowZ1x0ATUCIk2xvs7t0scdxryUMqul/W/pTNvmWXoklDXuyeXA3K67KMXGfP+pr6FnymXwIDAQAB
-----END PUBLIC KEY-----"""
    : """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyhCKqO0w4lggX1nNqRXOpbny08XnXweLP681TCNih6R+ANL+InG51mPzz4g2UI0XiHWyDShMjBQoSfYTcWezF/qDOJm/2gmJ/linvBWCfxnQanFXquv8lhFXirYmhpwHGx9BT+q3n+v8yErckzcu3JTVSdD5qIUDg5A4t4M1bJoJElOroGdrK8Tsv+4lC/xqF2ITxoh8RDjKJ4k/+XAHrr9HrY4BOEbEnNPDuo2ySoKYTzFaeaUZw3AEkhBC4eiq6zfJSn+I2QzHWTD3mPThG0qaYzFWnNklhIS9IUsz7AnvY/jykmvF0nnA4wDqGwXEiFtxVuNGURmwiUou7q0u+wIDAQAB
-----END PUBLIC KEY-----""";

// RSA 私钥（PEM 格式）
const String privateKeyBase64 = kDebug
    ? """-----BEGIN RSA PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2xqhuG6XUJCjRiwWcbZwLc9nTo1+IA7mQOGX5GEFUD1fp8Tq10boO/hNryf4N+fp9H62kYy2LoslzekNpLoXqHyCS+LTYgztyrMoTCPiIZl7yslQth2YKGebNXI6TWb2suPLvfNPLWdie26ClIYziWnl3kmL2ED+zh7pnbvzq6Cc9cjEvtcMcOYy5mNGjadJkiZzv9BeMtKh/SXoS7dMnsfUDc1/1RVu7bNLhDlDdTdDrd4IwhmdHsKrXryHDNKuX4juzGqb8w3P1VUujBnXHQBNQIiTbG+zu3Sxx3GvJQyq6X9b+lM2+ZZeiSUNe7J5cDcrrsoxcZ8/6mvoWfKZfAgMBAAECggEADm2pPyPsl84Q8NW2KC6Y6miHUgcsE+sjW2iwWBUblxys9nlGBrNbKjLRaqlj5kuNBBKbnQX9QvAjUfvdVXDIPn6djiyW7h6R81ADlbQYFoBqT2NXdM6i9fosd5quYKBtcjSZbOFgQMXDbWruCAsZeYbl3tQN/tmyhaQZyW+ABLUTuCOHcmPiBEVbUas5A8yeIFmdv/BRBYIMtgQlrrxGLZwKW/Xh2qyTkP8F15yolw6fVG7mLGyY31QJ3GYe/WuCqkuz4Ir/ee9nngiiPSj7uOnxwBKeH+zM/HNo5YXthruo/2xM9sYU9lMjGT7qCDaxdfg8TR5wdgUSz+n7nOdyjQKBgQDbdnW0Li8Uy2VRAISeWQhfNansv6tlGyIWDFxYSuUlzb+8XlvSK9w+pM0Lhgad9OXyIFRDAFRwVxLn3L5JiDSroPrxqtye2g4oCkQT+MiO34bXGCSiWypbpxmpi/+XkAMTZ4ozF/O9VquZtPhXfyJZOmlkDm7vCDDUwubQf06zhQKBgQDVNJp7rIXYJV1SQtyyNpS3yvSqACJAMjsZohDZS3AgIf6xSMUGhbirmHzaDbAto7zWjUXOLY8mq34096BXZkwstOZXKGM985s3aRzVGu2fKPVl+0GpqdWp3GQQ93y7DbiLwLisbtp6NE8QJH0rjWokC0Qk4tGtF1+weWcKvledkwKBgDuObSjFc79n5wnRYyv5q1zzGjcGWMPqVhi3qAPJ51+X81VyIebqQi252W8sIvWQtSo8/cDlAQUvjBDiBb28udZtrEwS+yYctMTG7GyjEgI7xMLwaVip9tfChCNPu7/ss/j7GzT8ZgfW0mC3DMHp3W9re5hMlmxFw8KpVav1MG1lAoGAUVfciMrt1eWLkVMJOMUyQF0is6mRZ8TCX6lBnGX3xYqyzZjh8vcZ2f43xEnsqb0K0yDbPSqMr0hGwpanijDxZy0tpJs4Ie7LjXyBD51Sg6Lw3ZPbHYw6hLAdTYve/s7hVhCVZNR2bqF+eL4zM2ak1FOoEBqpvMxqKb5n3eV5MBkCgYEAzHTFtoKe/Ir54F6O6PyTYXh3aiq1ENNNHhUmxf8tScz1hLVxvg+EpvXUh/ngqY8q+0E6lROvDKrLVKQ2sldhsaiGOwIEQfNZlGT4ejTWszRMC5bTZqeSPYre2e8KR/Pj3Pay1RolYhksJEHMRHbzjxRsSUrp5WGwlMrxtutd9OQ=
-----END RSA PRIVATE KEY-----"""
    : """-----BEGIN RSA PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDKEIqo7TDiWCBfWc2pFc6lufLTxedfB4s/rzVMI2KHpH4A0v4icbnWY/PPiDZQjReIdbINKEyMFChJ9hNxZ7MX+oM4mb/aCYn+WKe8FYJ/GdBqcVeq6/yWEVeKtiaGnAcbH0FP6ref6/zIStyTNy7clNVJ0PmohQODkDi3gzVsmgkSU6ugZ2srxOy/7iUL/GoXYhPGiHxEOMoniT/5cAeuv0etjgE4RsSc08O6jbJKgphPMVp5pRnDcASSEELh6KrrN8lKf4jZDMdZMPeY9OEbSppjMVac2SWEhL0hSzPsCe9j+PKSa8XSecDjAOobBcSIW3FW40ZRGbCJSi7urS77AgMBAAECggEAT16hAIqulXHVQW7mZCC/O4SnV7zYTZEKhtjdAAu0I6Cep9c44LG4M/it55pS72AEWuMULavjKXCvhKHGcFix1dzeV0i5Pb1aeVA8jpoKWL98iJr9uxMX86BbOQ6kYmJ+Pg6QWoJRX11UPR5+tsrJuvYxM14E+sdIopBvXtIIlZ/V/ioRQoACERBT2OO0di92NEUA+rOVMDQUsfNrU/AXgtfQncP5IvngyGUMk9wBPJSHIDnOJvbJYuJI4zjBrUUkNlD9D76xtYp5xA1Jd1AHPanAntF+o7eNJKXgHjkWQf5c1rJ6M+eyoW8PoT+20/t42H5W/CYsy7GTrZbTKPXfZQKBgQD8trX06/3RU8WF7TFss2wMVmDkKjYudW+AFUuxHzJR/zWv78RejTdSItrbKNvS6GPfKcjHER5VNIcnp4KrFxWHwJgQdukzKkn2OTP1fBpjIvC176igZ1F0C+LXxPbNtdNX8rI9OLsN7Kyecv5UmqOJB07gxIxxX599zJ+01gFvNwKBgQDMsTgOKYhrMpq3ziRqFSt6md2L/9Jv0hC6SpDXdbOVhn4ggRg2yMPM+FIXohAY3hFC15UYHzNBEYad+0I38MkHYGmZdri70l71rRy7QteSohipo1JmRZ5bxXHUsUUTI2021atRwYNs1EblZ3gdLyw60Xl70Tj5O4qVUQuCYyp4XQKBgQDbAg0WwR4qtwrLUhAvQB83YfR+SAbEpImI2uvKCFs3rJD+dDAeMQvsZsB8hiBb24IR+MhDnJZJCUjIGOepFkxWZZZjTmozKUcH1/Mrn+3OVI1/Xr8oo+F3TIW+sLei9xy9b1sAq5WQL7g5aTb4YzuumWLGcA+2gURbZWZS9dQwaQKBgD3d0nN1WiYMRXcIdzkY7OtBVhTaXWAuvZ+QqfZ1PLIU1Eo+unLUe1mYNDewY+0gMVdikU2ZIISqJ2XYa0s9G4V/PmuD1pHuI4VdrTFsInvPQmUDWARRRmu2YZFHgvkl2/yW2BD9zBs4jbUIcOVhPJ0Kcw1ZTz8FH/3BYRknn86BAoGBAI/TE6pJc4Qw4MufdlkRBNU1eVw5g5bRDeSoJP6/nGGnxLzaCAmkLPc0ySUu+P4rVo2ZEd21OnkKeyROSw09Cod2rAJUIzzgsLGEgviAZugiP6P+K2EQMXLWDleyV6LmsRq3tDWREyOeEgsVq+xWp2se0PBwITmM/msMVeZXqreg
-----END RSA PRIVATE KEY-----""";

class HostUtil {
  // 单例模式
  static final HostUtil _instance = HostUtil._internal();

  factory HostUtil() => _instance;

  HostUtil._internal();

  List<String> hostList = [];

  final _domainChecker = DomainChecker();

  Future<String?> fetchHostFromOss({int timeout = 15}) async {
    if (kIsWeb) return null;

    try {
      final ossUrls = getOssUrls();
      return await _domainChecker.findFastestDomain(ossUrls);
    } catch (e) {
      LogE("fetchHostFromOss error: $e");
      return null;
    }
  }

  List<String> getOssUrls() {
    String channel = const String.fromEnvironment('CHANNEL', defaultValue: 'YL');
    switch (channel) {
      case "YL":
        if (kDebug) {
          return [
            "https://dns-1337532089.cos.accelerate.myqcloud.com/dns-1337532089/domain/test/cryptoData",
            "https://sh-1337532089.cos.ap-shanghai.myqcloud.com/sh-1337532089/domain/test/cryptoData",
            "https://xg-1337532089.cos.ap-hongkong.myqcloud.com/xg-1337532089/domain/test/cryptoData",
            "https://gz-1337532089.cos.ap-guangzhou.myqcloud.com/gz-1337532089/domain/test/cryptoData",
            "https://bj-1337532089.cos.ap-beijing.myqcloud.com/bj-1337532089/domain/test/cryptoData",
          ];
        }
        return [
          "https://dns-1337532089.cos.ap-chongqing.myqcloud.com/dns-1337532089/domain/prod/cryptoData",
          "https://bj-1337532089.cos.ap-beijing.myqcloud.com/bj-1337532089/domain/prod/cryptoData",
          "https://gz-1337532089.cos.ap-guangzhou.myqcloud.com/gz-1337532089/domain/prod/cryptoData",
          "https://sh-1337532089.cos.ap-shanghai.myqcloud.com/sh-1337532089/domain/prod/cryptoData",
          "https://xg-1337532089.cos.ap-hongkong.myqcloud.com/xg-1337532089/domain/prod/cryptoData",
        ];
      case "JS":
        return ["https://dns-1337532089.cos.accelerate.myqcloud.com/dns-1337532089/domain/jswjs01/cryptoData"];
      case "JS2":
        return ["https://dns-1337532089.cos.accelerate.myqcloud.com/dns-1337532089/domain/jsdcp02/cryptoData"];
    }
    return ["https://dns-1337532089.cos.accelerate.myqcloud.com/dns-1337532089/domain/prod/cryptoData"];
  }

  void dispose() {
    _domainChecker.dispose();
  }
}

class _DomainResult {
  final String domain;
  final bool isSuccess;

  _DomainResult(this.domain, this.isSuccess);

  @override
  String toString() {
    return "domain: $domain, isSuccess: $isSuccess";
  }
}

class DomainChecker {
  String? _lastSuccessfulHost;

  // 持久化存储key
  static const String _lastHostKey = 'last_successful_host_${kChannel}_$kDebug';

  // 从持久化存储加载上次成功的host
  Future<void> _loadLastSuccessfulHost() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lastSuccessfulHost = prefs.getString(_lastHostKey);
      LogI("Loaded last successful host: $_lastSuccessfulHost");
    } catch (e) {
      LogE("Failed to load last successful host: $e");
    }
  }

  // 保存成功的host
  Future<void> _saveLastSuccessfulHost(String host) async {
    try {
      _lastSuccessfulHost = host;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastHostKey, host);
      LogI("Saved last successful host: $host");
    } catch (e) {
      LogE("Failed to save last successful host: $e");
    }
  }

  // 检查最后一次成功的host是否仍然可用
  Future<String?> _checkLastSuccessfulHost() async {
    await _loadLastSuccessfulHost();
    LogI("Checking last successful host: $_lastSuccessfulHost");
    if (_lastSuccessfulHost == null) {
      return null;
    }

    try {
      final result = await _checkDomain(_lastSuccessfulHost!).timeout(const Duration(milliseconds: 1500));

      if (result.isSuccess) {
        LogI("Last successful host is still available");
        await _saveLastSuccessfulHost(_lastSuccessfulHost!); // 更新时间戳
        return _lastSuccessfulHost;
      } else {
        LogI("Last successful host is no longer available");
        return null;
      }
    } catch (e) {
      LogE("Error checking last successful host: $e");
      return null;
    }
  }

  // 并发限制数和超时时间
  static const int _maxConcurrent = 3;
  static const Duration _checkTimeout = Duration(milliseconds: 1500);

  Future<String?> findFastestDomain(List<String> ossUrls) async {
    try {
      // 1. 尝试使用上次成功的host
      final lastHost = await _checkLastSuccessfulHost();
      if (lastHost != null) {
        return lastHost;
      }

      // 2. 如果上次host不可用,从OSS获取新域名
      LogI("💢Host Util.findFastestDomain >>> Last host failed, fetching from OSS");

      final domains = await _fetchDomainsFromOss(ossUrls);
      LogI("☺️Host Util. domains >>> $domains");
      if (domains == null || domains.isEmpty) {
        return null;
      }

      // 3. 尝试新域名列表(优先尝试前几个)
      final result = await _tryNewDomains(domains);
      if (result != null) {
        await _saveLastSuccessfulHost(result);
      }
      return result;
    } catch (e) {
      LogE("💢Host Util.findFastestDomain >>> findFastestDomain error: $e");
      return null;
    }
  }

  Future<String?> _tryNewDomains(List<String> domains) async {
    // 只取前几个域名优先尝试
    final priorityDomains = domains.take(_maxConcurrent).toList();
    final backupDomains = domains.skip(_maxConcurrent).toList();

    // 先尝试优先域名组(较短超时)
    final priorityResult = await _tryDomainsWithTimeout(
      priorityDomains,
      timeout: const Duration(seconds: 2),
    );
    if (priorityResult != null) {
      return priorityResult;
    }

    // 如果优先组失败,尝试备用组(较长超时)
    if (backupDomains.isNotEmpty) {
      return await _tryDomainsWithTimeout(
        backupDomains,
        timeout: const Duration(seconds: 4),
      );
    }
    return null;
  }

  Future<String?> _tryDomainsWithTimeout(List<String> domains, {Duration timeout = const Duration(seconds: 2)}) async {
    try {
      LogI("开始尝试域名列表: $domains");

      final tasks = domains.map((domain) async {
        try {
          final result = await _checkDomain(domain);
          if (result.isSuccess) {
            LogI("✅ 域名可用: $domain");
            return domain;
          } else {
            LogD("❌ 域名不可用: $domain");
            return null;
          }
        } catch (e) {
          LogE("检查域名失败: $domain, 错误: $e");
          return null;
        }
      }).toList();

      // 使用我们封装的 firstNonNullSuccessful 并加上超时处理
      return await tasks.firstNonNullSuccessful().timeout(timeout, onTimeout: () {
        LogW("所有域名检查超时");
        return null;
      });
    } catch (e) {
      LogE("尝试域名列表失败: $e");
      return null;
    }
  }

  Future<_DomainResult> _checkDomain(String domain) async {
    final url = '$domain/api/user/api/config';
    LogD("url>>$url");
    try {
      final uri = Uri.parse(url);
      final request = await _client.openUrl('OPTIONS', uri);
      request.headers.add('user-agent', 'Mozilla/5.0');

      final response = await request.close().timeout(_checkTimeout);
      await response.drain(); // 确保连接被正确关闭

      return _DomainResult(domain, response.statusCode == 200);
    } catch (e) {
      LogE("Check domain error: $e");
      return _DomainResult(domain, false);
    }
  }

  // HTTP客户端，用于连接复用
  final _client = HttpClient()
    ..connectionTimeout = const Duration(milliseconds: 1500)
    ..maxConnectionsPerHost = 5;

  // 从OSS获取并解析域名列表
  Future<List<String>?> _fetchDomainsFromOss(List<String> ossUrls) async {
    final List<String> allDomains = [];

    try {
      final result = await ossUrls.map((url) => _fetchSingleOssUrl(url)).toList().firstNonNullSuccessful();
      if (result != null) {
        allDomains.addAll(result);
      }
      return allDomains;
    } catch (e) {
      LogE("All OSS requests failed: $e");
      return null;
    }
  }

  // 从单个OSS URL获取域名列表
  Future<List<String>?> _fetchSingleOssUrl(String url) async {
    try {
      LogD("🌕HostUtil._fetchSingleOssUrl >>> url: $url");

      final response = await http.get(Uri.parse(url)).timeout(const Duration(seconds: 3));

      if (response.statusCode == 200) {
        // 解密响应数据
        final decryptedData = _decryptOssResponse(response.body);
        LogD("解密响应数据 >>> $decryptedData");
        final Map<String, dynamic> data = jsonDecode(decryptedData);

        // 转换域名格式
        final List<String> domains = List<String>.from(data['domains']).map((e) {
          if (!e.startsWith('http')) {
            // 不以 http 开头，添加随机前缀
            return "https://${StringUtil.generateRandomString(8)}.$e";
          } else {
            // 已经是 http/https，直接返回
            return e;
          }
        }).toList();
        LogI("Successfully fetched domains from $url: $domains");
        return domains;
      }

      LogE("Failed to fetch domains from $url: ${response.statusCode}");
      return null;
    } catch (e) {
      LogE("Error fetching domains from $url: $e");
      return null;
    }
  }

  // 解密OSS响应数据
  String _decryptOssResponse(String encryptedData) {
    try {
      // 使用 PEM 格式解析密钥
      final privateKey = CryptoUtils.rsaPrivateKeyFromPem(privateKeyBase64);
      final publicKey = CryptoUtils.rsaPublicKeyFromPem(publicKeyBase64);

      final encrypter = encrypt.Encrypter(encrypt.RSA(
        privateKey: privateKey,
        publicKey: publicKey,
        encoding: encrypt.RSAEncoding.PKCS1,
      ));

      final decrypted = encrypter.decrypt64(encryptedData);
      return decrypted;
    } catch (e) {
      LogE("Decryption error: $e");
      rethrow;
    }
  }

  // 清理资源
  void dispose() {
    _client.close();
  }
}
