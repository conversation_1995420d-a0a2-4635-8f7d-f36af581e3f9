import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:openinstall_flutter_plugin/openinstall_flutter_plugin.dart';

class OpenInstallManager {
  // 私有构造函数
  OpenInstallManager._internal();
  
  // 单例实例
  static final OpenInstallManager _instance = OpenInstallManager._internal();
  
  // 获取单例实例
  static OpenInstallManager get instance => _instance;
  
  // OpenInstall插件实例
  late final OpeninstallFlutterPlugin _openInstallFlutterPlugin;
  
  // 是否已初始化
  bool _isInitialized = false;

  // 初始化OpenInstall
  initPlatformState() {
    if (kIsWeb) return;

    if (_isInitialized) {
      LogD("📣OpenInstall已经初始化过，跳过重复初始化");
      return;
    }

    try {
      LogD("📣开始初始化OpenInstall");
      _openInstallFlutterPlugin = OpeninstallFlutterPlugin();
      _openInstallFlutterPlugin.setDebug(kDebug);
      _openInstallFlutterPlugin.init(_handleInstallData);
      
      if (SystemUtil.isFirstInstall) {
        LogD("📣首次安装，执行install");
        _openInstallFlutterPlugin.install(_handleInstallData);
      }
      
      _isInitialized = true;
      LogD("📣OpenInstall初始化完成");
    } catch (e) {
      LogD("📣OpenInstall初始化失败: $e");
      rethrow;
    }
  }

  // 处理安装数据
  Future<void> _handleInstallData(Map<String, Object> data) async {
    LogD("📣收到OpenInstall数据: $data");
    
    if (kDebug) {
      GSEasyLoading.showToast("来自OP消息>> $data", toast: Toast.LENGTH_LONG);
    }

    try {
      final (String? inviteCode, String? channelCode) = _parseInstallData(data);
      
      if (inviteCode?.isEmpty ?? channelCode?.isEmpty ?? true) {
        LogD("📣邀请码和渠道码都为空，跳过处理");
        return;
      }

      _updateConfig(inviteCode!, channelCode);
      _handleUserRegistration();
    } catch (e) {
      LogD("📣处理OpenInstall数据失败: $e");
    }
  }

  // 解析安装数据
  (String? inviteCode, String? channelCode) _parseInstallData(Map<String, Object> data) {
    String? inviteCode;
    String? channelCode;

    void parseDataFromJson(String? jsonString) {
      if (jsonString == null || jsonString.isEmpty) return;

      try {
        final Map<String, dynamic> bindData = jsonDecode(jsonString);
        inviteCode ??= bindData["invite"]?.toString();
        channelCode ??= bindData["channel"]?.toString();
        LogD("📣解析OpenInstall数据成功 - inviteCode: $inviteCode, channelCode: $channelCode");
      } catch (e) {
        LogD("📣解析OpenInstall JSON数据失败: $e");
      }
    }

    parseDataFromJson(data['bindData'] as String?);
    return (inviteCode, channelCode);
  }

  // 更新配置
  void _updateConfig(String inviteCode, String? channelCode) {
    GlobalConfig().inviteCode = inviteCode;
    GlobalConfig().channelCode = channelCode;
    LogD("📣更新配置成功 - inviteCode: $inviteCode, channelCode: $channelCode");
  }

  // 处理用户注册
  void _handleUserRegistration() {
    if (!sl<UserCubit>().state.isLogin) {
      if (GlobalConfig().isSplashPageLoad && 
          !GlobalConfig().isShowingGuidePage && 
          !SystemUtil.isFirstInstall) {
        LogD("📣满足条件，跳转到注册页面");
        sl<NavigatorService>().push(AppRouter.register);
      } else {
        LogD("📣设置需要注册标记");
        GlobalConfig().needToRegister = true;
      }
    } else {
      LogD("📣用户已登录，跳过注册流程");
    }
  }

  /// 上报用户注册成功
  void reportUserRegistration() {
    if (kIsWeb) return;

    return _openInstallFlutterPlugin.reportRegister();
  }
}