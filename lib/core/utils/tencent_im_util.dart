import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/apis/chat.dart';
import 'package:wd/core/models/entities/chat_service_online_entity.dart';
import 'package:wd/core/services/im_url_service_impl.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/polling_services/polliing_services.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services/im_url_service.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class TencentImUtil extends ChangeNotifier {
  static final TencentImUtil _instance = TencentImUtil._internal();

  factory TencentImUtil() {
    return _instance;
  }

  TencentImUtil._internal();

  List<ChatServiceOnlineEntity> _onlineServiceList = [];

  List<ChatServiceOnlineEntity> get onlineServiceList => _onlineServiceList;

  var isInit = false;

  bool isLogin = false;

  static const int MAX_RETRY_COUNT = 3;
  static const Duration INITIAL_RETRY_DELAY = Duration(seconds: 2);

  Future<bool> initSdkCore(
    int sdkAppID,
    String userId,
    String userSig, {
    required String token,
    void Function()? onLoginSuccess,
    void Function(int code, String error)? onConnectFailed,
  }) async {
    try {
      // 1. 清理旧的初始化状态
      if (isInit) {
        await _cleanup();
      }

      // 2. 初始化 SDK
      await _initSDK(sdkAppID, token, onConnectFailed);

      // 3. 登录
      return await _login(userId, userSig, onLoginSuccess, onConnectFailed);
    } catch (e) {
      LogE('初始化失败: $e');
      onConnectFailed?.call(-1, e.toString());
      return false;
    }
  }

  Future<void> _initSDK(
    int sdkAppID,
    String token,
    void Function(int code, String error)? onConnectFailed,
  ) async {
    IMUrlServiceManager.initialize(AppIMUrlService());
    await TIMUIKitCore.getInstance().init(
      sdkAppID: sdkAppID,
      loglevel: kDebug ? LogLevelEnum.V2TIM_LOG_DEBUG : LogLevelEnum.V2TIM_LOG_NONE,
      kDebug: kDebug,
      token: token,
      listener: V2TimSDKListener(
        onConnectFailed: (code, error) {
          LogE("连接失败: $error");
          isInit = false;
          onConnectFailed?.call(code, error);
        },
        onConnectSuccess: () {
          LogD("连接成功");
          isInit = true;
        },
        onUserSigExpired: () {
          LogE("userSig 过期");
          isLogin = false;
          onConnectFailed?.call(-2, "userSig expired");
        },
        onConnecting: () {
          LogD("tencent: onConnecting");
        },
        onKickedOffline: () {
          LogD("tencent: onKickedOffline");

          sl<UserCubit>().setLoginInfo(null);
          isLogin = false;
          logout();
        },
        onSelfInfoUpdated: (V2TimUserFullInfo info) {
          LogD("tencent: onSelfInfoUpdated");
        },
        onUIKitEventEmited: (event) {
          // 处理 UI 事件
          // if (event.type == UIKitEventType.messageSendFailed) {
          //   GSEasyLoading.showToast("消息发送失败");
          // }
          // ... 其他事件处理
        },
      ),
    );
  }

  Future<bool> _login(
    String userId,
    String userSig,
    void Function()? onLoginSuccess,
    void Function(int code, String error)? onConnectFailed,
  ) async {
    final res = await TIMUIKitCore.getInstance().login(
      userID: userId,
      userSig: userSig,
    );

    if (res.code == 0) {
      isLogin = true;
      onLoginSuccess?.call();
      startFetchOnlineServiceUserNoListPolling();
      return true;
    } else {
      LogE("登录失败: ${res.desc}");
      onConnectFailed?.call(res.code, res.desc);
      return false;
    }
  }

  Future<void> _cleanup() async {
    if (isLogin) {
      await logout();
    }
    isInit = false;
    isLogin = false;
  }

  Future<void> logout({
    void Function()? onLogoutSuccess,
  }) async {
    if (!isLogin) return;
    final timCoreInstance = TIMUIKitCore.getInstance();
    final res = await timCoreInstance.logout();
    if (res.code == 0) {
      isLogin = false;
      isInit = false;
      log("tencent: LOGOUT SUCCESS ${res.toJson()}");
      onLogoutSuccess?.call();
    }
  }

  /// 获取在线客服列表
  startFetchOnlineServiceUserNoListPolling() async {
    sl<PollingService>().startPolling(
      id: kGSTencentOnlineSupport,
      interval: const Duration(seconds: 20),
      onPoll: () async {
        final res = await ChatApi.fetchTencentAllOnlineList();
        _onlineServiceList = res;
        notifyListeners();
        return true;
      },
      shouldStop: () => !sl<UserCubit>().state.isLogin,
      shouldPause: () => RouteTracker().getCurrentRouteName() != AppRouter.tencentChat,
    );
  }

  /// 通知后端用户点击单聊
  notifyOnlineServiceClick(String userNo) {
    ChatApi.notifyOnlineServiceClick(userNo);
  }
}
