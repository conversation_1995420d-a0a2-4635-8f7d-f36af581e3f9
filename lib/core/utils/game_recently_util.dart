import 'dart:convert';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class GameRecentlyUtil with ChangeNotifier {
  static const int _maxCount = 20;  // 最大数量
  static const String _gameKey = 'recent_games';
  static const String _platformKey = 'recent_platforms';
  
  // 单例实现
  static final GameRecentlyUtil _instance = GameRecentlyUtil._internal();
  factory GameRecentlyUtil() => _instance;
  GameRecentlyUtil._internal();

  List<GameV2> _gameList = [];
  List<GamePlatformV2> _platformList = [];

  List<GameV2> get gameList => _gameList;
  List<GamePlatformV2> get platformList => _platformList;

  // 初始化方法，从本地存储加载数据
  Future<void> init() async {
    await _loadGames();
    await _loadPlatforms();
  }

  onChangeGameFav() {
    notifyListeners(); // 通知监听者
  }

  // 添加最近游戏
  Future<void> addRecentGame(GameV2 game) async {
    // 移除已存在的相同游戏
    _gameList.removeWhere((element) => element.code == game.code);
    // 添加到列表开头
    _gameList.insert(0, game);
    // 确保不超过最大数量
    if (_gameList.length > _maxCount) {
      _gameList = _gameList.sublist(0, _maxCount);
    }

    // LogD("GameRecentlyUtil.gameList.length : ${_gameList.length}");
    // 保存到本地
    await _saveGames();
    notifyListeners(); // 通知监听者
  }

  // 添加最近平台
  Future<void> addRecentPlatform(GamePlatformV2 platform) async {
    // 移除已存在的相同平台
    _platformList.removeWhere((element) => element.code == platform.code);
    // 添加到列表开头
    _platformList.insert(0, platform);
    // 确保不超过最大数量
    if (_platformList.length > _maxCount) {
      _platformList = _platformList.sublist(0, _maxCount);
    }

    // LogD("GameRecentlyUtil.platformList.length : ${_platformList.length}");
    // 保存到本地
    await _savePlatforms();
    notifyListeners(); // 通知监听者
  }

  // 从本地加载游戏列表
  Future<void> _loadGames() async {
    final prefs = await SharedPreferences.getInstance();
    final String? gamesJson = prefs.getString(_gameKey);
    if (gamesJson != null) {
      final List<dynamic> decoded = json.decode(gamesJson);
      _gameList = decoded.map((item) => GameV2.fromJson(item)).toList();
    }
  }

  // 从本地加载平台列表
  Future<void> _loadPlatforms() async {
    final prefs = await SharedPreferences.getInstance();
    final String? platformsJson = prefs.getString(_platformKey);
    if (platformsJson != null) {
      final List<dynamic> decoded = json.decode(platformsJson);
      _platformList = decoded.map((item) => GamePlatformV2.fromJson(item)).toList();
    }
  }

  // 保存游戏列表到本地
  Future<void> _saveGames() async {
    final prefs = await SharedPreferences.getInstance();
    final String encoded = json.encode(_gameList.map((e) => e.toJson()).toList());
    await prefs.setString(_gameKey, encoded);
  }

  // 保存平台列表到本地
  Future<void> _savePlatforms() async {
    final prefs = await SharedPreferences.getInstance();
    final String encoded = json.encode(_platformList.map((e) => e.toJson()).toList());
    await prefs.setString(_platformKey, encoded);
  }

  // 清除所有数据
  Future<void> clear() async {
    _gameList.clear();
    _platformList.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_gameKey);
    await prefs.remove(_platformKey);
    notifyListeners(); // 通知监听者
  }
}