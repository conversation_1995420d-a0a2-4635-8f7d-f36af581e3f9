import 'dart:convert';

import 'package:wd/core/utils/http/aes_cipher.dart';
import 'package:wd/generated/json/base/json_convert_content.dart';

class ResponseModel<T> {
  T? data;
  int code;
  String? msg;

  ResponseModel({
    this.data,
    this.code = 0,
    this.msg,
  });

  ResponseModel.fromJson(Map<String, dynamic> json, {bool needDecrypt = false})
      : code = json['code'] ?? 0,
        msg = json['msg'] {
    if (json['data'] != null && json['data'] != 'null') {
      var processedData = json['data'];

      // Add decryption if needed
      if (needDecrypt && processedData is String) {
        String decryptedStr = AESCipher().decryptString(processedData);
        try {
          // 尝试将解密后的字符串解析为 JSON
          processedData = jsonDecode(decryptedStr);
        } catch (e) {
          // 如果解析失败，保持原始解密字符串
          processedData = decryptedStr;
        }
      }

      if (processedData is List) {
        Map<String, dynamic> wrappedJson = {"list": processedData};
        data = JsonConvert.fromJsonAsT<T>(wrappedJson);
      } else {
        data = JsonConvert.fromJsonAsT<T>(processedData);
      }
      // LogD("解密后数据： $data");
    }
  }

  /// 是否成功
  bool get isSuccess => code == 200;


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data;
    }
    data['code'] = this.code;
    data['msg'] = this.msg;
    return data;
  }
}
