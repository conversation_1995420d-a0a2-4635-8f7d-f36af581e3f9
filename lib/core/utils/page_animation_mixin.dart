// lib/core/animation/page_animation_mixin.dart

import 'package:flutter/material.dart';

mixin PageAnimationMixin<T extends StatefulWidget> {
  late AnimationController pageAnimationController;
  late List<Animation<double>> itemAnimations;
  bool hasPlayedAnimation = false;

  /// Initialize animations - call this in initState
  void initializePageAnimations(TickerProvider vsync, {int itemCount = 5}) {
    pageAnimationController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 800),
    );

    itemAnimations = List.generate(
      itemCount,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: pageAnimationController,
          curve: Interval(
            index * 0.1,
            (index * 0.1) + 0.6,
            curve: Curves.easeOut,
          ),
        ),
      ),
    );
  }

  /// Play the animation
  void playPageAnimation() {
    pageAnimationController.forward(from: 0);
    hasPlayedAnimation = true;
  }

  /// Reset animation state when leaving page
  void resetPageAnimation() {
    hasPlayedAnimation = false;
  }

  /// Call this in dispose
  void disposePageAnimation() {
    pageAnimationController.dispose();
  }

  /// Wrap widget with fade and slide animation
  Widget wrapWithAnimation(Widget child, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.2, 0),
          end: Offset.zero,
        ).animate(animation),
        child: child,
      ),
    );
  }

  /// Check and play animation based on tab index
  // void checkAndPlayAnimation(BuildContext context, int thisPageIndex) {
  //   final currentIndex = context.read<MainScreenCubit>().state.selectedIndex;
  //   if (currentIndex == thisPageIndex && !hasPlayedAnimation) {
  //     playPageAnimation();
  //   } else if (currentIndex != thisPageIndex) {
  //     resetPageAnimation();
  //   }
  // }
  //
  // /// Play initial animation if needed
  // void playInitialAnimation(BuildContext context, int thisPageIndex) {
  //   if (context.read<MainScreenCubit>().state.selectedIndex == thisPageIndex) {
  //     playPageAnimation();
  //   }
  // }
}