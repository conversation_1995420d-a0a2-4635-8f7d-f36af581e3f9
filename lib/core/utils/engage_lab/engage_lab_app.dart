import 'dart:convert';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_plugin_engagelab/flutter_plugin_engagelab.dart';
import 'package:wd/core/models/entities/jump_model.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/injection_container.dart';
import 'dart:io';

import 'package:permission_handler/permission_handler.dart';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:wd/core/models/entities/notification_alert_entity.dart';

class EngageLabUtil {
  Future<void> init({
    required String appKey,
    required String channel,
    bool production = false,
    bool debug = true,
  }) async {

    // 清除角标
    AppBadgePlus.updateBadge(0);

    // FlutterPluginEngagelab.
    // Configure debug mode
    FlutterPluginEngagelab.configDebugMode(debug);

    // Platform-specific initialization
    if (Platform.isIOS) {
      FlutterPluginEngagelab.initIos(
        appKey: appKey,
        channel: channel,
      );
      FlutterPluginEngagelab.checkNotificationAuthorizationIos();
    } else if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      LogD("📣Android SDK版本: ${androidInfo.version.sdkInt}");
      if (androidInfo.version.sdkInt >= 33 && await Permission.notification.isDenied) {
        final flag = await Permission.notification.request();
      }
      FlutterPluginEngagelab.initAndroid();
    }

    // Add event handler
    FlutterPluginEngagelab.addEventHandler(
      onMTCommonReceiver: (Map<String, dynamic> message) async {
        String eventName = message["event_name"];
        String eventData = message["event_data"];

        if (eventName == "onConnectStatus" || eventName == "networkDidLogin") {
          final rid = await getRegistrationID();
          sl<UserCubit>().state.remotePushDeviceToken = rid;

          LogD("📣已更新用户Token: $rid");
        } else if (eventName == "onNotificationClicked" || eventName == "didReceiveNotificationResponse") {
          LogD("📣用户点击通知 - 事件名: $eventName");
          LogD("📣通知内容: $eventData");

          try {
            // 解析eventData为Map
            final Map<String, dynamic> data = jsonDecode(eventData);
            final Map<String, dynamic> extras = data['extras'] ?? {};
            LogD("📣解析后的extras数据: $extras");

            // 创建NotificationAlertEntity
            final alertEntity = NotificationAlertEntity()
              ..jumpUrl = extras['jumpUrl'] ?? ''
              ..gameId = int.tryParse(extras['gameId'] ?? '0') ?? 0
              ..jumpStatus = int.tryParse(extras['jumpStatus'] ?? '0') ?? 0
              ..gameBelong = int.tryParse(extras['gameBelong'] ?? '0') ?? 0
              ..platformCode = extras['platformCode'] ?? ''
              ..venueId = int.tryParse(extras['venueId'] ?? '0') ?? 0;

            // 使用JumpModel解析数据
            final jumpModel = JumpModel.fromNotificationAlertEntity(alertEntity);
            LogD("📣解析后的跳转数据: ${jumpModel.toJson()}");

            // 执行跳转
            SystemUtil.onJump(jumpModel);
          } catch (e) {
            LogD("📣解析跳转数据失败: $e");
          }
        }
      },
    );
  }

  Future<String?> getRegistrationID() async {
    return await FlutterPluginEngagelab.getRegistrationId();
  }

  Future<void> setAlias(String alias) async {
    await FlutterPluginEngagelab.setAlias(1, alias); // sequence number is required
  }

  Future<void> setTags(List<String> tags) async {
    await FlutterPluginEngagelab.updateTags({
      "sequence": 1,
      "tags": tags,
    });
  }

  void stopPush() {
    // Note: EngageLab doesn't have direct stopPush method
    // You might want to implement alternative logic here
  }

  void resumePush() {
    // Note: EngageLab doesn't have direct resumePush method
    // You might want to implement alternative logic here
  }
}
