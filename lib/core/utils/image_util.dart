

import 'dart:async';
import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';

class ImageUtil {

  static Future<ui.Image> loadImageAsset(String asset) async {
    final ByteData data = await rootBundle.load(asset);
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromList(Uint8List.view(data.buffer), (ui.Image img) {
      completer.complete(img);
    });
    return completer.future;
  }

  /// 本地文件转base64
  static Future<String> imageToBase64(String imagePath) async {
    // 读取资源文件
    ByteData bytes = await rootBundle.load(imagePath);
    // 转换为Uint8List
    var buffer = bytes.buffer;
    var base64String = base64Encode(Uint8List.view(buffer));
    // 添加前缀
    return 'data:image/png;base64,$base64String';
  }
}