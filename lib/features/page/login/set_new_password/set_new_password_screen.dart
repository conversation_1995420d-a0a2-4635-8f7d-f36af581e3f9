import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/verification_code/verification_code.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';

import '../../../../core/utils/system_util.dart';
import '../forgot/forgot_cubit.dart';

class SetNewPasswordPage extends StatefulWidget {
  final LoginType resetType;
  final String contactInfo; // phone or email

  const SetNewPasswordPage({
    super.key,
    required this.resetType,
    required this.contactInfo,
  });

  @override
  State<SetNewPasswordPage> createState() => _SetNewPasswordPageState();
}

class _SetNewPasswordPageState extends State<SetNewPasswordPage> {
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  Widget build(BuildContext context) {
    return _buildSliverLayout();
  }

  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36;

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _SetPasswordHeaderDelegate(
            minHeight: headerHeight * 0.6,
            maxHeight: headerHeight,
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  SizedBox(height: 20.gw),
                  // Tab bar for phone/email selection
                  Center(
                    child: Container(
                      width: 200.gw,
                      height: 44.gw,
                      decoration: BoxDecoration(
                        color: context.colorTheme.foregroundColor,
                        borderRadius: BorderRadius.circular(12.gw),
                      ),
                      padding: EdgeInsets.all(4.gw),
                      child: Row(
                        children: [
                          _buildCustomTabItem('Phone', Assets.loginTabIcon, widget.resetType == LoginType.phone, () {
                            // Tab switching is handled by parent widget
                          }),
                          _buildCustomTabItem('Email', Assets.registerTabIcon, widget.resetType == LoginType.email, () {
                            // Tab switching is handled by parent widget
                          }),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (widget.resetType == LoginType.phone) ...[
                            _buildPhoneInputField(),
                          ] else ...[
                            _buildEmailInputField(),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputField(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildSetPasswordButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return Text(
      'Set New',
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.w400,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    String subtitle;

    switch (widget.resetType) {
      case LoginType.phone:
        subtitle = 'Password via Phone';
        break;
      case LoginType.email:
        subtitle = 'Password via Email';
        break;
      default:
        subtitle = 'Password';
    }

    return Text(
      subtitle,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 28.gw,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        height: 1.2,
      ),
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField() {
    return PhoneInputField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: _selectedCountry,
      enabled: false, // Read-only since it's already verified
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField() {
    return IconTextfield(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "enter_email".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(Assets.iconMail, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField() {
    return IconTextfield(
      controller: TextEditingController(),
      hintText: "Please enter the code",
      keyboardType: TextInputType.number,
      prefixIcon: _buildVerificationPrefix(),
      suffixIcon: VerificationCode(
        phone: widget.resetType == LoginType.phone ? widget.contactInfo : '',
        email: widget.resetType == LoginType.email ? widget.contactInfo : null,
        isEmailMode: widget.resetType == LoginType.email,
        checkIsBind: false, // For password reset, don't check if phone is bound
        isGradient: false,
        onSmsCode: (smsCode) {
          if (kDebug && smsCode.isNotEmpty) {
            // Auto-fill the verification code in debug mode
          }
        },
        onEmailCode: (emailCode) {
          if (kDebug && emailCode.isNotEmpty) {
            // Auto-fill the email verification code in debug mode
          }
        },
      ),
      suffixPadding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 15.gh),
      onChanged: (value) {
        // Handle verification code change
      },
    );
  }

  /// Builds verification prefix icon
  Widget _buildVerificationPrefix() {
    return Padding(
      padding: EdgeInsets.all(18.gw),
      child: Image.asset(
        Assets.iconLoginShield,
        width: 20.gw,
        height: 24.gw,
      ),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput() {
    return IconTextfield(
      controller: TextEditingController(),
      hintText: "password_must_be_6_22".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput() {
    return IconTextfield(
      controller: TextEditingController(),
      hintText: "confirm_password_placeholder".tr(),
      prefixIcon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
    );
  }

  /// Builds a custom tab item with icon and text
  Widget _buildCustomTabItem(String title, String iconAsset, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 100.gw,
          height: 36.gw,
          decoration: BoxDecoration(
            color: isSelected ? context.colorTheme.tabItemBgA : Colors.transparent,
            borderRadius: BorderRadius.circular(10.gw),
            border: isSelected ? Border.all(color: context.colorTheme.borderE, width: 1) : null,
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                iconAsset,
                width: 16.gw,
                height: 16.gw,
              ),
              SizedBox(width: 6.gw),
              Text(
                title,
                style: context.textTheme.primary.copyWith(
                  fontSize: 14.gw,
                  color: isSelected ? context.colorTheme.textPrimary : context.colorTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the set password button
  Widget _buildSetPasswordButton() {
    return CommonButton(
      title: "Log In",
      textColor: context.colorTheme.btnTitlePrimary,
      onPressed: () {
        // TODO: Implement set new password functionality
      },
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}

class _SetPasswordHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _SetPasswordHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login/bg_login_logo.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.gw,
            left: 15.gw,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                alignment: Alignment.center,
                child: Image.asset(
                  Assets.iconBack,
                  height: 32.gh,
                  width: 32.gw,
                ),
              ),
            ),
          ),
          // Logo and title section
          Positioned.fill(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 90.gh),
                  // WD Logo
                  SvgPicture.asset(
                    Assets.tabLogo,
                    width: 131.gw,
                    height: 60.gh,
                  ),
                  SizedBox(height: 20.gh),
                  // Title
                  if (title != null) title!,
                  SizedBox(height: 8.gh),
                  // Subtitle
                  if (subtitle != null) subtitle!,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _SetPasswordHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
