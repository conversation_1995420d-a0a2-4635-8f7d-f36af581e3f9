import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/lottery.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/features/page/4_mine/bet_records/bet_records_state.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

class BetRecordListCubit extends Cubit<BetRecordListState> {
  BetRecordListCubit() : super(BetRecordListState().init()) {
    fetchLotteryList();
    fetchListData();
  }

  void updateStartDate(DateTime date) {
    emit(state.clone()..startDate = date);
  }

  void updateEndDate(DateTime date) {
    emit(state.clone()..endDate = date);
  }

  void updateSelectedLotteryType(String lotteryId) {
    if (lotteryId == '全部') {
      emit(state.clone()..selectedLotteryType = '全部');
    } else {
      final selectedLottery = state.lotteryList.firstWhere(
        (lottery) => lottery.id.toString() == lotteryId,
        // orElse: () => null,
      );

      emit(state.clone()..selectedLotteryType = selectedLottery.lotteryName);
    }
    fetchListData();
  }

  void updateSelectedStatus(String status) {
    emit(state.clone()..selectedStatus = status);
    fetchListData();
  }

  void updatePageNo(int pageNo) {
    emit(state.clone()..pageNo = pageNo);
  }

  void updatePageNoToNext() {
    emit(state.clone()..pageNo += 1);
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    emit(state.clone()..isNoMoreDataState = isNoMoreDataState);
  }

  Future<void> fetchLotteryList() async {
    try {
      List<LotteryGroup> groupList = await LotteryApi.fetchHomeLotteryList();
      Map<int, Lottery> uniqueLotteries = {};
      for (var group in groupList) {
        for (var lottery in group.list) {
          uniqueLotteries[lottery.id] = lottery;
        }
      }
      List<Lottery> allLotteries = uniqueLotteries.values.toList();
      emit(state.clone()..lotteryList = allLotteries);
    } catch (e) {
      emit(state.clone()..lotteryList = []);
    }
  }

  Future<void> fetchListData() async {
    final startOfDay =
        DateTime(state.startDate.year, state.startDate.month, state.startDate.day, 0, 0, 0);
    final endOfDay =
        DateTime(state.endDate.year, state.endDate.month, state.endDate.day, 23, 59, 59);
    GSEasyLoading.showLoading();
    final result = await LotteryApi.fetchBetRecordList(
      pageNo: state.pageNo,
      pageSize: 20,
      startDate: startOfDay.millisecondsSinceEpoch,
      endDate: endOfDay.millisecondsSinceEpoch,
      win: _getWinStatusFromSelectedStatus(),
      orderStatus: _getOrderStatusFromSelectedStatus(),
      lotteryType: _getLotteryTypeId(),
    );
    GSEasyLoading.dismiss();

    if (result != null) {
      if (result.page.total <= state.dataList!.length) {
        state.isNoMoreDataState = true;
      } else {
        state.isNoMoreDataState = false;
      }

      if (state.pageNo == 1) {
        state.dataList = result.page.records;
      } else {
        state.dataList = List.from(state.dataList!)..addAll(result.page.records);
      }
      state.betAmountToday = result.betAmountToday;
      state.totalWinToday = result.totalWinToday;
      state.totalSendAmount = result.totalSendAmount;
      state.netState = NetState.dataSuccessState;
      if (state.dataList!.isEmpty) state.netState = NetState.emptyDataState;
      emit(state.clone());
    }
  }

  dynamic _getWinStatusFromSelectedStatus() {
    switch (state.selectedStatus) {
      case '全部':
        return '';
      case '已中奖':
        return 1;
      case '未中奖':
        return 2;
      case '等待开奖':
        return 3;
      default:
        return '';
    }
  }

//TODO: implement orderStatus
  int? _getOrderStatusFromSelectedStatus() {
    return null;

    switch (state.selectedStatus) {
      case '全部':
        return 1;
      case '已中奖':
        return 2;
      case '未中奖':
        return 3;
      case '等待开奖':
        return 4;
      default:
        return null;
    }
  }

  int _getLotteryTypeId() {
    if (state.selectedLotteryType == '全部') return 0;
    final lottery = state.lotteryList.firstWhere(
      (l) => l.lotteryName == state.selectedLotteryType,
      orElse: () => Lottery()..id = 0,
    );
    return lottery.id;
  }
}
