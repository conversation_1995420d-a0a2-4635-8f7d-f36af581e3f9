import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/vip_model_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/shared/widgets/vip/vip_level_slider.dart';

import 'vip_center_v2_cubit.dart';
import 'vip_center_v2_state.dart';

class VipCenterV2Page extends BasePage {
  const VipCenterV2Page({super.key});

  @override
  BasePageState<BasePage> getState() => _VipCenterV2PageState();
}

class _VipCenterV2PageState extends BasePageState<VipCenterV2Page> {
  @override
  void initState() {
    pageTitle = "vip_center".tr();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = context.read<VipCenterV2Cubit>();
    return BlocBuilder<VipCenterV2Cubit, VipCenterV2State>(
      builder: (context, state) {
        if (state.netState == NetState.initializeState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.netState == NetState.errorShowRefresh) {
          return NetErrorWidget(title: 'errorVip'.tr(), refreshMethod: cubit.fetchVipInfo());
        } else if (state.netState == NetState.emptyDataState) {
          return EmptyWidget(title: 'emptyVip'.tr());
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              _buildVipSliderSection(state, cubit),
              SizedBox(height: 20.gw),
              if (state.currentIndex < state.dataList!.length) ...[
                _buildVipGiftSection(state),
                SizedBox(height: 13.gw),
              ],
              _buildLevelTable(state.dataList!.cast<VipModel>()),
              SizedBox(height: 30.gw),
              // _buildFooter(state),
              // SizedBox(height: 40.gw),
            ],
          ),
        );
      },
    );
  }

  Container _buildVipSliderSection(VipCenterV2State state, VipCenterV2Cubit cubit) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: VipLevelSlider(
        current: state.currentIndex,
        dataList: state.dataList!.cast<VipModel>(),
        onIndexChanged: (index) {
          cubit.onChangeCurrentIndex(index);
        },
      ),
    );
  }

  _buildVipGiftSection(VipCenterV2State state) {
    if (state.currentIndex >= state.dataList!.length) return const SizedBox.shrink();
    final model = state.dataList![state.currentIndex] as VipModel;
    final updateReward = StringUtil().formatLargeNumber(model.upgradeReward.toString());
    final skipReward = StringUtil().formatLargeNumber(model.skipgradeReward.toString());

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw),
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gw),
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1E170B),
            const Color(0xFF161616).withOpacity(0.41),
            const Color(0xFF161616).withOpacity(0.7),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 74,
            offset: const Offset(0, -16),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.gw),
                decoration: BoxDecoration(
                  color: context.colorTheme.iconBgA,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: SvgPicture.asset(Assets.vipIcon2),
              ),
              SizedBox(width: 10.gw),
              AneText(
                "vip_level_up_reward".tr(),
                style: context.textTheme.secondary.fs16,
              ),
            ],
          ),
          SizedBox(height: 16.gw, width: double.infinity),

          /// type: 0升级奖励 1跳级奖励
          Row(
            children: [
              // 0
              RewardCard(
                title: "level_up_reward".tr(),
                subTitle: "reward_points".tr(),
                points: updateReward,
                imagePath: Assets.reward1,
              ),
              SizedBox(width: 8.gw),
              // 1
              RewardCard(
                title: "level_jump_reward".tr(),
                subTitle: "reward_points".tr(),
                points: skipReward,
                imagePath: Assets.reward2,
              ),
            ],
          )
        ],
      ),
    );
  }

  _buildLevelTable(List<VipModel> items) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw),
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 12.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gw),
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1E170B),
            const Color(0xFF161616).withOpacity(0.41),
            const Color(0xFF161616).withOpacity(0.7),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 74,
            offset: const Offset(0, -16),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.gw),
                decoration: BoxDecoration(
                  color: context.colorTheme.iconBgA,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: SvgPicture.asset(Assets.vipIcon2),
              ),
              SizedBox(width: 10.gw),
              AneText(
                "vip_growth_rules".tr(),
                style: context.textTheme.secondary.fs18,
              ),
            ],
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 3.gw, vertical: 20.gw),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(6.gw))),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(20), // VIP等级
                1: FlexColumnWidth(25), // VIP头衔
                2: FlexColumnWidth(18), // 成长积分
                3: FlexColumnWidth(16), // 升级奖励
                4: FlexColumnWidth(21), // 跳级奖励
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              border: TableBorder.all(
                color: context.colorTheme.btnBgTertiary,
                width: 0.5.gw,
              ),
              children: [
                TableRow(
                  decoration: BoxDecoration(color: context.colorTheme.btnBgPrimary),
                  children: [
                    _tableHeader("vip_level".tr()),
                    _tableHeader("vip_title".tr()),
                    _tableHeader("points".tr()),
                    _tableHeader("level_1_up".tr()),
                    _tableHeader("skip_reward".tr(), isLast: true),
                  ],
                ),
                ...items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;

                  bool isCurrent = item.vipLevel == sl<UserCubit>().state.vipInfo?.vipLevel;
                  bool isOdd = index % 2 == 0;
                  return TableRow(
                    decoration: BoxDecoration(
                      color: context.colorTheme.foregroundColor,
                      border: Border(
                        bottom: BorderSide(
                          color: context.colorTheme.btnBgTertiary,
                          width: 0.5.gw,
                        ),
                      ),
                    ),
                    children: [
                      _buildLevelCell(
                          level: item.vipLevel, totalLevel: items.length, isOdd: isOdd, isCurrentLevel: isCurrent),
                      _tableCell(item.vipTitle),
                      _tableCell(item.growthIntegral.toString()),
                      _tableCell(item.upgradeReward.toString()),
                      _tableCell(item.skipgradeReward.toString(), isLast: true),
                    ],
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _tableHeader(String text, {bool isLast = false}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.gw, horizontal: 2.gw),
      alignment: Alignment.center,
      child: AneText(
        text,
        textAlign: TextAlign.center,
        style: context.textTheme.regular.w600.copyWith(color: context.colorTheme.btnTitlePrimary),
      ),
    );
  }

  /// 构建一个 VIP 等级表格中的单元格，用于展示单行的背景和状态样式。
  ///
  /// [level] 当前行对应的 VIP 等级
  /// [totalLevel] 总的 VIP 等级数
  /// [isOdd] 当前行是否为奇数行（用于交替背景色）
  /// [isCurrentLevel] 当前行是否是用户当前所在等级
  Widget _buildLevelCell(
      {required int level, required int totalLevel, required bool isOdd, required bool isCurrentLevel}) {
    return SizedBox(
      height: 34.gw,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              SvgPicture.asset(
                "assets/images/mine/vip/vip_medal_bg_new.svg",
                width: 25,
                height: 25,
              ),
              Positioned(
                bottom: 7,
                child: AneText(
                  "V$level",
                  style: context.textTheme.regular.fs9.w600.ffAne,
                ),
              )
            ],
          ),
          SizedBox(width: 10.gw),
          AneText(
            "V$level",
            style: context.textTheme.regular.fs12.w600,
          ),
        ],
      ),
    );
  }

  Widget _tableCell(String text, {bool isLast = false}) {
    text = StringUtil().formatLargeNumber(text);
    return Container(
      height: 34.gw,
      decoration: BoxDecoration(
        border: Border(
          right: isLast
              ? BorderSide.none
              : BorderSide(
                  color: context.colorTheme.btnBgTertiary,
                  width: 0.5.gw,
                ),
        ),
      ),
      alignment: Alignment.center,
      child: AneText(
        text,
        textAlign: TextAlign.center,
        style: context.textTheme.title,
      ),
    );
  }
}

class RewardCard extends StatelessWidget {
  const RewardCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.points,
    required this.imagePath,
  });

  final String title;
  final String subTitle;
  final String points;
  final String imagePath;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: CommonCard(
        color: context.colorTheme.foregroundColor,
        radius: 8,
        padding: 6.gw,
        child: Column(
          children: [
            AneText(title, style: context.textTheme.title),
            SizedBox(height: 8.gw),
            CommonCard(
              padding: 6,
              child: Row(
                children: [
                  AppImage(imageUrl: imagePath, width: 48.gw, height: 48.gw),
                  const Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AneText(points, style: context.textTheme.primary.w700),
                      AneText(subTitle, style: context.textTheme.title),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
