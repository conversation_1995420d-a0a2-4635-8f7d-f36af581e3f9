
import 'package:wd/core/base/base_state.dart';

class VipCenterV2State extends BaseState {
  int currentIndex = 0;

  VipCenterV2State init() {
    return VipCenterV2State()
      ..currentIndex = 0
      ..netState = NetState.initializeState
      ..dataList = [];
  }

  VipCenterV2State clone() {
    return VipCenterV2State()
      ..currentIndex = currentIndex
      ..netState = netState
      ..dataList = dataList;
  }
}
