import 'dart:convert';
import 'package:bloc/bloc.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/features/page/4_mine/profile/profile_state.dart';
import 'package:wd/injection_container.dart';

class UserProfileCubit extends Cubit<UserProfileState> {
  UserProfileCubit() : super(UserProfileState.fromUserSingleton());

  Future<void> loadAvatars() async {
    if (state.avatars.isNotEmpty) return; // Avoid reloading if already loaded

    try {
      final String response = await rootBundle.loadString('assets/json/avatars.json');
      final data = json.decode(response);
      final List<String> avatars = List<String>.from(data['avatars']);
      emit(state.copyWith(avatars: avatars));
    } catch (e) {
      LogE('Error loading avatars: $e');
    }
  }

  void updateField(UserFieldType field, String value) {
    try {
      switch (field) {
        case UserFieldType.nickName:
          emit(state.copyWith(nickName: value));
          break;
        case UserFieldType.account:
          emit(state.copyWith(account: value));
          break;
        case UserFieldType.phone:
          emit(state.copyWith(phone: value));
          break;
        case UserFieldType.email:
          emit(state.copyWith(email: value));
          break;
        default:
          throw ArgumentError('Unknown field: $field');
      }
    } catch (e) {
      LogE('Error updating field: $e');
    }
  }

  void setTempAvatar(int index) {
    if (index >= 0 && index < state.avatars.length) {
      emit(state.copyWith(tempAvatarId: index));
    }
  }

  void confirmAvatar() async {
    final originalAvatarId = state.avatarId;

    emit(state.copyWith(avatarId: state.tempAvatarId));

    final success = await UserApi.updateAvatar(state.tempAvatarId!);

    if (success) {
      // Success, keep the new avatar
      sl<UserCubit>().fetchUserInfo();
    } else {
      // Failure, revert to the original avatar
      emit(state.copyWith(avatarId: originalAvatarId));
      LogE('Error updating avatar');
    }
  }

  void setTempGender(int? genderIndex) {
    if (genderIndex != null && genderIndex >= 0 && genderIndex < genders.length) {
      emit(state.copyWith(tempGender: genderIndex));
    }
  }

  void confirmGender() async {
    final originalGender = state.gender;

    emit(state.copyWith(gender: state.tempGender));

    final success = await UserApi.updateGender(state.tempGender!);

    if (success) {
      // Success, keep the new gender
      sl<UserCubit>().fetchUserInfo();
    } else {
      // Failure, revert to the original gender
      emit(state.copyWith(gender: originalGender));
      LogE('Error updating gender');
    }
  }

  void setTempBirthday(DateTime birthday) {
    emit(state.copyWith(tempBirthday: birthday));
  }

  void confirmBirthday() async {
    final originalBirthday = state.birthday;

    emit(state.copyWith(birthday: state.tempBirthday));

    final success = await UserApi.updateBirthday(state.tempBirthday!);

    if (success) {
      sl<UserCubit>().fetchUserInfo();
    } else {
      emit(state.copyWith(birthday: originalBirthday));
      LogE('Error updating birthday');
    }
  }

  /// reset temp values
  void resetTempValues({
    int? originalAvatarId,
    int? originalGender,
    DateTime? originalBirthday,
  }) {
    emit(state.copyWith(
      tempAvatarId: state.avatarId,
      tempGender: originalGender,
      tempBirthday: originalBirthday,
      avatarId: originalAvatarId,
    ));
  }
}
