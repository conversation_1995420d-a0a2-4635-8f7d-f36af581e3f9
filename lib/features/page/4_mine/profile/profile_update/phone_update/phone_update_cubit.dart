import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/models/apis/user.dart';
import '../../../../../../core/models/country.dart';
import '../../../../../../core/services/country_service.dart';
import '../../../../../../shared/widgets/easy_loading.dart';
import '../../../../login/login_state.dart';

part 'phone_update_state.dart';

class PhoneUpdateCubit extends Cubit<PhoneUpdateState> {
  PhoneUpdateCubit() : super(const PhoneUpdateState(phoneNo: '')) {
    _initializeCountry();
  }

  /// Initialize default country
  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    emit(state.copyWith(selectedCountry: defaultCountry));
  }

  /// Update selected country
  void updateSelectedCountry(Country country) {
    emit(state.copyWith(selectedCountry: country));
  }

  void setPhoneNo(String phoneNo) => emit(state.copyWith(phoneNo: phoneNo));

  Future<void> updatePhoneNo({
    required String oldPhoneSmsCode,
    required String newPhoneNo,
    required String newPhoneSmsCode,
  }) async {
    emit(state.copyWith(updateStatus: SimplyNetStatus.loading));
    GSEasyLoading.showLoading();

    final success = await UserApi.updatePhoneNo(
      oldPhoneSmsCode,
      newPhoneNo,
      newPhoneSmsCode,
      areaCode: state.selectedCountry?.areaCode,
    );

    if (success) {
      GSEasyLoading.showToast('手机号更新成功');
      emit(state.copyWith(updateStatus: SimplyNetStatus.success));
    } else {
      GSEasyLoading.showToast('手机号更新失败');
      emit(state.copyWith(updateStatus: SimplyNetStatus.failed));
    }

    GSEasyLoading.dismiss();
  }

  Future<void> bindPhoneNo({required String phoneNo, required String code}) async {
    emit(state.copyWith(updateStatus: SimplyNetStatus.loading));
    GSEasyLoading.showLoading();

    final success = await UserApi.bindPhoneNo(
      phoneNo,
      code,
      areaCode: state.selectedCountry?.areaCode,
    );

    if (success) {
      GSEasyLoading.showToast('手机号绑定成功');
      emit(state.copyWith(updateStatus: SimplyNetStatus.success));
    } else {
      GSEasyLoading.showToast('手机号绑定失败');
      emit(state.copyWith(updateStatus: SimplyNetStatus.failed));
    }

    GSEasyLoading.dismiss();
  }
}
