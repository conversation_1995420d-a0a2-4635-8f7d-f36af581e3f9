import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../../core/constants/enums.dart';
import '../../../../../shared/widgets/gradient_border.dart';
import '../profile_cubit.dart';

/// Utility class for showing date pickers
/// Uses a custom three-column wheel picker with dark theme
class ProfileDatePicker {
  static void show(BuildContext context) {
    final now = DateTime.now();
    final userInfoCubit = context.read<UserProfileCubit>();

    CommonBottomSheet.show(
      context: context,
      showGradientOverlay: true,
      maxHeight: 300.gw,
      backgroundColor: context.theme.cardColor,
      header: BottomSheetHeader(
        title: 'select_date'.tr(),
        showCloseButton: false,
      ),
      children: [
        BlocProvider.value(
          value: userInfoCubit,
          child: CustomDatePickerContent(now: now),
        ),
      ],
      buttons: [
        BottomSheetButton(
          title: 'cancel'.tr(),
          style: CommonButtonStyle.tertiary,
          backgroundColor: context.colorTheme.foregroundColor,
          borderColor: context.colorTheme.foregroundColor,
          onPressed: () => Navigator.pop(context),
        ),
        BottomSheetButton(
          title: 'ok'.tr(),
          style: CommonButtonStyle.primary,
          onPressed: () {
            userInfoCubit.confirmBirthday();
            Navigator.pop(context);
          },
        ),
      ],
    );
  }
}

/// Custom three-column date picker content widget
class CustomDatePickerContent extends StatefulWidget {
  final DateTime now;

  const CustomDatePickerContent({
    super.key,
    required this.now,
  });

  @override
  State<CustomDatePickerContent> createState() =>
      _CustomDatePickerContentState();
}

class _CustomDatePickerContentState extends State<CustomDatePickerContent> {
  late FixedExtentScrollController dayController;
  late FixedExtentScrollController monthController;
  late FixedExtentScrollController yearController;

  late int selectedDay;
  late int selectedMonth;
  late int selectedYear;

  @override
  void initState() {
    super.initState();

    final userInfoCubit = context.read<UserProfileCubit>();
    final initialDate = userInfoCubit.state.tempBirthday ?? widget.now;

    selectedDay = initialDate.day;
    selectedMonth = initialDate.month;
    selectedYear = initialDate.year;

    dayController = FixedExtentScrollController(initialItem: selectedDay - 1);
    monthController =
        FixedExtentScrollController(initialItem: selectedMonth - 1);
    yearController = FixedExtentScrollController(
        initialItem: widget.now.year - selectedYear);
  }

  @override
  void dispose() {
    dayController.dispose();
    monthController.dispose();
    yearController.dispose();
    super.dispose();
  }

  void _updateDate() {
    final newDate = DateTime(selectedYear, selectedMonth, selectedDay);
    context.read<UserProfileCubit>().setTempBirthday(newDate);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 130.gw,
      child: Row(
        children: [
          // Day picker
          Expanded(
            child: _buildWheelPicker(
              type: DatePickerType.day,
              controller: dayController,
              itemCount: _getDaysInMonth(selectedYear, selectedMonth),
              onSelectedItemChanged: (index) {
                setState(() {
                  selectedDay = index + 1;
                });
                _updateDate();
              },
              itemBuilder: (index) => _buildPickerItem(
                (index + 1).toString().padLeft(2, '0'),
                index + 1 == selectedDay,
              ),
            ),
          ),
          // Month picker
          Expanded(
            child: _buildWheelPicker(
              type: DatePickerType.month,
              controller: monthController,
              itemCount: 12,
              onSelectedItemChanged: (index) {
                setState(() {
                  selectedMonth = index + 1;
                  // Adjust day if it's invalid for the new month
                  final maxDays = _getDaysInMonth(selectedYear, selectedMonth);
                  if (selectedDay > maxDays) {
                    selectedDay = maxDays;
                    dayController.animateToItem(
                      selectedDay - 1,
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                    );
                  }
                });
                _updateDate();
              },
              itemBuilder: (index) => _buildPickerItem(
                (index + 1).toString().padLeft(2, '0'),
                index + 1 == selectedMonth,
              ),
            ),
          ),
          // Year picker
          Expanded(
            child: _buildWheelPicker(
              type: DatePickerType.year,
              controller: yearController,
              itemCount: 100, // 100 years back
              onSelectedItemChanged: (index) {
                setState(() {
                  selectedYear = widget.now.year - index;
                  // Adjust day if it's invalid for the new year (leap year)
                  final maxDays = _getDaysInMonth(selectedYear, selectedMonth);
                  if (selectedDay > maxDays) {
                    selectedDay = maxDays;
                    dayController.animateToItem(
                      selectedDay - 1,
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                    );
                  }
                });
                _updateDate();
              },
              itemBuilder: (index) => _buildPickerItem(
                (widget.now.year - index).toString(),
                widget.now.year - index == selectedYear,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWheelPicker({
    required FixedExtentScrollController controller,
    required int itemCount,
    required ValueChanged<int> onSelectedItemChanged,
    required Widget Function(int) itemBuilder,
    required DatePickerType type,
  }) {
    return CupertinoPicker(
      scrollController: controller,
      itemExtent: 45.gw,
      onSelectedItemChanged: onSelectedItemChanged,
      selectionOverlay: Container(
        decoration: BoxDecoration(
          border: GradientBoxBorder(
            gradient: _getBorderGradient(type),
            width: 1.gw,
          ),
          gradient: _getBackgroundGradient(type),
        ),
      ),
      children: List.generate(itemCount, itemBuilder),
    );
  }

  LinearGradient _getBorderGradient(DatePickerType type) {
    switch (type) {
      case DatePickerType.day:
        return LinearGradient(
          colors: [
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(0.1),
          ],
        );
      case DatePickerType.month:
        return LinearGradient(
          colors: [
            Colors.white.withOpacity(0.12),
            Colors.white.withOpacity(0.12),
          ],
        );
      case DatePickerType.year:
        return LinearGradient(
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(0.0),
          ],
        );
    }
  }

  LinearGradient _getBackgroundGradient(DatePickerType type) {
    final baseColor = context.colorTheme.foregroundColor;
    
    switch (type) {
      case DatePickerType.day:
        return LinearGradient(
          colors: [
            baseColor.withOpacity(0.0),
            baseColor.withOpacity(0.0),
            baseColor.withOpacity(0.1),
            baseColor.withOpacity(0.15),
            baseColor.withOpacity(0.15),
          ],
        );
      case DatePickerType.month:
        return LinearGradient(
          colors: [
            baseColor.withOpacity(0.15),
            baseColor.withOpacity(0.15),
          ],
        );
      case DatePickerType.year:
        return LinearGradient(
          colors: [
            baseColor.withOpacity(0.15),
            baseColor.withOpacity(0.15),
            baseColor.withOpacity(0.1),
            baseColor.withOpacity(0.0),
            baseColor.withOpacity(0.0),
          ],
        );
    }
  }

  Widget _buildPickerItem(String text, bool isSelected) {
    return Center(
      child: AneText(
        text,
        style: TextStyle(
          color: isSelected ? context.textTheme.primary.color : context.textTheme.title.color,
          fontSize: 20.gw,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
    );
  }

  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
}
