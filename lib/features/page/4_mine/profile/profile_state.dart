import 'package:equatable/equatable.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/injection_container.dart';

class UserProfileState extends Equatable {
  final String? account;
  final String? nickName;
  final int? avatarId;
  final int? tempAvatarId;
  final DateTime? birthday;
  final DateTime? tempBirthday;
  final String? phone;
  final String? email;
  final int? gender;
  final int? tempGender;
  final List<String> avatars;
  final bool updated;

  const UserProfileState({
    this.avatarId,
    this.nickName,
    this.account,
    this.phone,
    this.email,
    this.gender,
    this.birthday,
    this.avatars = const [],
    this.updated = false,
    this.tempGender,
    this.tempAvatarId,
    this.tempBirthday,
  });

  factory UserProfileState.fromUserSingleton() {
    final userInfo = sl<UserCubit>().state.userInfo;
    return UserProfileState(
      account: userInfo?.userNo,
      nickName: userInfo?.nickName,
      avatarId: userInfo?.faceId,
      birthday: (userInfo?.birthday as String?)?.toDateTime(),
      phone: userInfo?.phoneNo,
      email: userInfo?.email,
      gender: userInfo?.gender,
      tempGender: userInfo?.gender,
      tempAvatarId: userInfo?.faceId,
      tempBirthday: (userInfo?.birthday as String?)?.toDateTime(),
    );
  }

  UserProfileState copyWith({
    int? avatarId,
    String? nickName,
    String? account,
    String? phone,
    String? email,
    int? gender,
    DateTime? birthday,
    List<String>? avatars,
    bool? updated,
    int? tempGender,
    int? tempAvatarId,
    DateTime? tempBirthday,
  }) {
    return UserProfileState(
      avatarId: avatarId ?? this.avatarId,
      nickName: nickName ?? this.nickName,
      account: account ?? this.account,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      avatars: avatars ?? this.avatars,
      updated: updated ?? this.updated,
      tempGender: tempGender ?? this.tempGender,
      tempAvatarId: tempAvatarId ?? this.tempAvatarId,
      tempBirthday: tempBirthday ?? this.tempBirthday,
    );
  }

  @override
  List<Object?> get props => [
        avatarId,
        nickName,
        account,
        phone,
        email,
        gender,
        birthday,
        avatars,
        updated,
        tempGender,
        tempAvatarId,
        tempBirthday,
      ];
}
