import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/page/4_mine/account_security/modify_pwd/modify_pwd_view.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'modify_pwd_state.dart';

class ModifyPwdCubit extends Cubit<ModifyPwdState> {
  ModifyPwdCubit() : super(ModifyPwdState().init());

  onClickModifyPassword(SetPasswordType type) async {
    sl<NavigatorService>().unFocus();
    if (sl<UserCubit>().state.userInfo!.hasFundPwd && state.oldPwd.isEmpty) {
      return GSEasyLoading.showToast(
          type == SetPasswordType.modifyLoginPwd ? 'enter_login_password'.tr() : 'enter_fund_password'.tr());
    }
    if (state.newPwd.isEmpty) {
      return GSEasyLoading.showToast('enter_password'.tr());
    }
    if (state.newPwd != state.newPwdConfirm) {
      return GSEasyLoading.showToast('passwords_do_not_match'.tr());
    }
    GSEasyLoading.showLoading();
    final flag = await UserApi.updatePassword(type, state.oldPwd, state.newPwd);
    await sl<UserCubit>().fetchUserInfo();
    GSEasyLoading.dismiss();
    if (flag) {
      GSEasyLoading.showToast('settings_updated_successfully'.tr());
      sl<NavigatorService>().pop();
    }
  }

  void oldPwdChanged(String password) {
    emit(state.copyWith(oldPwd: password));
  }

  void newPwdChanged(String password) {
    emit(state.copyWith(newPwd: password));
  }

  void newPwdConfirmChanged(String password) {
    emit(state.copyWith(newPwdConfirm: password));
  }

  void toggleOldPwdVisibility() {
    emit(state.copyWith(isOldPwdVisible: !state.isOldPwdVisible));
  }

  void toggleNewPwdVisibility() {
    emit(state.copyWith(isNewPwdVisible: !state.isNewPwdVisible));
  }

  void toggleNewPwdConfirmVisibility() {
    emit(state.copyWith(isNewPwdConfirmVisible: !state.isNewPwdConfirmVisible));
  }
}
