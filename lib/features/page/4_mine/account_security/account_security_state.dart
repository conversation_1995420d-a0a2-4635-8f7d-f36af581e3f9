import 'package:wd/core/base/base_state.dart';


class AccountSecurityState extends BaseState {
  String? nickName;
  String? account;
  String? phone;
  String? email;

  AccountSecurityState init() {
    return AccountSecurityState();
  }

  AccountSecurityState clone() {
    return AccountSecurityState()..netState = netState;
  }

  AccountSecurityState copyWith({
    String? nickName,
    String? account,
    String? phone,
    String? email,
    bool? movieTabVisible,
  }) {
    return AccountSecurityState()
      ..nickName = nickName ?? this.nickName
      ..account = account ?? this.account
      ..phone = phone ?? this.phone
      ..email = email ?? this.email;
  }

  List<Object?> get props => [
        nickName,
        account,
        phone,
        email,
      ];
}
