import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'payment_usdt_list_state.dart';

class PaymentUsdtListCubit extends Cubit<PaymentUsdtListState> {
  PaymentUsdtListCubit() : super(PaymentUsdtListState().init());

  fetchListData() async {
    GSEasyLoading.showLoading();
    final list = await UserApi.fetchBindBankCardList(type: 1, wldType: 2);
    GSEasyLoading.dismiss();
    state.netState = NetState.dataSuccessState;
    state.dataList = list;

    emit(state.clone());
  }

  void goto(PaymentCardEntity account) {
    // 实现跳转逻辑
  }

  // 添加钱包
  void onClickAdd() async {
    await sl<NavigatorService>().push(AppRouter.userPaymentUsdtAdd);
    fetchListData();
  }

}
