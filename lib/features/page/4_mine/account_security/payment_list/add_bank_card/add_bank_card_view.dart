import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/common_textformfield.dart';
import 'add_bank_card_cubit.dart';
import 'add_bank_card_state.dart';

class AddBankCardPage extends BasePage {
  const AddBankCardPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddBankCardPageState();
}

class _AddBankCardPageState extends BasePageState<AddBankCardPage> {
  final TextEditingController textControllerName = TextEditingController();
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController();
  final TextEditingController textControllerBankAddress =
      TextEditingController();

  @override
  void initState() {
    pageTitle = "添加银行卡".tr();
    context.read<AddBankCardCubit>().fetchBankListData();

    // 判断是否已设置真实姓名
    final realName = sl<UserCubit>().state.userInfo?.realName;
    if (realName != null && realName.isNotEmpty) {
      textControllerName.text = realName;
    }
    super.initState();
  }

  @override
  void dispose() {
    textControllerName.dispose();
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    textControllerBankAddress.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return CommonCardPage(
      child: Form(
        key: context.read<AddBankCardCubit>().state.formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.5.gw),
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildRealNameWidget(),
                SizedBox(height: 10.gw),
                _buildFormFieldsSection(),
                SizedBox(height: 10.gw),
                _buildDefaultCardSwitchSection(),
                SizedBox(
                  height: 15.gw,
                ),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRealNameWidget() {
    // final realName = "阿道夫";

    return BlocSelector<UserCubit, UserState, String?>(
        selector: (state) => state.userInfo?.realName,
        builder: (context, realName) {
          if (realName != null && realName.isNotEmpty) {
            return getRadiusContainer(
              height: 45.gw,
              child: Padding(
                padding: EdgeInsets.only(left: 10.gw),
                child: Row(
                  children: [
                    SizedBox(
                      width: 55.gw,
                      child: AutoSizeText(
                        "开户人".tr(),
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    SizedBox(width: 5.gw),
                    Expanded(
                      child: AutoSizeText(
                        realName,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return getRadiusContainer(
            child: Column(
              children: [
                Row(
                  children: [
                    Image.asset(
                      "assets/images/mine/icon_bank_card_realName.png",
                      width: 30.gw,
                      height: 30.gw,
                    ),
                    SizedBox(width: 5.gw),
                    Expanded(
                      child: Text(
                        "绑定银行卡，需本人姓名与银行卡开户人一致，填写后该姓名将与账户绑定。",
                        style: Theme.of(context).textTheme.titleMedium,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.5.gw),
                CommonTextFormField(
                  title: "开户人".tr(),
                  margin: EdgeInsets.zero,
                  controller: textControllerName,
                  validator: (v) {

                    if (v != null && StringUtil.containsSpecialCharsOrSpaces(v)) {
                      return "请勿包含空格或特殊字符";
                    }

                    return v!.trim().isEmpty ? "姓名不能为空".tr() : null;
                  },
                  hintText: "请输入真实姓名".tr(),
                ),
              ],
            ),
          );
        });





  }

  Widget _buildFormFieldsSection() {
    return getRadiusContainer(
      child: Column(
        children: [
          CommonTextFormField(
            controller: textControllerBankNum,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "银行卡号不能为空".tr();
              }
              if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                return "请勿包含空格或特殊字符";
              }
              return null;
            },
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(19),
            ],
            hintText: "请输入银行卡号".tr(),
            title: "银行卡号".tr(),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => context
                .read<AddBankCardCubit>()
                .onClickShowBankListPicker(
                    context: context, controller: textControllerBankName),
            child: CommonTextFormField(
              controller: textControllerBankName,
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return "请选择银行名称".tr();
                }
                if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                  return "请勿包含空格或特殊字符";
                }
                return null;
              },
              hintText: "请选择银行名称".tr(),
              title: "银行名称".tr(),
              suffixIcon: Image.asset(
                Assets.nextIcon,
                width: 15.gw,
                height: 15.gw,
              ),
              inputEnable: false,
            ),
          ),
          CommonTextFormField(
            controller: textControllerBankAddress,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "开户行地址不能为空".tr();
              }
              if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                return "请勿包含空格或特殊字符";
              }
              return null;
            },
            hintText: "请输入开户行地址".tr(),
            title: "开户行地址".tr(),
          ),
          SizedBox(height: 12.5.gw),
        ],
      ),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return getRadiusContainer(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("设置为默认银行卡".tr(), style: Theme.of(context).textTheme.titleLarge),
          BlocBuilder<AddBankCardCubit, AddBankCardState>(
            builder: (context, state) {
              return CommonSwitch(
                value: state.isDefaultCard,
                onChanged: (value) =>
                    context.read<AddBankCardCubit>().onClickSwitch(value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
      title: '提交'.tr(),
      backgroundColor: Theme.of(context).colorScheme.primary,
      onPressed: () => context.read<AddBankCardCubit>().onSubmit(
        context,
        realName: textControllerName.text,
        cardNo: textControllerBankNum.text,
        bankAddress: textControllerBankAddress.text,
      )
    );
  }

  Widget getRadiusContainer({required Widget child, double? height}) {
    return Container(
      padding: EdgeInsets.all(10.gw),
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: child,
    );
  }
}
