import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/user/pay_card_cell.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'payment_list_cubit.dart';
import 'payment_list_state.dart';

class PaymentListView extends BasePage {
  const PaymentListView({super.key});

  @override
  BasePageState<BasePage> getState() => _PaymentListViewState();
}

class _PaymentListViewState extends BasePageState<PaymentListView> {
  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  // 获取列表数据
  void _getData() {
    context.read<PaymentListCubit>().fetchListData();
  }

  @override
  void initState() {
    super.initState();
    final state = context.read<PaymentListCubit>().state;
    pageTitle = state.type.title;
    pageBgColor = Colors.white;
    _getData();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  // 添加按钮
  Widget _getAddButton(BuildContext context, int listLength) {
    final state = context.read<PaymentListCubit>().state;
    const limit = 5;
    if (listLength >= limit) return Container();

    return Container(
      height: 40.gw,
      margin: EdgeInsets.symmetric(horizontal: 17.gw),
      decoration: DottedDecoration(
        shape: Shape.box,
        color: const Color(0xff20B25B),
        borderRadius: BorderRadius.circular(20),
      ),
      child: CommonButton(
        title: state.type.addButtonText.tr(),
        fontWeight: FontWeight.w400,
        fontSize: 15.fs,
        textColor: const Color(0xff00AF2C),
        backgroundColor: const Color(0x1900CD58),
        onPressed: () => context.read<PaymentListCubit>().onClickAdd(),
      ),
    );
  }

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  // 主页面组件
  Widget mainPageWidget(PaymentListState state) {
    return CommonCardPage(
      child: Column(
        children: [
          if (state.netState == NetState.loadingState) ...[
            const Center(child: CircularProgressIndicator()),
          ],
          if (state.netState == NetState.dataSuccessState) ...[
            Expanded(
              child: CommonRefresher(
                enablePullDown: true,
                enablePullUp: false,
                refreshController: refreshController,
                onRefresh: _onRefresh,
                onLoading: () {},
                listWidget: state.dataList!.isEmpty
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(height: 10.gw),
                          Text(
                            state.type.emptyText.tr(),
                            style: TextStyle(fontSize: 14.fs, color: const Color(0xff7F8A9B)),
                          ),
                        ],
                      )
                    : SingleChildScrollView(
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.5.gw),
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (c, i) {
                              return InkWell(
                                onTap: () => context.read<PaymentListCubit>().goto(state.dataList![i]),
                                child: PayCardCell(state.dataList![i]),
                              );
                            },
                            separatorBuilder: (c, i) => SizedBox(height: 10.gw),
                            itemCount: state.dataList!.length,
                            padding: EdgeInsets.only(top: 10.gw, bottom: 10.gw),
                          ),
                        ),
                      ),
              ),
            ),
            SizedBox(height: 20.gw),
            _getAddButton(context, state.dataList!.length),
            SizedBox(
              height: MediaQuery.of(context).padding.bottom > 0 ? MediaQuery.of(context).padding.bottom + 20.gw : 20.gw,
            ),
          ]
        ],
      ),
    );
  }

  void _listener(BuildContext context, BaseState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<PaymentListCubit, PaymentListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(
          state,
          (baseState, context) => mainPageWidget(state),
          refreshMethod: () => _getData(),
        );
      },
    );
  }
}
