import 'package:bloc/bloc.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';

import 'add_usdt_address_state.dart';

class AddUsdtAddressCubit extends Cubit<AddUsdtAddressState> {
  AddUsdtAddressCubit() : super(AddUsdtAddressState().init());


  void onClickSwitch(bool value) {
    state.isDefaultCard = value;
    emit(state.clone());
  }

  void onSubmit(
      context, {
        required String address,
        required String addressProtocol,
      }) async {
    sl<NavigatorService>().unFocus();
    if (state.formKey.currentState!.validate()) {
      if (sl<UserCubit>().isFundPasswordInputLocked)  {
        GSEasyLoading.showToast("支付密码连续错误5次，请1分钟后重试");
        return;
      }

      final fundPwd = await FundPwdSheet(context).show();
      if (fundPwd == null) return;

      GSEasyLoading.showLoading();
      final flag = await UserApi.addBankCard(
          type: WithdrawType.wallet,
          bankCode: "USDT",
          bankName: addressProtocol,
          cardNo: address,
          realName: sl<UserCubit>().state.userInfo?.realName ?? "",
          fundPwd: fundPwd,
          wldType: 1, // USDT钱包
      );
      if (sl<UserCubit>().state.userInfo!.realName.isEmpty) {
        await sl<UserCubit>().fetchUserInfo();
      }
      GSEasyLoading.dismiss();
      if (flag) {
        sl<UserCubit>().resetFundPasswordErrorLock();
        GSEasyLoading.showToast("添加成功");
        await Future.delayed(const Duration(milliseconds: 500));
        sl<NavigatorService>().pop();
      }
    }
  }

}
