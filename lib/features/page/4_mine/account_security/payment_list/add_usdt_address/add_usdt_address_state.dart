import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';

class AddUsdtAddressState extends BaseState {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  FocusNode funPasswordFocusNode = FocusNode();
  bool isDefaultCard = false;

  // 初始化状态
  AddUsdtAddressState init() {
    return AddUsdtAddressState()
      ..formKey = GlobalKey<FormState>()
      ..funPasswordFocusNode = FocusNode()
      ..isDefaultCard = false
      ..dataList = [];
  }

  // 克隆状态
  AddUsdtAddressState clone() {
    return AddUsdtAddressState()
      ..formKey = formKey
      ..funPasswordFocusNode = funPasswordFocusNode
      ..isDefaultCard = isDefaultCard
      ..dataList = dataList;
  }
}
