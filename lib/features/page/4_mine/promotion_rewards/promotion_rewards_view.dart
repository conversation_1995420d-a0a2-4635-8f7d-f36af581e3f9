import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/entities/commission_overview_entity.dart';
import 'package:wd/core/models/entities/my_team_entity.dart';
import 'package:wd/core/models/entities/team_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/mixin/page_view_animation_mixin.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/balance/currency_symbol_icon.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/promotion/promotion_share.dart';
import 'package:wd/shared/widgets/section_container.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'promotion_rewards_state.dart';

/// 推广赚钱
class PromotionRewardsView extends BasePage {
  const PromotionRewardsView({super.key});

  @override
  BasePageState<BasePage> getState() => _PromotionRewardsViewState();
}

class _PromotionRewardsViewState extends BasePageState {
  late String agentPromotionUrl;

  @override
  void initState() {
    super.initState();
    isBack = false;
    pageTitle = "promotion".tr();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PromotionRewardsCubit>().didPop();
      context.read<PromotionRewardsCubit>().fetchUserInviteLink();
      context.read<PromotionRewardsCubit>().fetchTeam();
      context.read<PromotionRewardsCubit>().fetchMyTeam();
      context.read<PromotionRewardsCubit>().fetchCommission();
    });
    getAgentPromotionUrl();
  }

  void getAgentPromotionUrl() async {
    agentPromotionUrl = await GlobalConfig().getConfigValueByKey("agent_promotion_url") ?? '';
  }

  @override
  Widget buildPage(BuildContext context) {
    final imageActiveColor = context.theme.primaryColor;
    final imageInactiveColor = context.colorTheme.tabInactive;
    final tabs = [
      CommonTabBarItem(
          title: 'rewards'.tr(),
          imageUrl: 'assets/images/promotion/icon_reward.svg',
          imageActiveColor: imageActiveColor,
          imageInactiveColor: imageInactiveColor),
      CommonTabBarItem(
          title: 'invite'.tr(),
          imageUrl: 'assets/images/promotion/icon_invite.svg',
          imageActiveColor: imageActiveColor,
          imageInactiveColor: imageInactiveColor),
      CommonTabBarItem(
          title: 'rules'.tr(),
          imageUrl: 'assets/images/promotion/icon_rules.svg',
          imageActiveColor: imageActiveColor,
          imageInactiveColor: imageInactiveColor),
    ];

    return BlocBuilder<PromotionRewardsCubit, PromotionRewardsState>(
      builder: (context, state) {
        return Scaffold(
          body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.gw),
                child: AnimationLimiter(
                  child: Column(
                    children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 375),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                      children: [
                        SizedBox(height: 14.gw),
                        CommonTabBar(
                          tabs,
                          isScrollable: false,
                          currentIndex: state.currentTabIndex,
                          onTap: (index) => context.read<PromotionRewardsCubit>().changeTabIndex(index),
                        ),
                        // _buildTabSection(),
                        SizedBox(height: 10.gw),
                        if (state.currentTabIndex == 0) ...[
                          ...[
                            _buildTeamLevelSection(),
                            SizedBox(height: 16.gw),
                            _buildRewardsOverviewSection(),
                            SizedBox(height: 16.gw),
                            _buildTeamOverviewSection(),
                            SizedBox(height: 16.gw),
                          ],
                        ],
                        if (state.currentTabIndex == 1) ...[
                          const PromotionSharePage(),
                        ],
                        if (state.currentTabIndex == 2) ...[
                          SingleChildScrollView(
                            child: AppImage(
                              width: double.infinity,
                              fit: BoxFit.fill,
                              imageUrl: agentPromotionUrl,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                )),
          ),
        );
      },
    );
  }

  // Widget _buildTabSection() {
  //   return BlurContainer(
  //     height: 35.gw,
  //     borderRadius: BorderRadius.circular(8.gw),
  //     borderColor: Colors.white,
  //     borderWidth: 1,
  //     gradientColors: const [
  //       Color(0xFFECEFF6),
  //       Color(0xFFFEFEFF),
  //     ],
  //     boxShadow: const BoxShadow(
  //       color: Color(0x1A000000), // 阴影颜色，带透明度
  //       offset: Offset(0, 2), // 阴影偏移
  //       blurRadius: 2, // 模糊半径
  //     ),
  //     child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, int>(
  //       selector: (state) {
  //         return state.currentTabIndex;
  //       },
  //       builder: (context, state) {
  //         return Padding(
  //           padding: EdgeInsets.symmetric(horizontal: 5.gw),
  //           child: Row(
  //             children: tabs
  //                 .map((tab) => _buildTabButton(text: tab.tr(), index: tabs.indexOf(tab), currentTabIndex: state))
  //                 .toList(),
  //           ),
  //         );
  //       },
  //     ),
  //   );
  // }

  // Widget _buildTabButton({required String text, required int index, required int currentTabIndex}) {
  //   return Expanded(
  //       child: SizedBox(
  //     height: 26.gw,
  //     child: GestureDetector(
  //       onTap: () => context.read<PromotionRewardsCubit>().changeTabIndex(index),
  //       child: Container(
  //         height: 26.gw,
  //         decoration: BoxDecoration(
  //           color: index == currentTabIndex ? const Color(0xFFD4B88C) : Colors.transparent,
  //           borderRadius: BorderRadius.circular(6.gw),
  //         ),
  //         child: Center(
  //           child: Text(
  //             text,
  //             textAlign: TextAlign.center,
  //             style: TextStyle(
  //               color: index == currentTabIndex ? Colors.white : const Color(0xff3B4165),
  //               fontSize: 14.fs,
  //               fontWeight: FontWeight.w500,
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //   ));
  // }

  Widget _buildTeamLevelSection() {
    return SectionContainer(
      title: 'my_team_level'.tr(),
      imagePath: "assets/images/promotion/icon_promotion_level.png",
      onTap: () => sl<NavigatorService>().push(AppRouter.commisionRules),
      suffixIcon: Container(
          alignment: Alignment.topCenter,
          child: Image.asset("assets/images/promotion/icon_promotion_plan_tag.png", width: 53.gw)),
      child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, TeamEntity?>(
        selector: (state) => state.teamEntity,
        builder: (context, data) {
          if (data != null) {
            return _PageViewCard(teamData: data);
          }
          return SizedBox(
              height: 185.gw,
              child: Center(
                  child: SizedBox(
                height: 25.gw,
                width: 25.gw,
                child: const CircularProgressIndicator(strokeWidth: 1),
              )));
        },
      ),
    );
  }

  Widget _buildRewardsOverviewSection() {
    final cubit = context.read<PromotionRewardsCubit>();
    return BlocListener<PromotionRewardsCubit, PromotionRewardsState>(
      listenWhen: (previous, current) => previous.commissionReceiveState != current.commissionReceiveState,
      listener: (context, state) {
        if (state.commissionReceiveState == NetState.dataSuccessState) {
          GSEasyLoading.showToast('commission_receive_success'.tr());
          cubit.fetchCommission();
        }
      },
      child: SectionContainer(
        title: 'commission_overview'.tr(),
        imagePath: 'assets/images/promotion/icon_promotion_title_reward.png',
        onTap: () => sl<NavigatorService>().push(AppRouter.commissionRecords),
        suffixIcon: Padding(
          padding: EdgeInsets.only(right: 12.gw),
          child: Icon(
            Icons.arrow_forward_ios,
            size: 16.fs,
            color: const Color(0XFFCACDDB),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 12.gw),
          child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, CommissionOverviewEntity>(
            selector: (state) => state.commissionOverviewEntity ?? CommissionOverviewEntity(),
            builder: (context, data) {
              return GridView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 11.gw,
                  crossAxisSpacing: 16.gw,
                  childAspectRatio: 178 / 80,
                ),
                children: [
                  _SummaryItem(
                      title: 'cumulative_rewards'.tr(),
                      imagePath: "assets/images/promotion/icon_promotion_reward_total.png",
                      value: data.totalCommission.formattedMoney),
                  _SummaryItem(
                      title: 'yesterday_rewards'.tr(),
                      imagePath: "assets/images/promotion/icon_promotion_reward_yesterday.png",
                      value: data.yesterdayCommission.formattedMoney),
                  _SummaryItem(title: 'pending_rewards'.tr(), value: data.commission.formattedMoney),
                  Container(
                    alignment: Alignment.bottomCenter,
                    child: CommonButton(
                      height: 33.gw,
                      title: 'withdraw'.tr(),
                      onPressed: () {
                        if (data.commission > 0) {
                          cubit.commissionReceive();
                        } else {
                          GSEasyLoading.showToast('no_commission_available'.tr());
                        }
                      },
                    ),
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTeamOverviewSection() {
    return SectionContainer(
      title: 'team_overview'.tr(),
      imagePath: 'assets/images/promotion/icon_promotion_title_reward.png',
      onTap: () => sl<NavigatorService>().push(AppRouter.teamManagement),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 12.gw),
        child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, MyTeamEntity>(
          selector: (state) => state.myTeamEntity ?? MyTeamEntity(),
          builder: (context, data) {
            return GridView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 11.gw,
                crossAxisSpacing: 16.gw,
                childAspectRatio: 178 / 80,
              ),
              children: [
                _SummaryItem(
                  title: 'team_member_count'.tr(),
                  value: data.teamSize.toString(),
                  isMoney: false,
                ),
                _SummaryItem(title: 'team_betting'.tr(), value: data.teamBetAmount.formattedMoney),
                _SummaryItem(title: 'team_recharge'.tr(), value: data.teamCashinAmount.formattedMoney),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildTeamStat(String label, String value) {
    return Column(
      children: [
        Text(label, style: TextStyle(fontSize: 14.fs, color: const Color(0xFF6A7391))),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.fs,
            fontWeight: FontWeight.w600,
            color: const Color(0xff6A7391),
          ),
        ),
      ],
    );
  }
}

class _PageViewCard extends StatefulWidget {
  final TeamEntity teamData;

  const _PageViewCard({required this.teamData});

  @override
  State<_PageViewCard> createState() => _PageViewCardState();
}

class _PageViewCardState extends State<_PageViewCard> with PageViewAnimationMixin {
  @override
  void initState() {
    initializePageController(initialPage: widget.teamData.userTeamLevel);
    context.read<PromotionRewardsCubit>().changeTeamLevelIndex(widget.teamData.userTeamLevel);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        buildAnimatedPageView(
          initialPage: widget.teamData.userTeamLevel,
          itemCount: widget.teamData.teamConfigList.length,
          height: 258.gw,
          itemBuilder: (context, index) {
            final model = widget.teamData.teamConfigList[index];
            return _buildTeamLevelCards(model: model, isActive: widget.teamData.userTeamLevel == index);
          },
          onPageChanged: (index) => context.read<PromotionRewardsCubit>().changeTeamLevelIndex(index),
        ),
        Positioned(
          bottom: 16.gw,
          left: 113.gw,
          right: 113.gw,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              4,
              (index) => BlocSelector<PromotionRewardsCubit, PromotionRewardsState, int>(
                selector: (state) => state.currentTeamLevelIndex,
                builder: (context, currentIndex) {
                  return Container(
                    width: 12.gw,
                    height: 12.gw,
                    margin: EdgeInsets.symmetric(horizontal: 4.gw),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: currentIndex == index ? context.theme.primaryColorLight : context.colorTheme.tabInactive,
                    ),
                  );
                },
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildTeamLevelCards({required TeamTeamConfig model, required bool isActive}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.gw),
          Row(
            children: [
              Text('team_level'.tr(), style: context.textTheme.secondary.fs20),
              SizedBox(width: 12.gw),
              Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.gw),
                  decoration: BoxDecoration(
                    color: context.colorTheme.tabItemBgA,
                    borderRadius: BorderRadius.circular(20.gw),
                    border: Border.all(color: context.colorTheme.borderE, width: 1),
                  ),
                  child: Center(
                      child: AneText('LV${model.teamLevel.toString()}',
                          style: TextStyle(fontSize: 12.gw, color: context.colorTheme.btnBorderPrimary)))),
            ],
          ),
            SizedBox(height: 20.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                  child: _buildRewardCard(
                'betting_rewards'.tr(),
                '${(model.betRebate * 100).removeZeros}%',
                "assets/images/promotion/icon_level_bet.png",
              )),
              SizedBox(width: 8.gw),
              Expanded(
                  child: _buildRewardCard(
                'recharge_rewards'.tr(),
                '${(model.cashinRebate * 100).removeZeros}%',
                "assets/images/promotion/icon_level_deposit.png",
              )),
              SizedBox(width: 8.gw),
              Expanded(
                  child: _buildRewardCard(
                'highest_rewards'.tr(),
                model.maxAmount.removeZeros,
                "assets/images/promotion/icon_level_max.png",
              )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRewardCard(String title, String value, String imagePath) {
    return Container(
      padding: EdgeInsets.all(4.gw),
      decoration: const BoxDecoration(
        image: DecorationImage(image: AssetImage("assets/images/promotion/icon_level_bg.png"), fit: BoxFit.fill),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AspectRatio(
            aspectRatio: 109 / 75,
            child: Image.asset(
              imagePath,
            ),
          ),
          SizedBox(height: 12.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.gw),
            child: AneText(
              title,
              style: context.textTheme.secondary.fs16,
              maxLines: 1,
                overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(height: 2.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.gw),
            child: Row(
              children: [
                Image.asset("assets/images/promotion/icon_level_up_to.png", width: 16.gw, height: 10.gw),
                SizedBox(width: 4.gw),
                AneText(
                  "${'up_to'.tr()}$value",
                  style: context.textTheme.primary.copyWith(color: context.theme.primaryColorLight),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SummaryItem extends StatelessWidget {
  const _SummaryItem({
    required this.title,
    required this.value,
    this.isMoney = true,
    this.imagePath,
  });

  final String title;
  final String value;
  final bool isMoney;
  final String? imagePath;

  @override
  Widget build(BuildContext context) {
    final textStyle = TextStyle(color: context.colorTheme.borderE, fontSize: 14.gw).w500;
    return Container(
      height: 80.gw,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.colorTheme.foregroundColor,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (imagePath != null) ...[
                Image.asset(imagePath!, width: 14.gw),
                SizedBox(width: 8.gw),
              ],
              AneText(title, style: textStyle),
            ],
          ),
          SizedBox(height: 16.gw),
          if (isMoney) ...[
            Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      context.colorTheme.foregroundColor,
                      context.colorTheme.borderE,
                    ],
                    stops: const [0.648, 2.0561],
                  ),
                  border: Border.all(
                    color: context.colorTheme.textSecondary.withOpacity(0.11), // #FFFFFF1C
                    width: 0.6,
                  ),
                  borderRadius: BorderRadius.circular(6.gw),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6.gw),
                  child: Row(
                    children: [
                      CurrencySymbolIcon(),
                      SizedBox(width: 8.gw),
                      AneText(value, style: textStyle),
                    ],
                  ),
                ))
          ] else ...[
            AneText(value, style: textStyle),
          ],
        ],
      ),
    );
  }
}
