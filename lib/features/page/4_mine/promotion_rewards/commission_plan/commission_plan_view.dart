import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/base_stateful_page.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_bet_entity.dart';
import '../../../../../core/models/entities/commission_recharge_entity.dart';
import '../../../../../shared/widgets/promotion/tab_bar_promotion.dart';
import 'commission_plan_cubit.dart';

class CommissionPlanView extends BasePage {
  const CommissionPlanView({super.key});

  @override
  BasePageState<BasePage> getState() => _CommissionPlanViewState();
}

class _CommissionPlanViewState extends BasePageState with SingleTickerProviderStateMixin {
  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    pageTitle = 'commission_plan'.tr();
    _tabController = TabController(length: TeamType.values.length, vsync: this);
    context.read<CommissionPlanCubit>().fetchPromotionBetConfig();
    context.read<CommissionPlanCubit>().fetchCommissionPlanRecharge();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: TabBarPromotion(
        tabController: _tabController,
        tabs: [
          Tab(text: 'betting_commission'.tr()),
          Tab(text: 'recharge_commission'.tr()),
        ],
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [TabScreen1(), RechargeCommission()],
            ),
          )
        ],
      ),
    );
  }
}

class TabScreen1 extends StatelessWidget {
  const TabScreen1({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          if (state.commissionBetNetState == NetState.loadingState) return const SizedBox.shrink();
          return Column(
            children: [
              TabHeader(width: 90.gw, borderRadius: 0, children: [
                ...List.generate(
                    state.commissionBetEntity?.list?.length ?? 0,
                    (index) => _TableHeaderCell(
                          text: state.commissionBetEntity?.list?[index].type ?? '',
                          flex: 2,
                          isBold: true,
                          isSelected:
                              state.selectedCommissionBetList?.type == state.commissionBetEntity?.list?[index].type,
                          onSelected: (value) => context
                              .read<CommissionPlanCubit>()
                              .filterCommissionBetList(state.commissionBetEntity?.list?[index].type ?? ''),
                        )),
              ]),
              SizedBox(height: 8.gw),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: AnimationLimiter(
                    child: ListView.builder(
                      itemCount: state.selectedCommissionBetList?.thirdList?.length ?? 0,
                      itemBuilder: (BuildContext context, int index) {
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            scale: 0.5,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 4.gw),
                                child: _buildTableSection(
                                    state.selectedCommissionBetList?.thirdList?[index] ?? CommissionBetListThirdList()),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTableSection(CommissionBetListThirdList item) {
    return Container(
      height: 233.gw,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.gw),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 5.gw),
          Row(
            children: [
              Container(
                height: 15.gw,
                width: 5.gw,
                decoration: BoxDecoration(
                  color: const Color(0xFFD4B88C),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12.gw),
                    bottomRight: Radius.circular(12.gw),
                  ),
                ),
              ),
              SizedBox(width: 6.gw),
              Text(
                item.platformName ?? '',
                style: TextStyle(
                  fontSize: 16.fs,
                  color: const Color(0xFF3B416B),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.all(10.gw),
            child: SizedBox(
              height: 174.gw,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [const TableHeader(), TableBody(item: item)],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TableHeader extends StatelessWidget {
  const TableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 52.gw,
      decoration: const BoxDecoration(
        color: Color(0xFFB4A28A),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          _buildHeaderCell(
            "团队\n等级",
            flex: 2,
            isFirst: true,
          ),
          _buildHeaderCell(
            "一级下属",
            flex: 4,
            hasSubColumns: true,
            subColumns: const ["比例", "上限"],
          ),
          _buildHeaderCell(
            "二级下属",
            flex: 4,
            hasSubColumns: true,
            subColumns: const ["比例", "上限"],
          ),
          _buildHeaderCell(
            "三级下属",
            flex: 4,
            hasSubColumns: true,
            subColumns: const ["比例", "上限"],
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(
    String text, {
    required int flex,
    bool hasSubColumns = false,
    List<String> subColumns = const [],
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Expanded(
      flex: flex,
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            right: !isLast
                ? BorderSide(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  )
                : BorderSide.none,
          ),
        ),
        child: hasSubColumns
            ? Column(
                children: [
                  Expanded(
                    child: Center(
                      child: Text(
                        text,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.fs,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 25.gw,
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: subColumns.map((subText) {
                        final isLastSubColumn = subColumns.indexOf(subText) == subColumns.length - 1;
                        return Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border(
                                right: !isLastSubColumn
                                    ? BorderSide(
                                        color: Colors.white.withOpacity(0.2),
                                        width: 1,
                                      )
                                    : BorderSide.none,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                subText,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14.fs,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              )
            : Center(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.fs,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
      ),
    );
  }
}

class TableBody extends StatelessWidget {
  const TableBody({super.key, required this.item});
  final CommissionBetListThirdList item;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ...List.generate(4, (index) => _BuildTableRow(index: index, cells: _processData()[index])),
      ],
    );
  }

  List<List<String>> _processData() {
    final List<List<String>> processedData = [];

    // Group by teamLevel
    for (int teamLevel = 0; teamLevel <= 3; teamLevel++) {
      final List<String> rowData = [];

      // Add team level label
      rowData.add('$teamLevel级');

      // Get data for each childLevel (1-3) for current teamLevel
      for (int childLevel = 1; childLevel <= 3; childLevel++) {
        final record = item.list?.firstWhere(
          (element) => element.teamLevel == teamLevel && element.childLevel == childLevel,
        );

        // Add commission rate and cap for each child level
        rowData.add('${((record?.commissionRate ?? 0) * 100).toStringAsFixed(2)}%');
        rowData.add(formatNumberWithChineseUnits((record?.commissionCap ?? 0).toDouble()));
      }

      processedData.add(rowData);
    }
    return processedData;
  }
}

String formatNumberWithChineseUnits(double number) {
  if (number >= 100000000) {
    double value = number / 100000000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}亿';
  } else if (number >= 1000000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  } else if (number >= 10000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  }
  return number.toStringAsFixed(0);
}

class _BuildTableRow extends StatelessWidget {
  const _BuildTableRow({super.key, required this.cells, required this.index});
  final List<String> cells;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: cells.map((cell) {
        return Expanded(
          child: Container(
            height: 30.gw,
            width: 44.gw,
            padding: const EdgeInsets.all(6.0),
            decoration: BoxDecoration(
              color: index.isEven ? Colors.white : const Color(0xFFF3EEE8),
              border: Border.all(
                color: Colors.white,
              ),
            ),
            child: Center(
              child: AutoSizeText(
                cell,
                style: TextStyle(
                  color: const Color(0xFFB4A28A),
                  fontSize: 14.fs,
                  fontWeight: FontWeight.w500,
                ),
                minFontSize: 7.fs,
                maxFontSize: 14.fs,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

class _TableHeaderCell extends StatelessWidget {
  final String text;
  final int flex;
  final bool isBold;
  final TextStyle? textStyle;
  final bool showRightBorder;
  final bool isSelected;
  final Function(bool) onSelected;

  const _TableHeaderCell({
    super.key,
    required this.text,
    required this.flex,
    this.isBold = false,
    this.textStyle,
    this.showRightBorder = false,
    this.isSelected = false,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelected(true);
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 12.gw),
        width: 56.gw,
        decoration: showRightBorder
            ? const BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Color(0xFFC3B7AE),
                    width: 0.7,
                  ),
                ),
              )
            : BoxDecoration(
                color: isSelected ? const Color(0xffB9936D) : null,
                gradient: isSelected
                    ? const LinearGradient(
                        colors: [
                          Color(0xffEACA9F),
                          Color(0xffB9936D),
                        ],
                      )
                    : null,
                borderRadius: BorderRadius.circular(8),
              ),
        child: Center(
          child: Text(
            text.isEmpty ? "-----" : text,
            textAlign: TextAlign.center,
            style: textStyle?.copyWith(
                  fontWeight: isBold ? FontWeight.bold : null,
                  color: isSelected ? Colors.white : null,
                ) ??
                TextStyle(
                  fontWeight: isBold ? FontWeight.bold : null,
                  color: isSelected ? Colors.white : const Color(0xFF3B416B),
                ),
          ),
        ),
      ),
    );
  }
}

class RechargeCommission extends StatelessWidget {
  const RechargeCommission({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          return AnimationLimiter(
              child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => ScaleAnimation(
                scale: 0.5,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                _buildSection(
                  '返佣明细:',
                  _buildCommissionTable(list: state.commissionRechargeEntity?.list?.reversed.toList()),
                ),
                SizedBox(height: 24.gw),
                _buildSection(
                  '充值返佣规则:',
                  _buildRules(),
                ),
              ],
            ),
          ));
        },
      ),
    );
  }

  Widget _buildTitle(String text) {
    return Row(
      children: [
        Container(
          height: 17.gw,
          width: 5.gw,
          decoration: BoxDecoration(
            color: const Color(0xFFD4B88C),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12.gw),
              bottomRight: Radius.circular(12.gw),
            ),
          ),
        ),
        SizedBox(width: 8.gw),
        Text(
          text,
          style: TextStyle(
            fontSize: 16.fs,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF3B416B),
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.gw),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(height: 8.gw),
          _buildTitle(title),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gw),
            child: child,
          ),
          SizedBox(height: 14.gw),
        ],
      ),
    );
  }

  Widget _buildCommissionTable({required List<CommissionRechargeList>? list}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.gw),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          ...List.generate(list?.length ?? 0, (index) => _buildTableRow(item: list?[index], index: index)),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      height: 30.gw,
      decoration: BoxDecoration(
        color: const Color(0xFFB4A28A),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(6.gw),
          topRight: Radius.circular(6.gw),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildHeaderCell('团队等级', flex: 1),
          Container(
            height: 30.gw,
            width: 1.gw,
            color: Colors.white,
          ),
          _buildHeaderCell('一级下属', flex: 1),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: SizedBox(
        height: 30.gw,
        width: 159.gw,
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14.fs,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTableRow({required CommissionRechargeList? item, required int index}) {
    return Container(
      height: 30.gw,
      decoration: BoxDecoration(
        color: index.isEven ? Colors.white : const Color(0xFFF3EEE8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildCell('${item?.teamLevel}级', flex: 1),
          Container(
            height: 30.gw,
            width: 1.gw,
            color: Colors.white,
          ),
          _buildCell('${((item?.commissionRate ?? 0) * 100).toStringAsFixed(1).replaceAll('.0', '')}%', flex: 1),
        ],
      ),
    );
  }

  Widget _buildCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: SizedBox(
        height: 30.gw,
        width: 159.gw,
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: const Color(0xFF666666),
              fontSize: 14.fs,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRules() {
    final rules = [
      '1.团队等级越高，可领取的下级充值返佣比例越高；',
      '2.下级代理充值成功后，就可以按团队等级对应的比例领取佣金；',
      '3.只能领取直属下级（1级下属）的充值返利，2级和3级无法领取；',
      '4.充值返佣和投注返佣无需分开提取，可以通过代理首页一键提取；',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rules
          .map((rule) => Padding(
                padding: EdgeInsets.only(bottom: 8.gw),
                child: Text(
                  rule,
                  style: TextStyle(
                    fontSize: 14.fs,
                    color: const Color(0xff6A7391),
                    height: 1.5,
                  ),
                ),
              ))
          .toList(),
    );
  }
}
