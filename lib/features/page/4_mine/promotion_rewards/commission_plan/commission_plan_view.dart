import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/base_stateful_page.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_bet_entity.dart';
import '../../../../../core/models/entities/commission_recharge_entity.dart';
import '../../../../../shared/widgets/common_tabbar.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../../shared/widgets/header_content_card.dart';
import '../../../../../shared/widgets/promotion/tab_bar_promotion.dart';
import 'commission_plan_cubit.dart';

class CommissionPlanView extends BasePage {
  const CommissionPlanView({super.key});

  @override
  BasePageState<BasePage> getState() => _CommissionPlanViewState();
}

class _CommissionPlanViewState extends BasePageState with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    pageTitle = 'commission_plan'.tr();
    _tabController = TabController(length: TeamType.values.length, vsync: this);
    context.read<CommissionPlanCubit>().fetchPromotionBetConfig();
    context.read<CommissionPlanCubit>().fetchCommissionPlanRecharge();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('commission_plan'.tr()),
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16.gw),
            child: TabBarPromotion(
              tabController: _tabController,
              tabs: [
                Tab(text: 'betting_commission'.tr()),
                Tab(text: 'recharge_commission'.tr()),
              ],
              children: const [],
            ),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [TabScreen1(), RechargeCommission()],
      ),
    );
  }
}

class TabScreen1 extends StatelessWidget {
  const TabScreen1({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          if (state.commissionBetNetState == NetState.loadingState) return const SizedBox.shrink();

          // Create tab items for CommonTabBar
          final tabItems =
              state.commissionBetEntity?.list?.map((item) => CommonTabBarItem(title: item.type ?? '')).toList() ?? [];

          final selectedIndex = state.commissionBetEntity?.list
                  ?.indexWhere((item) => item.type == state.selectedCommissionBetList?.type) ??
              0;

          return Column(
            children: [
              if (tabItems.isNotEmpty)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
                  child: CommonTabBar(
                    tabItems,
                    style: CommonTabBarStyle.secondary,
                    currentIndex: selectedIndex >= 0 ? selectedIndex : 0,
                    onTap: (index) {
                      if (index < (state.commissionBetEntity?.list?.length ?? 0)) {
                        context
                            .read<CommissionPlanCubit>()
                            .filterCommissionBetList(state.commissionBetEntity?.list?[index].type ?? '');
                      }
                    },
                  ),
                ),
              SizedBox(height: 8.gw),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: AnimationLimiter(
                    child: ListView.builder(
                      itemCount: state.selectedCommissionBetList?.thirdList?.length ?? 0,
                      itemBuilder: (BuildContext context, int index) {
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            scale: 0.5,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 4.gw),
                                child: _buildTableSection(
                                    state.selectedCommissionBetList?.thirdList?[index] ?? CommissionBetListThirdList()),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTableSection(CommissionBetListThirdList item) {
    return Builder(
      builder: (context) {
        // Prepare data for CommonTable
        final columns = [
          CommonTableColumn(
            title: 'team_level'.tr(),
            key: 'level',
            flex: 2,
          ),
          CommonTableColumn(
            title: 'first_level_subordinate'.tr(),
            key: 'first_level',
            flex: 4,
            style: const CommonTableColumnStyle(
              showBadge: true,
              badgeColor: Color(0xFFB4A28A), // badgeColor from CommonTable
            ),
          ),
          CommonTableColumn(
            title: 'second_level_subordinate'.tr(),
            key: 'second_level',
            flex: 4,
            style: const CommonTableColumnStyle(
              showBadge: true,
              badgeColor: Color(0xFFB4A28A), // badgeColor from CommonTable
            ),
          ),
          CommonTableColumn(
            title: 'third_level_subordinate'.tr(),
            key: 'third_level',
            flex: 4,
            style: const CommonTableColumnStyle(
              showBadge: true,
              badgeColor: Color(0xFFB4A28A), // badgeColor from CommonTable
            ),
          ),
        ];

        final tableData = _processTableData(item);

        return HeaderContentCard(
          header: Row(
            children: [
              Container(
                height: 15.gw,
                width: 5.gw,
                decoration: BoxDecoration(
                  color: context.theme.primaryColor,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12.gw),
                    bottomRight: Radius.circular(12.gw),
                  ),
                ),
              ),
              SizedBox(width: 6.gw),
              Text(
                item.platformName ?? '',
                style: context.textTheme.primary.fs16.w500.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            ],
          ),
          content: Padding(
            padding: EdgeInsets.all(10.gw),
            child: CommonTable(
              columns: columns,
              data: tableData,
              headerHeight: 52,
              rowHeight: 30,
            ),
          ),
        );
      },
    );
  }

  List<List<String>> _processTableData(CommissionBetListThirdList item) {
    final List<List<String>> processedData = [];

    // Group by teamLevel
    for (int teamLevel = 0; teamLevel <= 3; teamLevel++) {
      final List<String> rowData = [];

      // Add team level label
      rowData.add('$teamLevel级');

      // Get data for each childLevel (1-3) for current teamLevel
      for (int childLevel = 1; childLevel <= 3; childLevel++) {
        final record = item.list?.firstWhere(
          (element) => element.teamLevel == teamLevel && element.childLevel == childLevel,
        );

        // Add commission rate and cap for each child level
        rowData.add('${((record?.commissionRate ?? 0) * 100).toStringAsFixed(2)}%');
        rowData.add(formatNumberWithChineseUnits((record?.commissionCap ?? 0).toDouble()));
      }

      processedData.add(rowData);
    }
    return processedData;
  }
}

String formatNumberWithChineseUnits(double number) {
  if (number >= 100000000) {
    double value = number / 100000000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}亿';
  } else if (number >= 1000000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  } else if (number >= 10000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  }
  return number.toStringAsFixed(0);
}

class RechargeCommission extends StatelessWidget {
  const RechargeCommission({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          return AnimationLimiter(
              child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => ScaleAnimation(
                scale: 0.5,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                HeaderContentCard(
                  header: _buildTitle('commission_details'.tr()),
                  content: _buildCommissionTable(list: state.commissionRechargeEntity?.list?.reversed.toList()),
                ),
                SizedBox(height: 24.gw),
                HeaderContentCard(
                  header: _buildTitle('recharge_commission_rules'.tr()),
                  content: _buildRules(),
                ),
              ],
            ),
          ));
        },
      ),
    );
  }

  Widget _buildTitle(String text) {
    return Row(
      children: [
        Container(
          height: 17.gw,
          width: 5.gw,
          decoration: BoxDecoration(
            color: const Color(0xFFD4B88C),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12.gw),
              bottomRight: Radius.circular(12.gw),
            ),
          ),
        ),
        SizedBox(width: 8.gw),
        Text(
          text,
          style: TextStyle(
            fontSize: 16.fs,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF3B416B),
          ),
        ),
      ],
    );
  }

  Widget _buildCommissionTable({required List<CommissionRechargeList>? list}) {
    final columns = [
      CommonTableColumn(
        title: 'team_level'.tr(),
        key: 'level',
        flex: 1,
      ),
      CommonTableColumn(
        title: 'first_level_subordinate'.tr(),
        key: 'commission',
        flex: 1,
      ),
    ];

    final tableData = list
            ?.map((item) => [
                  '${item.teamLevel}级',
                  '${((item.commissionRate ?? 0) * 100).toStringAsFixed(1).replaceAll('.0', '')}%',
                ])
            .toList() ??
        [];

    return Padding(
      padding: EdgeInsets.all(10.gw),
      child: CommonTable(
        columns: columns,
        data: tableData,
        headerHeight: 30,
        rowHeight: 30,
      ),
    );
  }

  Widget _buildRules() {
    final rules = [
      'commission_rule_1'.tr(),
      'commission_rule_2'.tr(),
      'commission_rule_3'.tr(),
      'commission_rule_4'.tr(),
    ];

    return Padding(
      padding: EdgeInsets.all(10.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: rules
            .map((rule) => Padding(
                  padding: EdgeInsets.only(bottom: 8.gw),
                  child: Text(
                    rule,
                    style: TextStyle(
                      fontSize: 14.fs,
                      color: const Color(0xff6A7391),
                      height: 1.5,
                    ),
                  ),
                ))
            .toList(),
      ),
    );
  }
}
