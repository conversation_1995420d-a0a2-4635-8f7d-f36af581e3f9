import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_details_entity.dart';
import '../../../../../shared/widgets/easy_loading.dart';

part 'commission_records_state.dart';

class CommissionRecordsCubit extends Cubit<CommissionRecordsState> {
  CommissionRecordsCubit() : super(const CommissionRecordsState());

  void changePaymentTabIndex(int index) {
    resetCommissionDetailsEntity();
    emit(state.copyWith(paymentTabIndex: TeamType.values[index]));
    updatePageNo(1);
    fetchCommissionRecords(teamType: TeamType.values[index]);
  }

  void changeDateType(int index) {
    emit(state.copyWith(dateType: DayType.values[index]));
    updatePageNo(1);
    fetchCommissionRecords(dayType: DayType.values[index]);
  }

  void updatePageNo(int pageNo) {
    if (isClosed) return;
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void resetCommissionDetailsEntity() {
    emit(state.copyWith(resetCommissionDetailsEntity: true, commissionDetailsNetState: NetState.initializeState));
  }

  Future<void> fetchCommissionRecords({String? childUserId, TeamType? teamType, DayType? dayType}) async {
    GSEasyLoading.showLoading();
    try {
      final commissionDetailsEntity = await PromotionApi.fetchCommissionDetail(
        pageNo: state.pageNo,
        childUserId: childUserId,
        type: teamType ?? state.paymentTabIndex,
        dateType: dayType ?? state.dateType,
      );
      GSEasyLoading.dismiss();

      if (commissionDetailsEntity == null) {
        emit(state.copyWith(
          commissionDetailsNetState: NetState.error404State,
        ));
      } else if (commissionDetailsEntity.records?.isEmpty == true) {
        emit(state.copyWith(
          commissionDetailsNetState: NetState.emptyDataState,
        ));
      } else {
        if (state.pageNo == 1) {
          emit(state.copyWith(
            commissionDetailsEntity: commissionDetailsEntity,
            commissionDetailsNetState: commissionDetailsEntity.records?.isNotEmpty == true
                ? NetState.dataSuccessState
                : NetState.emptyDataState,
          ));
        } else {
          final currentRecords = state.commissionDetailsEntity?.records ?? [];
          final newRecords = commissionDetailsEntity.records ?? [];

          commissionDetailsEntity.records = [...currentRecords, ...newRecords];

          emit(state.copyWith(
            commissionDetailsEntity: commissionDetailsEntity,
            commissionDetailsNetState: commissionDetailsEntity.records?.isNotEmpty == true
                ? NetState.dataSuccessState
                : NetState.emptyDataState,
          ));
        }
        emit(state.copyWith(
          isNoMoreData: (commissionDetailsEntity.total ?? 0) <= (commissionDetailsEntity.records?.length ?? 0),
        ));
      }
    } on Error catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        commissionDetailsNetState: NetState.errorShowRefresh,
        error: e.toString(),
      ));
    }
  }

  Future<bool> loadMoreCommissionRecords() async {
    if (isClosed) return false;
    if (state.isNoMoreData) return false;
    emit(state.copyWith(pageNo: state.pageNo + 1));
    await fetchCommissionRecords();
    return !state.isNoMoreData;
  }
}
