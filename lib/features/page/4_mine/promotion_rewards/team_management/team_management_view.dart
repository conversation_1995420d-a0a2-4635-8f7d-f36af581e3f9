import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/utils/extentions.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/team_management/team_management_cubit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/constants/assets.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/team_details_entity.dart';
import '../../../../../core/models/entities/team_members_entity.dart';
import '../../../../../core/theme/themes.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../routers/navigator_utils.dart';
import '../../../../../core/utils/time_util.dart';

class TeamManagementView extends StatefulWidget {
  const TeamManagementView({super.key});

  @override
  State<TeamManagementView> createState() => _TeamManagementViewState();
}

class _TeamManagementViewState extends State<TeamManagementView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _tabController.addListener(() {
      context.read<TeamManagementCubit>().resetTeamData();
      if (_tabController.indexIsChanging) return;
      if (_tabController.index != _tabController.previousIndex) {
        context.read<TeamManagementCubit>().updatePageNo(1);
        switch (_tabController.index) {
          case 0:
            context.read<TeamManagementCubit>().fetchTeamMemberInfo();
            break;
          case 1:
            context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.bet); //bet
            break;
          case 2:
            context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.recharge); //recharge
            break;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('team_management'.tr()),
        centerTitle: true,
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage(Assets.iconBack),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
        actions: [
          _buildTabTypeDropdown(),
          SizedBox(width: 16.gw),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.gw),
        child: TabBarView(
          controller: _tabController,
          children: [
            TeamMember(),
            TeamDetailsBet(),
            TeamDetailsRecharge(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabTypeDropdown() {
    return PopupMenuButton<int>(
      onSelected: (int index) {
        _tabController.animateTo(index);
      },
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<int>(
          value: 0,
          child: Row(
            children: [
              Text(
                'team_member_count'.tr(),
                style: _tabController.index == 0 ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
        PopupMenuItem<int>(
          value: 1,
          child: Row(
            children: [
              Text(
                'team_betting'.tr(),
                style: _tabController.index == 1 ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
        PopupMenuItem<int>(
          value: 2,
          child: Row(
            children: [
              Text(
                'team_profit_loss'.tr(),
                style: _tabController.index == 2 ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
      ],
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(10.gw),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getTabTypeLabel(_tabController.index),
              style: context.textTheme.title.fs13.w500,
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.arrow_drop_down,
              size: 20.gw,
              color: context.colorTheme.textTitle,
            ),
          ],
        ),
      ),
    );
  }

  String _getTabTypeLabel(int index) {
    switch (index) {
      case 0:
        return 'team_member_count'.tr();
      case 1:
        return 'team_betting'.tr();
      case 2:
        return 'team_profit_loss'.tr();
      default:
        return 'team_member_count'.tr();
    }
  }
}

class TeamMember extends StatelessWidget {
  TeamMember({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreTeamMemberInfo();
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using CommonTable with level badges
            if (state.teamMembersNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamMembersNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamMembersEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 3,
                            ),
                            CommonTableColumn(
                              title: 'levels'.tr(),
                              key: 'level',
                              flex: 2,
                              style: CommonTableColumnStyle.levelBadge, // Badge style for levels
                            ),
                            CommonTableColumn(
                              title: 'join_time'.tr(),
                              key: 'joinTime',
                              flex: 3,
                            ),
                            CommonTableColumn(
                              title: 'date'.tr(),
                              key: 'joinDate',
                              flex: 3,
                              style: CommonTableColumnStyle.yellowText,
                            ),
                          ],
                          data: _getTeamMembersTableData(state.teamMembersEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  /// Converts team members data to table format
  List<List<String>> _getTeamMembersTableData(List<TeamMembersRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              _getLevelText(record.level ?? 0), // This will be styled as level badge
              TimeUtil.timeOnlyFromString(record.registerDate), // Join Time (HH:mm:ss)
              TimeUtil.dateOnlyFromString(record.registerDate), // Join Date (yyyy-MM-dd)
            ])
        .toList();
  }

  /// Helper method to get level text
  String _getLevelText(int level) {
    switch (level) {
      case 1:
        return '直属';
      case 2:
        return '2级';
      case 3:
        return '3级';
      case 4:
        return '4级';
      case 5:
        return '5级';
      default:
        return '直属';
    }
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () =>
                  context.read<TeamManagementCubit>().fetchTeamMemberInfo(childUserId: _controller.text.trim()),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TeamDetailsBet extends StatelessWidget {
  TeamDetailsBet({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.bet);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.bet);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using basic CommonTable for Team bet
            if (state.teamDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 3,
                            ),
                            CommonTableColumn(
                              title: 'bet_amount'.tr(),
                              key: 'betAmount',
                              flex: 4,
                            ),
                            CommonTableColumn(
                              title: 'contribution_commission'.tr(),
                              key: 'commission',
                              flex: 2,
                            ),
                          ],
                          data: _getTeamBetTableData(state.teamDetailsEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  /// Converts team bet data to table format
  List<List<String>> _getTeamBetTableData(List<TeamDetailsRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              record.amount?.toString() ?? '0',
              record.commissionAmount?.toString() ?? '0',
            ])
        .toList();
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text.trim(), type: TeamType.bet),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TeamDetailsRecharge extends StatelessWidget {
  TeamDetailsRecharge({super.key});

  final TextEditingController _controller = TextEditingController();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.recharge);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.recharge);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using basic CommonTable for Team Profit
            if (state.teamDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 5,
                            ),
                            CommonTableColumn(
                              title: 'withdrawal_amount'.tr(),
                              key: 'withdrawAmount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'recharge_amount'.tr(),
                              key: 'amount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'profit_loss_amount'.tr(),
                              key: 'profitAmount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'contribution_commission'.tr(),
                              key: 'commissionAmount',
                              flex: 5,
                            ),
                          ],
                          data: _getTeamProfitTableData(state.teamDetailsEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Converts team profit data to table format
  List<List<String>> _getTeamProfitTableData(List<TeamDetailsRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              record.withdrawAmount?.toString() ?? '0',
              record.amount?.toString() ?? '0',
              record.profitAmount?.toString() ?? '0',
              record.commissionAmount?.toString() ?? '0',
            ])
        .toList();
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text.trim(), type: TeamType.recharge),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
