import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/order/order_main_cell.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'channel_type_order_list_cubit.dart';
import 'channel_type_order_list_state.dart';

class ChannelTypeOrderListPage extends BasePage {
  final String gameClassCode;
  final String gameClassName;
  final RecordDateType dateType;
  final String returnAmount;

  const ChannelTypeOrderListPage(
      {super.key,
      required this.gameClassCode,
      required this.gameClassName,
      required this.dateType,
      required this.returnAmount});

  @override
  BasePageState<BasePage> getState() => _ChannelTypeOrderListPageState();
}

class _ChannelTypeOrderListPageState extends BasePageState<ChannelTypeOrderListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();

    pageTitle = widget.gameClassName;

    _tabController = TabController(length: 3, vsync: this, initialIndex: widget.dateType.index)
      ..addListener(() {
        _getData();
      });
    _getData();
  }

  _getData() {
    BlocProvider.of<ChannelTypeOrderListCubit>(context).currentDateTypeChanged(
      RecordDateType.values[_tabController.index],
      widget.gameClassCode,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  Widget mainPageWidget(ChannelTypeOrderListState state) {
    return GSDateTabBarWidget(
      tabController: _tabController,
      children: [
        if (state.netState == NetState.emptyDataState) ...[
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                emptyWidget('empty_popular'.tr()),
              ],
            ),
          )
        ],
        if (state.netState == NetState.dataSuccessState) ...[
          CommonCard(
            margin: 16.gw,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AneText('${'total_profit_loss'.tr()} : ', style: context.textTheme.title.fs20.w600),
                AneText(
                  state.totalWinAmount.formattedMoney,
                  style: context.textTheme.primary.fs20.w600,
                )
              ],
            ),
          ),
          Expanded(
            child: AnimationLimiter(
              child: CommonRefresher(
                enablePullDown: true,
                enablePullUp: false,
                refreshController: refreshController,
                onRefresh: _onRefresh,
                onLoading: () {},
                listWidget: ListView.separated(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  itemBuilder: (context, index) {
                    final model = state.dataList![index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: GSOrderMainListCell(
                            model: model,
                            showTitle: false,
                            onPressed: () => sl<NavigatorService>().push(
                              AppRouter.orderChannelDetailRecordList,
                              arguments: {
                                "gameClassCode": model.gameClassCode,
                                "categoryCode": model.categoryCode,
                                "thirdPlatformId": model.id,
                                "gameClassName": model.title,
                                "dateType": RecordDateType.values[_tabController.index],
                              },
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                  itemCount: state.dataList!.length,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _listener(BuildContext context, ChannelTypeOrderListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<ChannelTypeOrderListCubit, ChannelTypeOrderListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
