import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/order/order_sub_cell.dart';

import 'channel_platform_order_list_state.dart';

class ChannelPlatformOrderListCubit extends Cubit<ChannelPlatformOrderListState> {
  ChannelPlatformOrderListCubit() : super(ChannelPlatformOrderListState().init());

  static const int pageSize = 20;

  fetchListData({
    required String gameClassCode,
    required String categoryCode,
    required String thirdPlatformId,
  }) async {
    GSEasyLoading.showLoading();
    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final result = await ChannelApi.fetchGameMainPlatformRecord(
      gameClassCode: gameClassCode,
      categoryCode: categoryCode,
      thirdPlatformId: thirdPlatformId,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      pageNo: state.pageNo,
      pageSize: pageSize,
    );
    GSEasyLoading.dismiss();

    if (result == null) {
      state.totalBetAmount = 0;
      state.totalWinAmount = 0;
      state.dataList = [];
      state.netState = NetState.error404State;
    } else {
      if (result.page.total <= state.dataList!.length) {
        state.isNoMoreDataState = true;
      }

      if (state.pageNo == 1) {
        state.dataList = result.page.records.map((e) => GSOrderSubViewModel.formOrderChannelListPageRecord(e)).toList();
      } else {
        state.dataList = List.from(state.dataList!)
          ..addAll(result.page.records.map((e) => GSOrderSubViewModel.formOrderChannelListPageRecord(e)).toList());
      }

      state.totalBetAmount = result.totalBetAmount;
      state.totalWinAmount = result.totalWinToday;

      state.netState = state.dataList!.isNotEmpty ? NetState.dataSuccessState : NetState.emptyDataState;
    }
    emit(state.clone());
  }

  void currentDateTypeChanged({
    required RecordDateType type,
    required String gameClassCode,
    required String categoryCode,
    required String thirdPlatformId,
  }) {
    if (type != state.currentDateType) {
      state.pageNo = 1;
      state.isNoMoreDataState = false;
      state.currentDateType = type;
    }

    fetchListData(gameClassCode: gameClassCode, categoryCode: categoryCode, thirdPlatformId: thirdPlatformId);
    emit(state.clone());
  }

  void updatePageNoToNext() {
    state.pageNo += 1;
    emit(state.clone());
  }
}
