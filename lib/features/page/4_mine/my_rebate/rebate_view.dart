import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/invite_code_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'rebate_cubit.dart';
import 'rebate_state.dart';

class RebateView extends BasePage {
  const RebateView({super.key});

  @override
  BasePageState<BasePage> getState() => _RebateViewState();
}

class _RebateViewState extends BasePageState<RebateView> {
  @override
  void initState() {
    super.initState();
    pageTitle = '佣金获取';
  }

  @override
  Widget buildPage(BuildContext context) {
    return SingleChildScrollView(
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: [
              SizedBox(height: 16.gw),
              BlocBuilder<RebateCubit, RebateState>(
                builder: (context, state) => _buildUserInfoCard(state),
              ),
              _buildCommissionList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard(RebateState state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 14.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(width: 1, color: Colors.white),
        color: const Color(0xFFFAF6F3),
      ),
      child: Column(
        children: [
          _buildUserInfo(state),
          _buildInviteCodeSection(state),
        ],
      ),
    );
  }

  Widget _buildUserInfo(RebateState state) {
    return BlocSelector<UserCubit, UserState, ({String avatarUrl, String userName})>(
      selector: (state) => (
        avatarUrl: state.userInfo?.faceId.toString() ?? '',
        userName: state.userInfo?.nickName ?? '',
      ),
      builder: (context, userState) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AuthUtil.getAvatarWidget(context, avatarStr: userState.avatarUrl, size: Size(60.gw, 60.gw)),
              SizedBox(width: 16.gw),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userState.userName,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: const Color(0xff7E6245),
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
              ),
              _buildUserStats(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserStats(RebateState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _buildStatText("下级人数：${state.subordinateCount}"),
        SizedBox(height: 4.gw),
        _buildStatText("获得佣金：${state.commissionEarned}"),
      ],
    );
  }

  Widget _buildStatText(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14.fs,
        color: const Color(0xFF808C9F),
      ),
    );
  }

  Widget _buildInviteCodeSection(RebateState state) {
    return Container(
      padding: EdgeInsets.all(12.gw),
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: Colors.white),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFF0E3D6),
            Color(0xFFFFFCFA),
            Color(0xFFE4D3BD),
            Color(0xFFFFFCF9),
            Color(0xFFEDDECF),
          ],
          stops: [-0.5489, -0.0521, 0.526, 1.0369, 1.5244],
        ),
      ),
      child: BlocSelector<UserCubit, UserState, InviteCodeEntity>(
        selector: (state) => state.inviteInfo ?? InviteCodeEntity(),
        builder: (context, inviteInfo) {
    return Row(
        children: [
          _buildInviteCode(inviteInfo),
          Container(
            width: 1,
            height: 50.gw,
            color: const Color(0xFFE9ECF2),
            margin: EdgeInsets.symmetric(horizontal: 16.gw),
          ),
          _buildInviteLink(inviteInfo),
        ],
      );
  },
),
    );
  }

  Widget _buildInviteCode(InviteCodeEntity model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildSectionTitle("邀请码"),
          SizedBox(height: 8.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                model.inviteCode,
                style: TextStyle(
                  fontSize: 20.fs,
                  color: const Color(0xFF7E6245),
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 8.gw),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: model.inviteCode));
                  GSEasyLoading.showToast('复制成功');
                },
                child: SvgPicture.asset(
                  Assets.iconCopy,
                  width: 18.gw,
                  height: 18.gw,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInviteLink(InviteCodeEntity model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildSectionTitle("邀请链接"),
          SizedBox(height: 8.gw),
          GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: model.spreadUrl));
              GSEasyLoading.showToast('复制成功');
            },
            child: Container(
              width: 84.gw,
              height: 25.gw,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Assets.bgRebateButton),
                  fit: BoxFit.fill,
                ),
              ),
              child: Text(
                "复制链接",
                style: TextStyle(
                  fontSize: 14.fs,
                  color: const Color(0xFF7E6245),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: const Color(0xff6A7391),
            fontSize: 14.fs,
          ),
    );
  }

  Widget _buildCommissionList() {
    return BlocBuilder<RebateCubit, RebateState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(16.gw, 16.gw, 16.gw, 8.gw),
              child: Text(
                "佣金记录",
                style: TextStyle(
                  fontSize: 18.fs,
                  fontWeight: FontWeight.w600,
                  // color: const Color(0xFF6A7391),
                  color: const Color(0xFF6A7391),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 14.gw),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                boxShadow: const [
                  BoxShadow(
                    blurRadius: .5,
                    spreadRadius: .5,
                    offset: Offset(0, 2),
                    color: Color(0x668FA0BF),
                  )
                ],
              ),
              constraints: BoxConstraints(
                minHeight: 150.gw,
                maxHeight: state.commissionRecords.isEmpty ? 300.gw : 400.gw,
              ),
              child: state.commissionRecords.isEmpty ? _buildEmptyState() : _buildCommissionTable(state),
            ),
            if (state.isLoadingMore)
              Padding(
                padding: EdgeInsets.all(8.gw),
                child: const Center(
                  child: CircularProgressIndicator.adaptive(),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return const EmptyWidget(title: '暂无佣金记录');
  }

  Widget _buildCommissionTable(RebateState state) {
    final totalWidth = GSScreenUtil().screenWidth - 28.gw - 30;
    final double nicknameWidth = totalWidth * 0.25;
    final double amountWidth = totalWidth * 0.3;
    final double timeWidth = totalWidth * 0.45;
    return Scrollbar(
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
            context.read<RebateCubit>().fetchCommissionRecords();
          }
          return true;
        },
        child: RefreshIndicator(
          onRefresh: () => context.read<RebateCubit>().fetchCommissionRecords(refresh: true),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: DataTable(
              columnSpacing: 5,
              horizontalMargin: 16,
              headingRowHeight: 50.gw,
              dataRowMinHeight: 40.gw,
              dataRowMaxHeight: 40.gw,
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                ),
              ),
              border: const TableBorder(
                verticalInside: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                horizontalInside: BorderSide(color: Color(0xFFE0E0E0), width: 1),
              ),
              columns: [
                DataColumn(
                  label: SizedBox(
                    width: nicknameWidth,
                    child: Text(
                      "好友昵称",
                      style: TextStyle(
                        fontSize: 14.fs,
                        color: const Color(0xFFCDB296),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                DataColumn(
                  label: SizedBox(
                    width: amountWidth,
                    child: Text(
                      "佣金金额",
                      style: TextStyle(
                        fontSize: 14.fs,
                        color: const Color(0xFFCDB296),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                DataColumn(
                  label: SizedBox(
                    width: timeWidth,
                    child: Text(
                      "发放时间",
                      style: TextStyle(
                        fontSize: 14.fs,
                        color: const Color(0xFFCDB296),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
              rows: state.commissionRecords
                  .map((record) => DataRow(
                        cells: [
                          record.userNo,
                          record.changeAmount.toString(),
                          record.createTime,
                        ].asMap().entries.map((entry) {
                          final index = entry.key;
                          final text = entry.value;
                          return DataCell(
                            Container(
                              width: index == 0
                                  ? nicknameWidth
                                  : index == 1
                                      ? amountWidth
                                      : timeWidth,
                              alignment: Alignment.center,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Text(
                                  text,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: index == 0 || index == 2 ? TextAlign.left : TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 12.fs,
                                    color: const Color(0xFF6A7391),
                                  ),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ))
                  .toList(),
            ),
          ),
        ),
      ),
    );
  }
}
