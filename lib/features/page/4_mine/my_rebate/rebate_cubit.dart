import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/agent.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';
import 'rebate_state.dart';

class RebateCubit extends Cubit<RebateState> {
  static const int pageSize = 10;

  RebateCubit() : super(RebateState()) {
    initialize();
  }

  void initialize() async {
    if (sl<UserCubit>().state.inviteInfo == null) await sl<UserCubit>().fetchInviteInfo();

    await Future.wait([fetchSubordinateInfo(), fetchCommissionRecords()]);

    emit(state.clone());
  }

  Future<void> fetchSubordinateInfo() async {
    if (!sl<UserCubit>().state.isLogin) return;

    final result = await AgentApi.fetchSubordinateInfo();
    if (result != null) {
      state.subordinateCount = result.subordinateCount;
      state.commissionEarned = result.commissionEarned;
      emit(state.clone());
    }
  }

  Future<void> fetchCommissionRecords({bool refresh = false}) async {

    if (!sl<UserCubit>().state.isLogin) return;

    if (refresh) {
      state.currentPage = 1;
      state.commissionRecords = [];
    }

    if (state.isLoadingMore || (!refresh && state.currentPage > state.totalPages)) {
      return;
    }

    state.isLoadingMore = true;

    emit(state.clone());

    final result = await AgentApi.fetchCommissionRecords(
      pageNo: state.currentPage,
      pageSize: pageSize,
    );

    state.isLoadingMore = false;
    emit(state.clone());
    if (result != null) {
      if (refresh) {
        state.commissionRecords = result.records;
      } else {
        state.commissionRecords.addAll(result.records);
      }

      state.currentPage++;
      state.totalPages = result.pages;
    }

    state.isLoadingMore = false;
    emit(state.clone());
  }

}
