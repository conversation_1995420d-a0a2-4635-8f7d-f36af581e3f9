import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/promotion_banner_entity.dart';

class MineV2State extends BaseState with EquatableMixin {
  final List<PromotionBannerList> promoBanners;
  final SimplyNetStatus promoBannerFetchStatus;

  MineV2State({
    this.promoBanners = const [],
    this.promoBannerFetchStatus = SimplyNetStatus.idle,
  });

  MineV2State copyWith({
    List<PromotionBannerList>? promoBanners,
    SimplyNetStatus? promoBannerFetchStatus,
  }) {
    return MineV2State(
      promoBanners: promoBanners ?? this.promoBanners,
      promoBannerFetchStatus: promoBannerFetchStatus ?? this.promoBannerFetchStatus,
    );
  }

  @override
  List get props => [
        promoBanners,
        promoBannerFetchStatus,
      ];
}
