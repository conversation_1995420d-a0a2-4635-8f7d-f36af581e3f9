import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/invite_code_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/video_coupon/video_coupon_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_sheet.dart';
import 'package:wd/shared/widgets/container/golden_border_container.dart';
import 'package:wd/shared/widgets/container/golden_section_card.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/mine/mine_app_bar.dart';
import 'package:wd/shared/widgets/mine/mine_video_vip_remain_widget.dart';
import 'package:wd/shared/widgets/mine/mine_vip_section.dart';
import '../main/screens/main_screen_cubit.dart';
import 'mine_v2_cubit.dart';
import 'mine_v2_state.dart';

/// 我的
class MineV2Page extends StatefulWidget {
  const MineV2Page({super.key});

  @override
  State<StatefulWidget> createState() => _MineV2PageState();
}

class _MineV2PageState extends State<MineV2Page> with SingleTickerProviderStateMixin {
  final double _marginHorizontal = 10.gw;

  // final double _marginTop = 10.gw;
  final ScrollController _scrollController = ScrollController();
  late AnimationController _balanceRefreshController;
  bool hasUpdate = false;

  @override
  void initState() {
    super.initState();
    _balanceRefreshController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
    SystemUtil.appUpdate().then((value) => setState(() => hasUpdate = value.$1));
  }

  @override
  void dispose() {
    _balanceRefreshController.dispose();
    super.dispose();
  }

  void _handleWalletRefresh() async {
    _balanceRefreshController.repeat();
    await GameUtil.transferOutAllPlatform();
    sl<UserCubit>().fetchUserVip();
    _balanceRefreshController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
      listenWhen: (previous, current) => current.isLogin != previous.isLogin,
      listener: (context, userState) {
        if (!userState.isLogin) {
          sl<VideoCouponCubit>().resetState();
        }
      },
      child: BlocBuilder<MineV2Cubit, MineV2State>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.white,
            resizeToAvoidBottomInset: false,
            body: SafeArea(
              bottom: false,
              child: Stack(
                children: [
                  Container(
                    color: const Color(0xffF4F4F4),
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: AnimationLimiter(
                        child: Column(
                          children: AnimationConfiguration.toStaggeredList(
                            duration: const Duration(milliseconds: 375),
                            childAnimationBuilder: (widget) => SlideAnimation(
                              horizontalOffset: 50.0,
                              child: FadeInAnimation(child: widget),
                            ),
                            children: [
                              _buildMemberWidget(state),
                              SizedBox(height: 6.gw),
                              _buildBalanceAndVipWidget(state),
                              _buildGradientColumn(
                                  column: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildChatEntrance(),
                                  SizedBox(height: 11.gw),
                                  _buildFunctionSection(
                                    children: _buildAccountFunctions(),
                                  ),
                                  SizedBox(height: 11.gw),
                                  _buildOtherFunctionListWidget(),
                                  BlocSelector<UserCubit, UserState, bool>(
                                      selector: (state) => state.isLogin,
                                      builder: (context, isLogin) {
                                        return isLogin ? _buildLogoutButton() : const SizedBox.shrink();
                                      }),
                                  _buildVersionSection(),
                                ],
                              )),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  MineScrollFadeAppBar(scrollController: _scrollController)
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 会员名称
  Widget _buildMemberWidget(MineV2State state) {
    return InkWell(
      onTap: () => sl<NavigatorService>().push(
        sl<UserCubit>().state.isLogin ? AppRouter.userProfile : AppRouter.login,
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 16),
        child: BlocSelector<UserCubit, UserState, ({String avatarUrl, String userName})>(
          selector: (state) => (
            avatarUrl: state.userInfo?.faceId.toString() ?? '',
            userName: !state.isLogin ? "sign_in_tips".tr() : state.userInfo?.nickName ?? '',
          ),
          builder: (context, userState) {
            return Row(
              children: [
                AuthUtil.getAvatarWidget(context, avatarStr: userState.avatarUrl, size: Size(48.gw, 48.gw)),
                SizedBox(width: 10.gw),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(userState.userName,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: const Color(0xff3A3A3A),
                                  fontWeight: FontWeight.w700,
                                )),
                        if (!sl<UserCubit>().state.isLogin) ...[
                          const SizedBox(width: 5),
                          const Icon(Icons.arrow_forward_ios_sharp, color: Color(0x666A7391), size: 16),
                        ],
                      ],
                    ),
                    if (sl<UserCubit>().state.isLogin) ...[
                      SizedBox(height: 5.gw),
                      _buildInviteCodeRow(),
                    ]
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 邀请码模块
  Widget _buildInviteCodeRow() {
    return BlocSelector<UserCubit, UserState, InviteCodeEntity>(
        selector: (state) => state.inviteInfo ?? InviteCodeEntity(),
        builder: (context, inviteInfo) {
          if (StringUtil.isEmpty(inviteInfo.inviteCode)) return const SizedBox.shrink();
          return Row(
            children: [
              Text(
                "invite_code_prefix".tr(),
                style: Theme
                    .of(context)
                    .textTheme
                    .titleSmall
                    ?.copyWith(
                  color: const Color(0xff808C9F),
                  fontSize: 12.fs,
                ),
              ),

              InkWell(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: inviteInfo.inviteCode));
                  GSEasyLoading.showToast('复制成功');
                },
                child: Row(
                  children: [
                    AutoSizeText(
                      inviteInfo.inviteCode,
                      style: Theme
                          .of(context)
                          .textTheme
                          .titleSmall
                          ?.copyWith(
                        color: const Color(0xff808C9F),
                        fontSize: 12.fs,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.gw),
                      child: SvgPicture.asset(
                        Assets.iconCopy_v2,
                        colorFilter: const ColorFilter.mode(Color(0xff5A5C69), BlendMode.srcIn),
                        width: 12.gw,
                        height: 12.gw,
                      ),
                    ),
                  ],
                ),
              ),


            ],
          );
        });
  }

  /// 余额和vip模块
  Widget _buildBalanceAndVipWidget(MineV2State state) {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.fromLTRB(10.gw, 0.gw, 10.gw, 0),
          padding: EdgeInsets.fromLTRB(20.gw, 16.gw, 20.gw, 5.gw),
          width: double.infinity,
          decoration: const BoxDecoration(
              image: DecorationImage(image: AssetImage("assets/images/mine/v2/bg_mine_vip.png"), fit: BoxFit.fill)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBalanceRow(state),
              const MineVipSection(),
            ],
          ),
        ),
        if (GlobalConfig.needShowVideoPage()) ...[
          _buildDays(),
        ],
      ],
    );
  }

  /// 余额模块
  Widget _buildBalanceRow(MineV2State state) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: _handleWalletRefresh,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "rmb_wallet".tr(),
            style: TextStyle(color: const Color(0xff4F536E), fontSize: 12.fs, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 4.5.gw),
          BlocSelector<UserCubit, UserState, bool>(
            selector: (state) => state.isLogin,
            builder: (context, isLogin) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isLogin)
                    Text(
                      "¥",
                      style: TextStyle(color: const Color(0xff4F536E), fontSize: 18.fs, fontWeight: FontWeight.w500),
                    ),
                  SizedBox(width: 1.gw),
                  BlocSelector<UserCubit, UserState, String?>(
                    selector: (state) => state.balanceInfo?.accountMoney.formattedMoney,
                    builder: (context, balance) {
                      return Text(
                        balance ?? '-',
                        style: TextStyle(color: const Color(0xff4F536E), fontSize: 22.fs, fontWeight: FontWeight.w700),
                      );
                    },
                  ),
                  SizedBox(width: 4.gw),
                  if (isLogin)
                    RotationTransition(
                      turns: _balanceRefreshController.drive(
                        CurveTween(curve: Curves.easeInOutCubic),
                      ),
                      child: SvgPicture.asset(
                        "assets/images/mine/icon_mine_refresh_golden.svg",
                        width: 16.gw,
                        height: 16.gw,
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// 观影天数
  Widget _buildDays() {
    return Positioned(
      top: 0.gw,
      right: 10.gw,
      child: Transform.translate(
        offset: Offset(4.gw, -3.gw),
        child: BlocSelector<UserCubit, UserState, int>(
          selector: (state) => state.videoVipInfo?.days ?? 0,
          builder: (context, days) {
            return MineVideoVipRemainWidget(
              days: days,
              onTap: () => AuthUtil.checkIfLogin(() => sl<NavigatorService>().push(AppRouter.videoCoupon)),
            );
          },
        ),
      ),
    );
  }

  _buildChatEntrance() {
    // return Container(width: 100, height: 800, color: Colors.redAccent,);
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 6.gw),
      child: GestureDetector(
        onTap: () => AuthUtil.checkIfLogin(() async {
          sl<MainScreenCubit>().goToChatPage();
        }),
        child: Image.asset(
          "assets/images/mine/banner_chat.png",
          width: double.infinity,
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  _buildGradientColumn({required Column column}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(0, 12.gw, 0, 8.gw),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            // Colors.redAccent,// 0%
            Color(0xFFFFFFFF), // 0%
            Color(0xFFE5D6C8), // 3.89%
            Color(0xFFF4F4F4), // 14.28%
            Color(0xFFF1E7DE), // 76.5%
            Color(0xFFF4F4F4), // 100%
          ],
          stops: [
            0.0,
            0.0389,
            0.1428,
            0.765,
            1.0,
          ],
        ),
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: column,
    );
  }

  Widget _buildFunctionSection({required List<FunctionItem> children}) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.gw),
        // height: 250.gw,
        // width: GSScreenUtil().scaleWidth,
        child: GoldenSectionCard(
          title: "我的功能",
          child: GridView.builder(
            padding: EdgeInsets.only(top: 25.gw),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 0,
              crossAxisSpacing: 0,
              // 添加宽高比控制
              childAspectRatio: 1.0, // 根据实际UI需求调整这个值
            ),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: children.length,
            // items是你的数据源
            itemBuilder: (context, index) {
              return _buildFunctionButton(children[index]);
            },
          ),
        ));
  }

  List<FunctionItem> _buildAccountFunctions() {
    return [
      FunctionItem(
        text: "statement".tr(),
        imageAssets: "Assets.statementIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.userStatement)),
      ),
      FunctionItem(
        text: "orders".tr(),
        imageAssets: "Assets.orderIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.order)),
      ),
      FunctionItem(
        text: "my_notifications".tr(),
        imageAssets: "Assets.newsIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.notifications)),
      ),
      FunctionItem(
        text: "vip_center".tr(),
        imageAssets: "Assets.vipIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.vipCenter)),
      ),
      FunctionItem(
        text: "my_wallet".tr(),
        imageAssets: "Assets.walletIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.myWallet)),
      ),
      FunctionItem(
        text: "promotion_earnings".tr(),
        imageAssets: "Assets.rebateIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.promotionRewards)),
        // onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.myRebate)),
      ),
      FunctionItem(
        text: "partnership".tr(),
        imageAssets: "Assets.agentRecruitmentIcon",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.agentRecruitment)),
      ),
    ];
  }

  Widget _buildFunctionButton(FunctionItem item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: item.onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(item.imageAssets, width: 48.gw, height: 48.gw),
          SizedBox(height: 8.gw),
          Text(
            item.text,
            style: TextStyle(fontSize: 13.fs, color: const Color(0xff50576D)),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherFunctionListWidget() {
    final dataList = _buildHelpCenterFunctions();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.gw),
      child: GoldenBorderContainer(
        offsetY: 2,
        child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return _buildOtherListCell(dataList[index]);
            },
            separatorBuilder: (context, index) => Container(
                  height: 0.5,
                  color: const Color(0xffE7E7E7),
                ),
            itemCount: dataList.length),
      ),
    );
  }

  _buildOtherListCell(FunctionItem item) {
    return InkWell(
      onTap: item.onPressed,
      child: SizedBox(
        height: 48.gw,
        child: Row(
          children: [
            Image.asset(item.imageAssets, width: 28.gw, height: 28.gw),
            SizedBox(width: 12.gw),
            Text(
              item.text,
              style: TextStyle(color: const Color(0xff666D81), fontSize: 14.fs, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            SvgPicture.asset(
              "assets/images/common/icon_next.svg",
              width: 12.gw,
              height: 12.gw,
              colorFilter: const ColorFilter.mode(Color(0xffA48669), BlendMode.srcIn),
            ),
          ],
        ),
      ),
    );
  }

  List<FunctionItem> _buildHelpCenterFunctions() {
    return [
      if (kDebugMode)
        FunctionItem(
          text: "提现进度".tr(),
          imageAssets: "Assets.iconNotice",
          onPressed: () => sl<NavigatorService>().push(AppRouter.transactWithdrawProgress),
        ),
      FunctionItem(
        text: "announcement".tr(),
        imageAssets: "Assets.iconNotice",
        onPressed: () => sl<NavigatorService>().push(AppRouter.announcementList),
      ),
      FunctionItem(
        text: "sponsors".tr(),
        imageAssets: "Assets.iconSponsor",
        onPressed: () => sl<NavigatorService>().push(AppRouter.sponsorList),
      ),
      FunctionItem(
        text: "match_result".tr(),
        imageAssets: "Assets.iconMatch",
        onPressed: () => GSEasyLoading.showToast('即将开放'),
      ),
      FunctionItem(
        text: "e_wallet_guide".tr(),
        imageAssets: "Assets.iconCourse",
        onPressed: () => sl<NavigatorService>().push(AppRouter.courseTutorialList),
      ),
      FunctionItem(
        text: "bet_guide".tr(),
        imageAssets: "Assets.iconBetGuide",
        onPressed: () => sl<NavigatorService>().push(AppRouter.betGuidesList),
      ),
      FunctionItem(
        text: "tutorial".tr(),
        imageAssets: "Assets.iconBeginner",
        onPressed: () => sl<NavigatorService>().push(AppRouter.tutorialList),
      ),
      FunctionItem(
        text: "about_us".tr(),
        imageAssets: "Assets.iconAbout",
        showBadge: hasUpdate,
        onPressed: () => sl<NavigatorService>().push(AppRouter.aboutList),
      ),
      FunctionItem(
        text: "download_app".tr(),
        imageAssets: "Assets.iconAppDownload",
        onPressed: () => SystemUtil.goToAppDownload(),
      ),
      if (GlobalConfig.needShowVideoPage())
        FunctionItem(
          text: "video_coupon".tr(),
          imageAssets: "Assets.iconAppCoupon",
          onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.videoCoupon)),
        ),
    ];
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.only(top: 20.0),
      child: CommonButton(
        title: "logout".tr(),
        width: GSScreenUtil().screenWidth - _marginHorizontal * 2,
        height: 45.gw,
        onPressed: () {
          CommonSheet.show(
            context: context,
            title: 'logout_tips'.tr(),
            buttons: [
              SheetButtonItem(
                title: 'logout'.tr(),
                titleStyle: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.redAccent),
                onPressed: () {
                  Navigator.pop(context);
                  context.read<MineV2Cubit>().logout();
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildVersionSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: Text(
        'v${SystemUtil.version}_${SystemUtil.buildNumber}',
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  void _handleAuthAction(VoidCallback action) {
    AuthUtil.checkIfLogin(action);
  }
}

class FunctionItem {
  final String text;
  final String imageAssets;
  final VoidCallback onPressed;
  final bool showBadge;

  FunctionItem({
    required this.text,
    required this.imageAssets,
    required this.onPressed,
    this.showBadge = false,
  });
}
