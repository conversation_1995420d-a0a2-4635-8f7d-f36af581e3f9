import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/models/entities/platform_amount_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/account_security/payment_list/payment_list_state.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import '../../main/screens/main_screen_cubit.dart';
import 'my_wallet_state.dart';

class MyWalletCubit extends Cubit<MyWalletState> {
  MyWalletCubit() : super(MyWalletState().init()) {
    fetchUserBalance();
  }

  loadWallets() async {
    final list = await ChannelApi.getAllPlatformAmount();
    if (!isClosed) { // getAllPlatformAmount耗时太久可能存在关闭界面后触发emit
      emit(state.copyWith(dataList: list, isFirstFetchingAll: false));
    }
  }

  fetchUserBalance() async {
    await sl<UserCubit>().fetchUserBalance();
  }


  // 跳转到存款页面逻辑
  void onGoToDepositPage() {
    sl<MainScreenCubit>().goToTransactPage(isDeposit: true);
    sl<NavigatorService>().pop();
  }

  // 跳转到取款页面逻辑
  void onGoToWithdrawPage() {
    sl<MainScreenCubit>().goToTransactPage(isDeposit: false);
    sl<NavigatorService>().pop();
  }

  // 跳转到账变页面逻辑
  void onGoToTransferPage() {
    sl<NavigatorService>().push(AppRouter.userStatement);
  }

  // 跳转到收付款页面逻辑
  void onGoToBankAccountList(context) {
    if (sl<UserCubit>().state.userInfo!.hasFundPwd) {
      sl<NavigatorService>().push(AppRouter.userWithdrawList, arguments: PaymentType.bankCard);
    } else {
      SystemUtil.showSetFundPwdDialog(context);
    }
  }

  onRefreshOneClick() async {
    emit(state.copyWith(isFetchingTransferAll: true));
    if (state.dataList!.isEmpty) {
      await loadWallets();
    }

    List<int> existAmountPlatformIds = [];
    for (PlatformAmountEntity model in state.dataList!) {
      if (model.amount > 0) {
        existAmountPlatformIds.add(model.platformId);
      }
    }
    if (existAmountPlatformIds.isNotEmpty) {
      await GameUtil.transferOutFromLoginPlatform(platformIds: existAmountPlatformIds);
      fetchUserBalance();
      await loadWallets();
    } else {
      await GameUtil.transferOutAllPlatform();
    }

    emit(state.copyWith(isFetchingTransferAll: false));
  }

  void onChangeGameCentType(int index) {
    // emit(state.clone()..currentWalletIndex = index);
    // 切换场馆逻辑
  }

  bool isTransferring = false;

  onRefresh({
    required AnimationController animationController,
    required String channelCode,
    required PlatformAmountEntity model,
  }) async {
    if (model.amount <= 0) {
      return;
    }
    if (isTransferring) return GSEasyLoading.showToast("请勿频繁点击");
    isTransferring = true;
    animationController.repeat();

    // 找到该 model 在列表中的位置
    final index = state.dataList!.indexOf(model);
    // 复制列表并更新特定元素的 isLoading 属性
    List updatedList = List.from(state.dataList!);
    updatedList[index] = updatedList[index].copyWith(isLoading: true);
    emit(state.copyWith(dataList: updatedList));

    final flag =
        await ChannelApi.transferGameAmount(AmountTransferType.out, amount: model.amount, platformId: model.platformId);
    if (flag) {
      // 复制列表并更新特定元素的 isLoading 属性
      updatedList[index] = updatedList[index].copyWith(isLoading: false, amount: 0.0);
      emit(state.copyWith(dataList: updatedList));
    }
    animationController.reset();

    await fetchUserBalance();
    isTransferring = false;
    loadWallets();
  }
}
