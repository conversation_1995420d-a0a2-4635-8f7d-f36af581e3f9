import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';

class CourseTutorialListPage extends BasePage {
  const CourseTutorialListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _TutorialListPageState();
}

class _TutorialListPageState extends BasePageState<CourseTutorialListPage> with SingleTickerProviderStateMixin {
  static const _animationDuration = Duration(milliseconds: 375);
  static const _horizontalOffset = 50.0;

  late final List<TutorialItem> _tutorialItems;

  @override
  void initState() {
    super.initState();
    pageTitle = "e_wallet_guide".tr();
    _tutorialItems = TutorialConfig.getTutorialItems(kChannel);
  }

  Widget mainPageWidget() {
    return AnimationLimiter(
      child: ListView(
        padding: EdgeInsets.only(top: 10.gh),
        children: AnimationConfiguration.toStaggeredList(
          duration: _animationDuration,
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: _horizontalOffset,
            child: FadeInAnimation(child: widget),
          ),
          children: _tutorialItems.map((item) => _buildTutorialCell(item)).toList(),
        ),
      ),
    );
  }

  Widget _buildTutorialCell(TutorialItem item) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => _navigateToTutorial(item),
          child: Container(
            decoration: const BoxDecoration(color: Colors.white),
            height: 60.gw,
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            child: Row(
              children: [
                _buildIcon(item.icon),
                Text(
                  item.title.tr(),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                ),
                const Spacer(),
                _buildNextIcon(),
              ],
            ),
          ),
        ),
        SizedBox(height: 10.gw),
      ],
    );
  }


  Widget _buildIcon(String iconPath) => Padding(
        padding: EdgeInsets.only(right: 8.gw),
        child: Image.asset(iconPath, width: 30.gw, height: 30.gw),
      );

  Widget _buildNextIcon() => Image.asset(
        Assets.nextIcon,
        width: 20.gw,
        height: 20.gw,
      );

  void _navigateToTutorial(TutorialItem item) {
    sl<NavigatorService>().push(AppRouter.commonHtmlView, arguments: {
      "title": item.title.tr(),
      "path": item.path,
    });
  }

  @override
  Widget buildPage(BuildContext context) {
    return mainPageWidget();
  }
}

class TutorialItem {
  final String title;
  final String icon;
  final String path;

  const TutorialItem({
    required this.title,
    required this.icon,
    required this.path,
  });
}

class TutorialConfig {
  static const _allTutorialItems = [
    TutorialItem(
      title: "ok_pay_tutorial",
      icon: Assets.iconOkPay,
      path: "assets/html/tutorials/course/ok_pay_tutorial.html",
    ),
    TutorialItem(
      title: "go_pay_tutorial",
      icon: Assets.iconGoPay,
      path: "assets/html/tutorials/course/go_pay_tutorial.html",
    ),
    TutorialItem(
      title: "to_pay_tutorial",
      icon: Assets.iconToPay,
      path: "assets/html/tutorials/course/to_pay_tutorial.html",
    ),
    TutorialItem(
      title: "kd_pay_tutorial",
      icon: Assets.iconKdPay,
      path: "assets/html/tutorials/course/kd_pay_tutorial.html",
    ),
    TutorialItem(
      title: "cb_pay_tutorial",
      icon: Assets.iconCBPay,
      path: "assets/html/tutorials/course/cb_pay_tutorial.html",
    ),
    TutorialItem(
      title: "jd_pay_tutorial",
      icon: Assets.iconJDPay,
      path: "assets/html/tutorials/course/jd_pay_tutorial.html",
    ),
    TutorialItem(
      title: "u_pay_tutorial",
      icon: Assets.iconUPay,
      path: "assets/html/tutorials/course/u_pay_tutorial.html",
    ),
    TutorialItem(
      title: "ab_pay_tutorial",
      icon: Assets.iconAbPay,
      path: "assets/html/tutorials/course/ab_pay_tutorial.html",
    ),
    TutorialItem(
      title: "365_pay_tutorial",
      icon: Assets.icon365Pay,
      path: "assets/html/tutorials/course/365_pay_tutorial.html",
    ),
  ];

  static const _jsTutorialItems = [
    TutorialItem(
      title: "kd_pay_tutorial",
      icon: Assets.iconKdPay,
      path: "assets/html/tutorials/course/kd_pay_tutorial.html",
    ),
    TutorialItem(
      title: "jd_pay_tutorial",
      icon: Assets.iconJDPay,
      path: "assets/html/tutorials/course/jd_pay_tutorial.html",
    ),
    TutorialItem(
      title: "to_pay_tutorial",
      icon: Assets.iconToPay,
      path: "assets/html/tutorials/course/to_pay_tutorial.html",
    ),
    TutorialItem(
      title: "ab_pay_tutorial",
      icon: Assets.iconAbPay,
      path: "assets/html/tutorials/course/ab_pay_tutorial.html",
    ),
    TutorialItem(
      title: "365_pay_tutorial",
      icon: Assets.icon365Pay,
      path: "assets/html/tutorials/course/365_pay_tutorial.html",
    ),
    TutorialItem(
      title: "cb_pay_tutorial",
      icon: Assets.iconCBPay,
      path: "assets/html/tutorials/course/cb_pay_tutorial.html",
    ),
  ];

  static List<TutorialItem> getTutorialItems(String channel) {
    switch (channel) {
      case 'JS':
        return _jsTutorialItems;
      default:
        return _allTutorialItems;
    }
  }
}
