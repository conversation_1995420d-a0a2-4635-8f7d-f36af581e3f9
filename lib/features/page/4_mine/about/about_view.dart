import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/app_version_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/dialog/app_update_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

class AboutListPage extends BasePage {
  const AboutListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AboutListPageState();
}

class _AboutListPageState extends BasePageState<AboutListPage> {
  @override
  void initState() {
    super.initState();
    pageTitle = "关于我们";
  }

  @override
  Widget buildPage(BuildContext context) {
    final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        bottom: keyboardHeight,
        left: 14.gw,
        right: 14.gw,
        top: 14.gw,
      ),
      child: AnimationLimiter(
        child: Column(
          children: [
            _buildLogoSection(),
            SizedBox(height: 10.gw),
            _buildAboutList(),
            if (!SystemUtil.isWeb()) ...[
              SizedBox(height: 10.gw),
              _buildVersionSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Container(
      height: 180.gw,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 80.gw,
            width: 80.gw,
            child: Image.asset(
              'assets/images/logo/logo.png',
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(height: 16.gw),
          Text(
            '当前版本: ${SystemUtil.version}_${SystemUtil.buildNumber}',
            style: TextStyle(
              fontSize: 14.fs,
              color: const Color(0xFF808C9F),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutList() {
    final aboutItems = [
      _AboutItem(
        title: "responsible_gaming".tr(),
        path: "assets/html/about/responsible_gaming.html",
      ),
      _AboutItem(
        title: "withdrawal_assistance".tr(),
        path: "assets/html/about/withdrawal_assistance.html",
      ),
      _AboutItem(
        title: "deposit_help".tr(),
        path: "assets/html/about/deposit_help.html",
      ),
      _AboutItem(
        title: "alliance_agreement".tr(),
        path: "assets/html/about/alliance_agreement.html",
      ),
      _AboutItem(
        title: "partners".tr(),
        path: "assets/html/about/partners.html",
      ),
      _AboutItem(
        title: "contact_us".tr(),
        path: "assets/html/about/contact_us.html",
      ),
      _AboutItem(
        title: "about_us".tr(),
        path: "assets/html/about/about_us.html",
      ),
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: aboutItems
              .map((item) => _buildListItem(
                    context,
                    item.title,
                    callback: () => _navigateToHtmlView(item),
                  ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildVersionSection() {
    return FutureBuilder<(bool, AppVersionEntity?)>(
      future: SystemUtil.appUpdate(),
      builder: (context, snapshot) {
        final (shouldShowUpdateDialog, model) = snapshot.data ?? (false, null);
        return GestureDetector(
          onTap: () => _handleVersionTap(shouldShowUpdateDialog, model),
          child: Container(
            height: 44.gw,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 14.gw),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.gw),
            ),
            child: Row(
              children: [
                Text(
                  '版本更新',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const Spacer(),
                _buildVersionInfo(context, shouldShowUpdateDialog),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildVersionInfo(BuildContext context, bool shouldShowUpdateDialog) {
    return Row(
      children: [
        if (shouldShowUpdateDialog)
          Container(
            width: 8.gw,
            height: 8.gw,
            decoration: const BoxDecoration(
              color: Color(0xFFFF0004),
              shape: BoxShape.circle,
            ),
          ),
        SizedBox(width: 4.gw),
        Text(
          shouldShowUpdateDialog ? "发现新版本" : "",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w400,
                color: const Color(0xff808C9F),
                fontFamily: 'MiSans',
              ),
        ),
        SizedBox(width: 7.gw),
        Image.asset(
          Assets.nextIcon,
          width: 20.gw,
          height: 20.gw,
        ),
      ],
    );
  }

  Widget _buildListItem(
    BuildContext context,
    String title, {
    VoidCallback? callback,
  }) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: callback,
          child: SizedBox(
            height: 55.gh,
            child: Row(
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const Spacer(),
                Image.asset(
                  Assets.nextIcon,
                  width: 20.gw,
                  height: 20.gw,
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 1.gw),
      ],
    );
  }

  void _navigateToHtmlView(_AboutItem item) {
    sl<NavigatorService>().push(
      AppRouter.commonHtmlView,
      arguments: {
        "title": item.title,
        "path": item.path,
      },
    );
  }

  void _handleVersionTap(bool shouldShowUpdateDialog, AppVersionEntity? model) {
    if (shouldShowUpdateDialog && model != null) {
      final updatedModel = AppVersionEntity()
        ..id = model.id
        ..version = model.version
        ..forceUpdate = false
        ..releaseNotes = model.releaseNotes
        ..url = model.url
        ..createTime = model.createTime;

      AppUpdateDialog(model: updatedModel).show();
    } else {
      GSEasyLoading.showToast("当前已是最新版本");
    }
  }
}

class _AboutItem {
  final String title;
  final String path;

  const _AboutItem({
    required this.title,
    required this.path,
  });
}
