import 'package:bloc/bloc.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:tencent_cloud_chat_sdk/enum/V2TimConversationListener.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';

import 'chat_conversation_state.dart';

class ChatConversationCubit extends Cubit<ChatConversationState> {
  ChatConversationCubit() : super(ChatConversationState().init()) {
    setupConversationListener();
    getList();
  }

  Future<List<V2TimConversation?>> getList() async {

    V2TimValueCallback<String> getLoginUserRes =
    await TencentImSDKPlugin.v2TIMManager.getLoginUser();
    // print("getLoginUserRes.code>>> ${getLoginUserRes.code}");
    if (getLoginUserRes.code != 0) {
      return [];
    }

    List<V2TimConversation?> allConversations = [];

    try {
      String nextSeq = "0";
      bool isFinished = false;

      while (!isFinished) {
        V2TimValueCallback<V2TimConversationResult> result =
            await TencentImSDKPlugin.v2TIMManager.getConversationManager().getConversationList(
                  count: 100,
                  nextSeq: nextSeq,
                );

        if (result.code != 0) {
          throw Exception('获取会话列表失败: ${result.desc}');
        }

        if (result.data?.conversationList != null) {
          allConversations.addAll(result.data!.conversationList!);
        }

        isFinished = result.data?.isFinished ?? true;
        nextSeq = result.data?.nextSeq ?? "0";
      }

      state.dataList = allConversations.whereType<V2TimConversation>().toList();
      emit(state.clone());
      return allConversations;
    } catch (e) {
      LogE('获取会话列表失败: $e');
      rethrow;
    }
  }

  void _updateConversations(List<V2TimConversation> conversationList) {
    // 创建一个Map来存储最新的会话
    final Map<String, V2TimConversation> updates = {};
    for (var conversation in conversationList) {
      updates[conversation.conversationID] = conversation;
    }

    // 只更新已存在的会话
    for (int i = 0; i < state.dataList.length; i++) {
      final currentConversation = state.dataList[i];
      if (updates.containsKey(currentConversation.conversationID)) {
        // 更新已存在的会话
        state.dataList[i] = updates[currentConversation.conversationID]!;
      }
    }

    // 如果需要处理新会话，应该通过 onNewConversation 回调来处理
    emit(state.clone());
  }

  // 处理新会话的回调
  void _onNewConversation(List<V2TimConversation> newConversationList) {
    // 在这里处理新会话
    state.dataList.insertAll(0, newConversationList);
    emit(state.clone());
  }

  // 处理会话删除
  void _onConversationDeleted(List<String> conversationIDList) {
    // 从列表中移除被删除的会话
    state.dataList.removeWhere((conversation) => conversationIDList.contains(conversation.conversationID));

    emit(state.clone());
  }

  /// 设置会话监听器
  /// 用于监听会话列表的变化、新会话、未读消息数等事件
  setupConversationListener() {
    final manager = TencentImSDKPlugin.v2TIMManager.getConversationManager();
    manager.addConversationListener(
        listener: V2TimConversationListener(
            // 当现有会话的关键信息发生变更时触发
            onConversationChanged: _updateConversations,
            // 当有新的会话被创建时触发
            onNewConversation: _onNewConversation,
            // 当会话未读总数发生变化时触发
            onTotalUnreadMessageCountChanged: (totalUnread) {
              // _totalUnReadCount = totalUnread;
              // _chatGlobalModel.totalUnReadCount = totalUnread;
              // notifyListeners();
            },
            // 当会话分组名称发生变化时触发
            // oldName: 原分组名称
            // newName: 新分组名称
            onConversationGroupNameChanged: (oldName, newName) {
              // print("onConversationGroupNameChanged: $oldName, $newName");
            },
            // 同步服务器会话完成时触发
            // 通常用于首次登录或者会话同步
            onSyncServerFinish: () {
              // 移除启动后加载大量会话的处理
              //! Web端修复
              // if (!PlatformUtils().isWeb) {
              //   loadInitConversation();
              // }
            },
            // 当会话被删除时触发
            // conversationIDList: 被删除的会话ID列表
            onConversationDeleted: _onConversationDeleted));
  }

  clearData() {
    state.dataList.clear();
    emit(state.clone());
  }

  @override
  Future<void> close() {
    // TODO: implement close
    return super.close();
  }
}
