
import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/3_chat_plus/widget/avatar/chat_avatar.dart';

class ChatContactCell extends StatelessWidget {
  final String? faceUrl;
  final String? userID;
  final String? name;

  const ChatContactCell({
    super.key,
    required this.faceUrl,
    required this.userID,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 58.gw,
      color: Colors.white,
      child: Column(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 7.5.gw),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ChatAvatar(
                    faceUrl: faceUrl,
                    userId: userID,
                    name: name ?? userID ?? '',
                  ),
                  SizedBox(width: 10.gw),
                  Expanded(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNameWidget(),
                    ],
                  ))
                ],
              ),
            ),
          ),
          Container(
            height: 0.5,
            margin: EdgeInsets.only(left: 67.gw),
            color: const Color(0xffEAEAEA),
          ),

        ],
      ),
    );
  }

  _buildNameWidget() {
    return Text(
      name ?? userID ?? '',
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      softWrap: false,
      style: TextStyle(
        fontSize: 16.fs,
        color: const Color(0xff353D4B),
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
