import 'package:sticky_and_expandable_list/sticky_and_expandable_list.dart';

class ChatContactState {
  late List<ContactSection> sections;


  ChatContactState init() {
    sections = [
      ContactSection(
        title: '我的频道',
        items: [],
        expanded: true,
      ),
      ContactSection(
        title: '我的好友',
        items: [],
        expanded: true,
      ),
    ];
    return this;
  }

  ChatContactState clone() {
    final cloned = ChatContactState()
      ..sections = sections;
    return cloned;
  }
}


class ContactSection implements ExpandableListSection<dynamic> {
  final String title;
  List<dynamic> items;
  bool expanded;

  ContactSection({
    required this.title,
    this.items = const [],
    this.expanded = true,
  });

  @override
  List<dynamic> getItems() => items;

  @override
  bool isSectionExpanded() => expanded;

  @override
  void setSectionExpanded(bool expanded) {
    this.expanded = expanded;
  }
}