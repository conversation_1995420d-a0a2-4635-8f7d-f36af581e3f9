import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

import 'chat_message_list_cubit.dart';

class ChatMessageListPage extends StatelessWidget {
  const ChatMessageListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ChatMessageListCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ChatMessageListCubit>(context);

    return Container();
  }

  bool _shouldShowTime(List<V2TimMessage> messages, int index) {
    if (index == messages.length - 1) return true;
    
    final currentMessage = messages[index];
    final previousMessage = messages[index + 1];
    
    // 如果两条消息间隔超过5分钟，显示时间
    return ((currentMessage.timestamp ?? 0) - (previousMessage.timestamp??0)).abs() > 300;
  }
}


