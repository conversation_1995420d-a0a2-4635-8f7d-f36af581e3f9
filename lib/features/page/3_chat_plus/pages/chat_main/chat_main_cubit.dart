import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/tencent_im_util.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_contact/chat_contact_cubit.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_conversation/chat_conversation_cubit.dart';
import 'package:wd/injection_container.dart';

import 'chat_main_state.dart';

class ChatMainCubit extends Cubit<ChatMainState> {
  ChatMainCubit() : super(const ChatMainState());


  void initChats(int sdkAppID, userId, userSig, String token) async {
    if (state.tencentConnectStatus == ChatConnectStatus.success) return;
    emit(
      state.copyWith(
        tencentConnectStatus: ChatConnectStatus.loading,
      ),
    );
    try {
      bool isConnected = false;
      int retryCount = 0;

      while (!isConnected) {
        try {
          await TencentImUtil().initSdkCore(
            sdkAppID,
            userId,
            userSig,
            token: token,
            onLoginSuccess: () {
              isConnected = true;
              emit(
                state.copyWith(
                  isTencentInitialized: true,
                  tencentConnectStatus: ChatConnectStatus.success,
                ),
              );
            },
            // onConnecting: () {
            //   emit(state.copyWith(tencentConnectStatus: ChatConnectStatus.loading));
            // },
            onConnectFailed: (code, error) {
              LogE('连接失败，正在重试第 ${retryCount + 1} 次: $error');
              emit(state.copyWith(tencentConnectStatus: ChatConnectStatus.failed));
              isConnected = false;
            },
          );

          if (!isConnected) {
            retryCount++;
            // 可选：添加延迟，避免立即重试
            await Future.delayed(const Duration(seconds: 2));
          }
        } catch (e) {
          LogE('连接异常: $e');
          await Future.delayed(const Duration(seconds: 2));
        }
      }
    } catch (e) {
      LogE('initChats: $e');
    }
  }

  Future<void> logout() async {
    try {
      await TencentImUtil().logout(
        onLogoutSuccess: () {
          sl<ChatConversationCubit>().clearData();
          sl<ChatContactCubit>().clearData();
          emit(
            state.copyWith(
              isTencentInitialized: false,
              tencentConnectStatus: ChatConnectStatus.idle,
            ),
          );
        },
      );
    } catch (e) {
      LogE('logout: $e');
    }
  }

  // void _onLoginChanged(bool isLogin) async {
  //   if (isLogin) {
  //     final sdkConfig = await UserApi.fetchTencentUserSig();
  //     if (sdkConfig != null) {
  //       final tencentConfig = sdkConfig;
  //       initChats(int.parse(tencentConfig.sdkAppId), tencentConfig.userId, tencentConfig.userSig,
  //           UserSingleton().loginInfo?.token ?? "");
  //     }
  //     return;
  //   }
  //   if (!isLogin) {
  //     logout();
  //   }
  // }

}
