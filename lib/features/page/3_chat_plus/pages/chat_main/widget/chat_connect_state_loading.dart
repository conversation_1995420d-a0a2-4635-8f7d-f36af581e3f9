import 'package:flutter/cupertino.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';


class ChatConnectStateLoading extends StatelessWidget {
  final bool isLoading;

  const ChatConnectStateLoading({
    super.key,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    // 如果是成功状态，直接不显示
    if (!isLoading) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(right: 8.gw),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 8,
            height: 8,
            child: CupertinoActivityIndicator(
            ),
          ),
          SizedBox(height: 5.gw),
          Text(
            '连接中...',
            style: TextStyle(
              fontSize: 10.fs,
              color: const Color(0XFF353D4B),
            ),
          ),
        ],
      ),
    );
  }
}

