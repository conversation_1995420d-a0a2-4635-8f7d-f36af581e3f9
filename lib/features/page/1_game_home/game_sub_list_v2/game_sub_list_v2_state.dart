import 'package:equatable/equatable.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';

class GameSubListV2State extends Equatable {
  final bool isExpanded;
  final String keyword;
  final GamePlatformV2 selectedPlatform;
  final List<GameV2> dataList;

  const GameSubListV2State({
    this.isExpanded = false,
    this.keyword = '',
    required this.selectedPlatform,
    this.dataList = const [],
  });

  GameSubListV2State copyWith({
    bool? isExpanded,
    String? keyword,
    GamePlatformV2? selectedPlatform,
    List<GameV2>? dataList,
  }) {
    return GameSubListV2State(
      isExpanded: isExpanded ?? this.isExpanded,
      keyword: keyword ?? this.keyword,
      selectedPlatform: selectedPlatform ?? this.selectedPlatform,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [
    isExpanded,
    keyword,
    selectedPlatform,
    dataList,
  ];
}
