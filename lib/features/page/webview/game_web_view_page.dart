import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/features/routers/route_tracker.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/webview/common_webview.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GameWebViewPage extends StatefulWidget {
  final String initialUrl;
  final bool hideFloatButton;

  const GameWebViewPage({super.key, required this.initialUrl, this.hideFloatButton = false});

  @override
  State createState() => _GameWebViewPageState();
}

class _GameWebViewPageState extends State<GameWebViewPage>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver, HideFloatButtonRouteAwareMixin {
  Offset _buttonPosition = const Offset(0, 100); // 初始位置
  final double buttonSize = kChannel == "JS2" ? 60.0 : 48.0;
  final webViewKey = UniqueKey();
  bool _keepAlive = true;
  OverlayEntry? entryHolder;
  String? _previousRouteName;

  @override
  void initState() {
    super.initState();
    _previousRouteName =
        RouteTracker().routeStack.length >= 2 ? RouteTracker().routeStack[RouteTracker().routeStack.length - 2] : null;
    WidgetsBinding.instance.addObserver(this); // 添加观察者来监听屏幕旋转
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!widget.hideFloatButton) {
        _showFloatWidget();
      }
      // 恢复按钮位置
      _loadButtonPosition();
    });
  }

  _hideFloatWidget() {
    entryHolder?.remove();
    entryHolder = null;
  }

  _showFloatWidget() {
    if (entryHolder != null) return;
    OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          left: _buttonPosition.dx,
          top: _buttonPosition.dy,
          width: buttonSize,
          height: buttonSize,
          child: _buildFloatingButton(context),
        );
      },
    );
    sl<NavigatorService>().navigatorKey.currentState?.overlay?.insert(overlayEntry);
    entryHolder = overlayEntry;
  }

  // 保存按钮位置
  Future<void> _saveButtonPosition() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('buttonPositionX', _buttonPosition.dx);
    await prefs.setDouble('buttonPositionY', _buttonPosition.dy);
  }

  // 读取按钮位置
  Future<void> _loadButtonPosition() async {
    final prefs = await SharedPreferences.getInstance();
    final dx = prefs.getDouble('buttonPositionX') ?? 0.0;
    final dy = prefs.getDouble('buttonPositionY') ?? 100.0;
    setState(() {
      _buttonPosition = Offset(dx, dy);
    });
  }

  @override
  void dispose() {
    _keepAlive = false;
    _hideFloatWidget();
    WidgetsBinding.instance.removeObserver(this); // 移除观察者
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // 在屏幕旋转时检查按钮位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkAndResetButtonPosition();
      }
    });
  }

  void _checkAndResetButtonPosition() {
    final screenSize = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;

    setState(() {
      double dx = _buttonPosition.dx;
      double dy = _buttonPosition.dy;

      // 限制按钮的水平位置在 0 到 (screenSize.width - buttonSize) 之间
      if (dx < 0) {
        dx = 0;
      } else if (dx + buttonSize > screenSize.width) {
        dx = screenSize.width - buttonSize;
      }

      // 限制按钮的垂直位置在 0 到 (screenSize.height - buttonSize) 之间
      if (dy < 0) {
        dy = 0;
      } else if (dy + buttonSize > screenSize.height - padding.bottom) {
        dy = screenSize.height - padding.bottom - buttonSize;
      }

      _buttonPosition = Offset(dx, dy);
      _saveButtonPosition(); // 更新按钮位置后保存
    });
  }

  Widget _buildFloatingButton(BuildContext context) {
    onTapCallback() {
      CommonDialog.show(
        context: context,
        title: "提示",
        content: "确定要退出?",
        complete: () async {
          if (_previousRouteName != null && _previousRouteName!.isNotEmpty) {
            sl<NavigatorService>().popUntil(_previousRouteName!);
          } else {
            sl<NavigatorService>().pop();
          }
        },
      );
    }

    // 判断是否为 Web 平台
    if (SystemUtil.isWeb()) {
      return PointerInterceptor(
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: onTapCallback,
          child: _buildBackWidget(),
        ),
      );
    }

    // 在 build 方法内获取屏幕信息
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final Size screenSize = mediaQuery.size;
    final EdgeInsets padding = mediaQuery.padding;
    final double buttonWidth = buttonSize; // 根据你的按钮实际大小调整
    final double buttonHeight = buttonSize; // 根据你的按钮实际大小调整

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onPanUpdate: (details) {
          setState(() {
            // 计算新位置并限制在安全区域内
            double newX = _buttonPosition.dx + details.delta.dx;
            double newY = _buttonPosition.dy + details.delta.dy;

            newX = newX.clamp(padding.left, screenSize.width - padding.right - buttonWidth);
            newY = newY.clamp(padding.top, screenSize.height - padding.bottom - buttonHeight);

            _buttonPosition = Offset(newX, newY);
          });
          entryHolder?.markNeedsBuild();
        },
        onTap: onTapCallback,
        child: _buildBackWidget(),
      ),
    );
  }

  Widget _buildBackWidget() {
    double iconSize = 18;
    if (kChannel == "JS2") {
      return SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: Image.asset(
          "assets/images/home/<USER>",
        ),
      );
    }
    return Container(
      width: buttonSize,
      height: buttonSize,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(
          width: 0.5,
          color: Theme.of(context).dividerColor,
        ),
      ),
      alignment: Alignment.center,
      child: Container(
        width: iconSize,
        height: iconSize,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
      canPop: false,
      child: OrientationBuilder(builder: (context, orientation) {
        if (orientation == Orientation.landscape) {
          // 横屏时隐藏状态栏和导航栏
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        } else {
          // 竖屏时恢复UI
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: SystemUiOverlay.values);
        }
        return Container(
          color: Colors.black,
          child: SafeArea(
            child: CommonWebView(key: webViewKey, initialUrl: widget.initialUrl),
          ),
        );
      }),
    );
  }

  @override
  bool get wantKeepAlive => _keepAlive;
}
