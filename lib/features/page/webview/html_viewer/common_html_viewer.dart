import 'package:draggable_float_widget/draggable_float_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'html_viewer_web.dart' if (dart.library.io) 'html_viewer_native.dart';

class CommonHtmlViewer extends StatefulWidget {
  final String? title;
  final String? assetPath;
  final String? htmlContent;

  const CommonHtmlViewer({
    super.key,
    this.title,
    this.assetPath,
    this.htmlContent,
  }) : assert(assetPath != null || htmlContent != null, 'Either assetPath or htmlContent must be provided');

  static Future<String> loadHtmlFromAsset(String assetPath) async {
    try {
      return await rootBundle.loadString(assetPath);
    } catch (e) {
      throw Exception('Failed to load HTML asset: $e');
    }
  }

  @override
  State<CommonHtmlViewer> createState() => _CommonHtmlViewerState();
}

class _CommonHtmlViewerState extends State<CommonHtmlViewer> with HideFloatButtonRouteAwareMixin {
  bool _isLoading = true;
  String? _error;
  bool _isInitialized = false;
  String? _htmlContent;

  @override
  void initState() {
    super.initState();
    _loadContent();
  }

  Future<void> _loadContent() async {
    try {
      _htmlContent =
          widget.assetPath != null ? await CommonHtmlViewer.loadHtmlFromAsset(widget.assetPath!) : widget.htmlContent!;

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _isLoading = false;
          _error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'error';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Stack(
        children: [
          if (_error != null)
            _buildErrorWidget()
          else if (_isInitialized && _htmlContent != null)
            HtmlContentView(
              htmlContent: _htmlContent!,
            ),
          if (_isLoading)
            Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: const Center(
                child: CircularProgressIndicator.adaptive(),
              ),
            ),
          if (SystemUtil.isWeb() && widget.title == null) ...[
            PointerInterceptor(
              child: _buildFloatingButton(context),
            ),
          ],
          if (!SystemUtil.isWeb() && widget.title == null)
            DraggableFloatWidget(
              // eventStreamController: eventStreamController,
              config: DraggableFloatWidgetBaseConfig(
                isFullScreen: true,
                initPositionYInTop: true,
                initPositionYMarginBorder: 0,
                borderTop: MediaQuery.of(context).padding.top,
                borderBottom: MediaQuery.of(context).padding.bottom,
              ),
              onTap: () {},
              child: _buildFloatingButton(context),
            ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    if (widget.title == null) {
      return const PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: SizedBox(),
      );
    }
    return AppBar(
      elevation: 0,
      title: Text(
        widget.title ?? '',
        style: Theme.of(context).textTheme.headlineMedium,
      ),
      centerTitle: true,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(top: 10.gw, bottom: 10.gw),
            width: 160.gw,
            height: 160.gw,
            child: Image.asset('assets/images/common/no_data_Normal.png'),
          ),
          Text(
            '加载失败',
            style: TextStyle(fontSize: 14.fs, color: const Color(0xFF999999)),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingButton(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        CommonDialog.show(
          context: context,
          title: "提示",
          content: "确定要退出?",
          complete: () async {
            sl<NavigatorService>().pop();
          },
        );
      },
      child: _buildBackWidget(),
    );
  }

  Widget _buildBackWidget() {
    double buttonSize = kChannel == "JS2" ? 60 : 48;
    if (kChannel == "JS2") {
      return SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: Image.asset(
          "assets/images/home/<USER>",
        ),
      );
    }
    double iconSize = 18;
    return Container(
      width: buttonSize,
      height: buttonSize,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(
          width: 0.5,
          color: Theme.of(context).dividerColor,
        ),
      ),
      alignment: Alignment.center,
      child: Container(
        width: iconSize,
        height: iconSize,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

/// 确保不受build的影响，避免重复构建
class HtmlContentView extends StatefulWidget {
  final String htmlContent;

  const HtmlContentView({super.key, required this.htmlContent});

  @override
  _HtmlContentViewState createState() => _HtmlContentViewState();
}

class _HtmlContentViewState extends State<HtmlContentView> {
  late final Widget _htmlView;

  @override
  void initState() {
    super.initState();
    _htmlView = buildWebHtmlView(widget.htmlContent); // 缓存 HTML 内容
  }

  @override
  Widget build(BuildContext context) {
    return _htmlView;
  }
}
