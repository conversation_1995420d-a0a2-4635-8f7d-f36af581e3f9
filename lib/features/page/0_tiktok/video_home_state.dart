import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/winner_entity.dart';


enum HomeTikTokPlayerStatus {
  play,
  pause,
}

class VideoHomeState extends BaseState with EquatableMixin {
  final bool isShowUnlockVideoBanner;
  TabController? mainTabController;
  String jumpTabTitle;
  final HomeTikTokPlayerStatus playerStatus;
  final List<WinnerEntity> winnerList;

  VideoHomeState({
    this.isShowUnlockVideoBanner = false,
    this.mainTabController,
    this.jumpTabTitle = '',
    this.playerStatus = HomeTikTokPlayerStatus.play,
    this.winnerList = const [],
  });

  VideoHomeState copyWith({
    TabController? mainTabController,
    String? jumpTabTitle,
    HomeTikTokPlayerStatus? playerStatus,
    List<WinnerEntity>? winnerList,
  }) {
    return VideoHomeState(
      isShowUnlockVideoBanner: isShowUnlockVideoBanner ?? isShowUnlockVideoBanner,
      mainTabController: mainTabController ?? this.mainTabController,
      jumpTabTitle: jumpTabTitle ?? this.jumpTabTitle,
      playerStatus: playerStatus ?? this.playerStatus,
      winnerList: winnerList ?? this.winnerList,
    );
  }

  @override
  List<Object?> get props => [
        isShowUnlockVideoBanner,
        mainTabController,
        jumpTabTitle,
        playerStatus,
        winnerList,
      ];
}
