import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';

import 'video_detail_state.dart';

class VideoDetailCubit extends Cubit<VideoDetailState> {
  VideoDetailCubit() : super(VideoDetailState.initial()) {
    judgeTimeLimit();
  }

  void onLoginChanged(bool isLogin) async {
    if (isLogin) {
      judgeTimeLimit();
    } else {
      state.minutesLimit = sl<UserCubit>().filmMinutesLimit;
      state.clone();
    }
  }

  Future<void> fetchVideoUrl(id) async {
    final model = await VideoApi.fetchVideoDetail(id: id);
    if (model != null) {
      state.currentVideoDetailModel = model;
      emit(state.clone());
    } else {
      await Future.delayed(const Duration(seconds: 3));
      if (!isClosed) {
        fetchVideoUrl(id);
      }
    }
  }

  Future<bool> onClickVideoLike() async {
    final model = state.currentVideoDetailModel;
    if (model == null) return false;

    bool flag = await VideoApi.operaVideoLike(isLike: !model.isLiked, videoId: model.videoId);
    if (flag) {
      model.isLiked = !model.isLiked;
      if (model.isLiked) {
        model.likes += 1;
      } else {
        model.likes -= 1;
      }
    }
    return flag;
  }

  judgeTimeLimit() async {
    final res = await VideoApi.fetchRemainingVipDays();
    if (res != null && res.days > 0) {
      state.minutesLimit = 9999;
      emit(state.clone());
    } else {
      state.minutesLimit = sl<UserCubit>().filmMinutesLimit;
      emit(state.clone());
    }
  }

}
