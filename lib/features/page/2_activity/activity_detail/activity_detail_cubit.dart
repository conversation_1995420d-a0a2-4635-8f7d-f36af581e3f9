
import 'package:bloc/bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/models/apis/user.dart';
import 'activity_detail_state.dart';

class ActivityDetailCubit extends Cubit<ActivityDetailState> {
  ActivityDetailCubit() : super(const ActivityDetailState().init()) {
    sl<UserCubit>().fetchUserInfo();
  }

  void setPhoneNo(String phoneNo) => emit(state.copyWith(phoneNo: phoneNo));

  fetchCollectStatus(int id) async {
    GSEasyLoading.showLoading();
    final res = await ActivityApi.fetchActivityCollectionStatus(id: id);
    GSEasyLoading.dismiss();
    emit(state.copyWith(collectStatus: ActivityRewardStatusExtension.fromValue(res)));
  }

  executeCollect(context, int id, int recordType) async {
    GSEasyLoading.showLoading();
    emit(state.copyWith(executeStatus: SimplyNetStatus.loading));

    // Map recordType to operationType
    int operationType;
    switch (recordType) {
      case 0: // Notice
        operationType = 1; // Acknowledge
        break;
      case 1: // Bonus
        operationType = 2; // Collect
        break;
      case 2: // Application
        operationType = 3; // Apply
        break;
      default:
        GSEasyLoading.dismiss();
        GSEasyLoading.showToast("Invalid activity type");
        return;
    }

    final res = await ActivityApi.applyActivity(id: id, operationType: operationType);
    emit(state.copyWith(executeStatus: SimplyNetStatus.success));
    // Check status after apply for both bonus and application
    if (recordType == 1 || recordType == 2) {
      await fetchCollectStatus(id);
    }

    // Update balance only for bonus
    if (recordType == 1) {
      sl<UserCubit>().fetchUserBalance();
    }

    GSEasyLoading.dismiss();
    if (res.isSuccess) {
      String message;
      switch (operationType) {
        case 1:
          message = "已确认"; // Confirmed (for Notice type activities)
          break;
        case 2:
          message = "领取成功"; // Successfully collected (for Bonus type activities)
          break;
        case 3:
          message = "申请成功"; // Successfully applied (for Application type activities)
          break;
        default:
          message = "操作成功"; // Generic success message for unexpected operation types
      }
      GSEasyLoading.showToast(message);
    } else {
      if (res.msg.isNotEmpty) {
        CommonDialog.show(
            context: context,
            title: "提示",
            content: res.msg,
            sureBtnTitle: "知道了",
            showCancelBtn: false,
            complete: () => {});
      }
    }
  }

  void verifyCode({required String phoneNo, required String code}) async {
    if (phoneNo.isEmpty) {
      GSEasyLoading.showToast('请输入手机号');
      return;
    }
    // Validate phone number length and format
    final bool isValidLength = phoneNo.length == 11;
    final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(phoneNo);

    if (!isValidLength || !isValidFormat) {
      GSEasyLoading.showToast('请输入正确的手机号');
      return;
    }

    if (code.isEmpty) {
      GSEasyLoading.showToast('请输入验证码');
      return;
    }

    GSEasyLoading.showLoading();
    emit(state.copyWith(bindStatus: SimplyNetStatus.loading));
    final result = await UserApi.bindPhoneNo(phoneNo, code);
    if (result) {
      emit(state.copyWith(isVerified: true, bindStatus: SimplyNetStatus.success));
    } else {
      emit(state.copyWith(isVerified: false, bindStatus: SimplyNetStatus.failed));
    }
    GSEasyLoading.dismiss();
  }

}
