import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/dialog/check_in_success_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

part 'activity_list_state.dart';

class ActivityListCubit extends Cubit<ActivityListState> {
  late StreamSubscription<bool> _isLoginSubscription;

  ActivityListCubit() : super(ActivityListState().init());

  refreshData() {
    fetchGameActivityCategoryList();
    fetchTaskCategoryList();
    fetchCheckInData();
  }

  resetData() {
    state
      ..currentDayId = 1
      ..totalSignedDays = 1
      ..totalGoldEarned = 1
      ..consecutiveSignedDays = 1
      ..isNoMoreDataState = false
      ..isCheckedToday = false
      ..taskCategoryList = []
      ..checkInModel = null
      ..currentTabIndex = 0
      ..currentGameTabIndex = 0
      ..currentTaskTabIndex = 0
      ..dataList = [];
    emit(state.clone());
    refreshData();
  }

  @override
  Future<void> close() {
    _isLoginSubscription.cancel();
    return super.close();
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    state.isNoMoreDataState = isNoMoreDataState;
    emit(state.clone());
  }

  onChangeTabIndex(int index) {
    state.currentTabIndex = index;
    emit(state.clone());
    refreshCurrentTabData();
  }

  onChangeGameTabIndex(int index) {
    state.currentGameTabIndex = index;
    emit(state.clone());
  }

  onChangeTaskTabIndex(int index) {
    state.currentTaskTabIndex = index;
    emit(state.clone());
  }

  /// 页面切换时，刷新当前页数据
  refreshCurrentTabData() {
    if (state.currentTabIndex == 0) {
      // 游戏活动
      if (state.gameCategoryList.isEmpty) {
        fetchGameActivityCategoryList();
      } else {
        fetchGameListData(state.gameCategoryList[state.currentTaskTabIndex]);
      }
    } else if (state.currentTabIndex == 1) {
      // 自助领取
      if (state.taskCategoryList.isEmpty) {
        fetchTaskCategoryList();
      } else {
        fetchTaskListData(state.taskCategoryList[state.currentTaskTabIndex]);
      }
    }
  }

  fetchCheckInData() async {
    try {
      final res = await UserApi.fetchCheckInList();

      state.checkInModel = res;
      calculateCheckInDaysTotal();
      emit(state.clone());
    } catch (e) {
      // 可以在这里添加错误处理逻辑
      LogD('Error fetching daily check in data: $e');
    } finally {
      state.checkInListNetState = state.checkInModel == null ? NetState.errorShowRefresh : NetState.dataSuccessState;
      emit(state.clone());
    }
  }

  calculateCheckInDaysTotal() {
    if (state.checkInModel == null) return;
    int totalSignedDays = 0;
    double totalGoldEarned = 0;
    for (var entity in state.checkInModel!.fullList) {
      if (entity.signInState == SignInState.signed.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.signInAward;
      } else if (entity.signInState == SignInState.backdated.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.reSignInAward;
      }
    }
    state.totalSignedDays = totalSignedDays;
    state.totalGoldEarned = totalGoldEarned;
    emit(state.clone());
  }

  confirmClearCheckInInfo(DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.clone());
    try {
      await UserApi.clearUserCheckInInfo();
      await fetchCheckInData();
    } catch (e) {
      model.isFetching = false;
      emit(state.clone());
    }
  }

  onClickCheckBtn(context, DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.clone());
    try {
      final res = await UserApi.executeCheckIn(
          isBackdate: model.signInState == SignInState.needBackdate.value, date: model.day);
      sl<UserCubit>().fetchUserBalance();
      if (res.$1) {
        // 签到成功
        await fetchCheckInData();
        final award = model.signInState == SignInState.needBackdate.value ? model.reSignInAward : model.signInAward;
        CheckInSuccessDialog(money: award, days: state.totalSignedDays).show(context);
      } else {
        model.isFetching = false;
        emit(state.clone());
        CommonDialog.show(context: context, title: '提示', content: res.$2, showCancelBtn: false);
      }
    } catch (e) {
      model.isFetching = false;
      emit(state.clone());
    }
  }

  /// *********************************************** 游戏活动相关

  fetchGameActivityCategoryList() async {
    final result = await ActivityApi.fetchActivityCategoryList(
      1,
      mapper: ActivityGameTypeViewModel.fromActivityCategory,
    );

    state.gameCategoryList = result;
    state.gameNetState = result.isEmpty ? NetState.errorShowRefresh : NetState.dataSuccessState;
    emit(state.clone());

    if (result.isNotEmpty) {
      // 先加载第一个分类的数据以保证主界面响应
      await fetchGameListData(result.first);
      state.gameNetState = NetState.dataSuccessState;
      emit(state.clone());

      // 后台预加载其他分类数据
      unawaited(_preloadOtherCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherCategories(List<ActivityGameTypeViewModel> categories) async {
    try {
      await Future.wait(
        categories.map((category) => fetchGameListData(category)),
        eagerError: false, // 某个请求失败不影响其他请求
      );
    } catch (e) {
      print('Preload error: $e'); // 可以添加错误日志
    }
  }

  Future fetchGameListData(ActivityGameTypeViewModel viewModel) async {
    viewModel.netState = NetState.loadingState;
    if (viewModel.list.isEmpty) {
      viewModel.refreshStatus = SimplyNetStatus.loading;
    }
    emit(state.clone());

    final result = await ActivityApi.fetchActivityList(
      pageNo: viewModel.pageNo,
      activeCategory: viewModel.category,
    );
    if (viewModel.pageNo == 1) {
      viewModel.list = result.records;
    } else {
      viewModel.list = List.from(viewModel.list)..addAll(result.records);
    }

    viewModel.isNoMoreDataState = result.total <= viewModel.list.length;

    viewModel.refreshStatus = SimplyNetStatus.success;
    viewModel.netState = NetState.dataSuccessState;
    if (viewModel.list.isEmpty) viewModel.netState = NetState.emptyDataState;
    emit(state.clone());
    return result.records;
  }

  /// *********************************************** 自助领取相关

  fetchTaskCategoryList() async {
    final result = await ActivityApi.fetchActivityCategoryList(
      2,
      mapper: ActivityTaskTypeViewModel.fromActivityCategory,
    );

    state.taskCategoryList = result;
    state.taskNetState = result.isEmpty ? NetState.errorShowRefresh : NetState.dataSuccessState;
    emit(state.clone());

    if (result.isNotEmpty && sl<UserCubit>().state.isLogin) {
      // 先加载第一个分类的数据以保证主界面响应
      await fetchTaskListData(result.first);
      emit(state.clone());

      // 后台预加载其他分类数据
      unawaited(_preloadOtherTaskCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherTaskCategories(List<ActivityTaskTypeViewModel> categories) async {
    try {
      await Future.wait(
        categories.map((category) => fetchTaskListData(category)),
        eagerError: false, // 某个请求失败不影响其他请求
      );
    } catch (e) {
      print('task Preload error: $e'); // 可以添加错误日志
    }
  }

  Future fetchTaskListData(ActivityTaskTypeViewModel viewModel) async {
    viewModel.netState = NetState.loadingState;
    if (viewModel.list.isEmpty) {
      viewModel.netState = NetState.loadingState;
    }
    emit(state.clone());

    final result = await ActivityApi.fetchTaskList(activeCategory: viewModel.category);

    // 优化：直接使用 spread operator 创建新列表
    final inProgressTask = result.firstWhereOrNull((task) => task.receiveStatus == 1);
    List<ActivityTask> processedTasks = [
      // 如果存在进行中的任务且不是新手任务，则添加到开头
      if (viewModel.category != 1) // 新手任务类别
        if (inProgressTask != null) ...[inProgressTask.clone()..isProcess = true],
      ...result,
    ];

    viewModel.list = processedTasks;

    if (result.isNotEmpty) {
      viewModel.netState = NetState.dataSuccessState;
    } else {
      viewModel.netState = NetState.emptyDataState;
    }

    emit(state.clone());
    return result;
  }

  bool isRequestCompleteTask = false;

  onClickCompleteTask({required int id, required int category}) async {
    if (isRequestCompleteTask) return;
    isRequestCompleteTask = true;
    GSEasyLoading.showLoading();
    try {
      final flag = await ActivityApi.completeActivityTask(id: id);
      if (flag) {
        final viewModel = state.taskCategoryList.firstWhereOrNull((e) => e.category == category);
        if (viewModel != null) fetchTaskListData(viewModel);
      }
    } finally {
      isRequestCompleteTask = false;
      GSEasyLoading.dismiss();
    }
  }
}
