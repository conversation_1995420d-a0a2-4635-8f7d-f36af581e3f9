import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:wd/core/constants/assets.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/log.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/dp.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/support_dp.dart';
import 'package:wd/features/page/3_chat_new/widgets/shared/shared_error.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/red_packet_card.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/transact_order_msg.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../../../../core/models/entities/chat_red_envelope.dart';
import '../../../../shared/widgets/easy_loading.dart';

class ChatScreen extends StatefulWidget {
  final V2TimConversation? selectedConversation;
  final String? nickName;
  final V2TimFriendInfo? freindInfo;

  const ChatScreen({
    super.key,
    this.selectedConversation,
    this.nickName,
    this.freindInfo,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  @override
  Widget build(BuildContext context) {
    final isGroup = widget.selectedConversation?.type == 2;
    if (widget.selectedConversation == null) {
      return const SharedError();
    }
    return TIMUIKitChat(
      chatScreenDecoration: BoxDecoration(
        image: DecorationImage(
          image: Theme.of(context).brightness == Brightness.light
              ? const AssetImage(Assets.chatLightBackground1)
              : const AssetImage(Assets.chatDarkBackground),
          fit: BoxFit.cover,
        ),
      ),
      onVideoSendCallback: (String error) => GSEasyLoading.showToast(error),
      messageItemBuilder: MessageItemBuilder(
        redEnvelopeBuilder: (message, isShowJump, clearJump) {
          try {
            final text = message.textElem?.text;
            if (text == null) return null;
            final parsedJson = json.decode(text);
            LogD("parsedJson>>>> $parsedJson");
            final name = message.nickName;
            final faceUrl = message.faceUrl;
            final redEnvelopeId = parsedJson["redEnvelopeId"];
            final redEnvelopeName = parsedJson["redEnvelopeName"] ?? "";
            final content = parsedJson["content"] ?? "";
            final userGrabList =
                (parsedJson['userGrabList'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [];

            final status = RedEnvelopeStatus.fromCode(int.tryParse(parsedJson["status"] ?? "-999") ?? -999);
            return RedPacketCard(
              id: redEnvelopeId,
              title: redEnvelopeName,
              content: _getContent(status, content),
              subTitle: _getSubTitle(userGrabList, message.userID, content),
              status: status,
              message: message,
              name: name,
              faceUrl: faceUrl,
            );
          } catch (e) {
            logDev(e, 'parsedJson e >>>');
          }
          return Container();
        },
        customMessageItemBuilder: (message, isShowJump, clearJump) {
          try {
            final data = message.customElem?.data;
            if (data == null) return null;
            final parsedJson = json.decode(data);
            // msgType 1=订单消息
            if (parsedJson['msgType'] == 1) {
              return TransactOrderMsg(data: parsedJson);
            }
          } catch (e) {
            logDev(e, 'parsedJson e >>>');
          }
          return null;
        },
      ),
      textFieldBuilder: isGroup
          ? (context) => const SizedBox(
                height: 20,
              )
          : null,
      morePanelConfig: MorePanelConfig(
        extraAction: [
          //! Remove: Used for test purposes
          // MorePanelItem(
          //   icon: Container(child: const CircleAvatar()),
          //   id: 'custom',
          //   title: "cm1",
          //   onTap: (v) {
          //     try {
          //       final isGroup = widget.selectedConversation!.type == 2;
          //       final convID = isGroup ? widget.selectedConversation!.groupID : widget.selectedConversation!.userID;
          //       final convType = isGroup ? ConvType.group : ConvType.c2c;
          //       final model = TUIChatSeparateViewModel();

          //       const String jsonData = '''
          //           {
          //             "Text": {
          //               "content": "红包",
          //               "redEnvelopeId": 47,
          //               "status": "active",
          //               "redEnvelopeName": "测试红包"
          //             }
          //           }
          //           ''';

          //       if (convID == null) {
          //         throw Exception('convID error');
          //       }

          //       MessageUtils.handleMessageError(
          //         model.sendCustomMessage(convID: convID, convType: convType, data: jsonData),
          //         context,
          //       );
          //     } catch (error) {
          //       logDev(error, "sendCustomMessage", error: true);
          //     }
          //   },
          // ),
        ],
      ),
      conversation: widget.selectedConversation!,
      customStickerPanel: renderCustomStickerPanel,
      config: TIMUIKitChatConfig(
        stickerPanelConfig: StickerPanelConfig(
            useQQStickerPackage: true, // 是否启用QQ图片小表情包，默认启用
            useTencentCloudChatStickerPackage: true, // 是否启用我们自己设计的图片小表情包，默认启用
            unicodeEmojiList: [], // Unicode Emoji表情包列表，默认提供一组，传入空数组则不启用此表情包
            customStickerPackages: [
              // _getCustomStickerPackage('4350'),
              // _getCustomStickerPackage('4351'),
              // _getCustomStickerPackage('4352'),
            ] // 拓展更多自定义表情包
            ),
        isAllowClickAvatar: false,
        isUseDefaultEmoji: true,
        isAllowLongPressMessage: true,
        isShowReadingStatus: true,
        isShowGroupReadingStatus: true,
        notificationTitle: "",
        isUseMessageReaction: false,
        isShowAvatar: true,
        // groupReadReceiptPermissionList: const [
        //   GroupReceiptAllowType.work,
        //   GroupReceiptAllowType.meeting,
        //   GroupReceiptAllowType.public,
        // ],
      ),
      userAvatarBuilder: (context, message, canShow) {
        if (!canShow || message.groupID == null) return const SizedBox();
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: CircleAvatar(
            radius: 16,
            child: Dp(
              name: message.nickName.notNullNorEmpty ? message.nickName : message.sender,
              faceUrl: message.faceUrl,
              size: 32,
              fontSize: 12,
            ),
          ),
        );
      },
      appBarConfig: AppBar(
        backgroundColor: context.theme.appBarTheme.backgroundColor,
        foregroundColor:  context.theme.appBarTheme.foregroundColor,
        surfaceTintColor: context.theme.primaryColor,
        titleTextStyle: TextStyle(
          color: context.colorTheme.textRegular,
        ),
        iconTheme: IconThemeData(
          color: context.colorTheme.textRegular,
        ),
        actionsIconTheme: IconThemeData(
          color: context.colorTheme.textRegular,
        ),
        centerTitle: false,
        leadingWidth: 50,
        leading: const BackButton(),
        elevation: 0,
        title: GestureDetector(
          onTap: () async {
            final conversationType = widget.selectedConversation?.type ?? 1;

            if (conversationType == 1) {
              return;
            } else if (conversationType == 2) {
              final String? groupID = widget.selectedConversation?.groupID;
              if (groupID != null) {
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //     builder: (context) => GroupProfileScreen(groupID: groupID),
                //   ),
                // );
              }
            }
          },
          child: Row(
            children: [
              if (widget.freindInfo != null &&
                  (widget.freindInfo?.userProfile?.role == 1 || widget.freindInfo?.userProfile?.role == 2))
                SupportDp(
                  faceUrl: widget.selectedConversation?.faceUrl,
                  userId: widget.selectedConversation?.userID ?? widget.selectedConversation?.groupID,
                  name: widget.selectedConversation?.showName ??
                      widget.freindInfo?.userProfile?.userID ??
                      widget.selectedConversation?.userID,
                  size: 38,
                )
              else
                Dp(
                  faceUrl: widget.selectedConversation?.faceUrl,
                  type: widget.selectedConversation?.type,
                  size: 38,
                  userId: widget.selectedConversation?.userID ?? widget.selectedConversation?.userID,
                  // name: PlatformUtils().isWeb
                  //     ? widget.selectedConversation?.showName
                  //     : widget.freindInfo?.userProfile?.nickName ?? widget.selectedConversation?.showName,
                  name: widget.selectedConversation?.showName ??
                      widget.freindInfo?.userProfile?.userID ??
                      widget.selectedConversation?.userID,
                ),
              const SizedBox(width: 16),
              Text(
                // PlatformUtils().isWeb
                //     ? widget.selectedConversation?.showName ?? ''
                widget.freindInfo?.userProfile?.nickName ??
                    widget.selectedConversation?.showName ??
                    widget.freindInfo?.userProfile?.userID ??
                    '',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: context.colorTheme.textRegular,
                ),
              ),
            ],
          ),
        ),
        scrolledUnderElevation: 0,
      ),
    );
  }

  CustomStickerPackage _getCustomStickerPackage(elementName) {
    final emojiSet = TUIKitStickerConstData.emojiList.firstWhere((element) => element.name == elementName);
    return CustomStickerPackage(
        name: emojiSet.name,
        baseUrl: "assets/custom_face_resource/${emojiSet.name}",
        isEmoji: emojiSet.isEmoji,
        isDefaultEmoji: true,
        stickerList:
            emojiSet.list.asMap().keys.map((idx) => CustomSticker(index: idx, name: emojiSet.list[idx])).toList(),
        menuItem: CustomSticker(
          index: 0,
          name: emojiSet.icon,
        ));
  }

  String _getContent(RedEnvelopeStatus status, String title) {
    switch (status) {
      case RedEnvelopeStatus.expired:
        return "手慢了，红包已过期";
      case RedEnvelopeStatus.userGrab:
        return "手慢了，红包派完了";
      default:
        return title;
    }
  }

  String _getSubTitle(List<String> userGrabList, userID, String title) {
    if (userGrabList.contains(userID)) {
      return "已领取";
    }
    return title;
  }
}
