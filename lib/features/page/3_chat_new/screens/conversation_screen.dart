import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/chat/enums.dart';
import 'package:wd/core/utils/chat/scaffold.dart';
import 'package:wd/core/utils/chat/theme.dart';
import 'package:wd/features/page/3_chat/chat_cubit.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/chat_type_tabs.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/contacts_chats.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/conversation_chats.dart';
import 'package:wd/shared/mixin/hide_float_button_route_aware_mixin.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

// 会话屏幕的主要Widget
class ConversationScreen extends StatefulWidget {
  const ConversationScreen({super.key});

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen> with HideFloatButtonRouteAwareMixin {
  // Tencent IM SDK 核心实例
  CoreServicesImpl timCoreInstance = TIMUIKitCore.getInstance();
  // 群组和用户计数
  int groupCount = 0;
  int userCount = 0;
  // 输入框焦点控制
  FocusNode focusNode = FocusNode();
  // 当前选中的标签页索引
  int selectedIndex = 0;
  // 定义标签页类型：全部和联系人
  final tabs = [
    ChatScreenType.ALL,
    ChatScreenType.CONTACTS,
  ];
  // 控制连接状态显示
  bool _showConnected = false;
  Timer? _timer;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TUIChatTheme(
      customTheme: ChatTheme.lightChatTheme,
      child: DefaultTabController(
        length: tabs.length,
        child: CustomScaffold(
          appBar: AppBar(
            // AppBar 配置
            toolbarHeight: 100,
            surfaceTintColor: Colors.transparent,
            backgroundColor: Colors.white,
            automaticallyImplyLeading: false,
            centerTitle: false,
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back_ios_new_rounded),
            ),
            title: Row(
              children: [
                // 标题："聊天"
                Text(
                  'chats'.tr(),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
                const Spacer(),
                // 监听聊天连接状态的 BlocConsumer
                BlocConsumer<ChatCubit, ChatState>(
                  listener: (context, state) {
                    // 当连接成功时显示状态提示，2秒后自动隐藏
                    if (state.tencentConnectStatus == DataStatus.success) {
                      setState(() => _showConnected = true);
                      _timer?.cancel();
                      _timer = Timer(const Duration(seconds: 2), () {
                        if (mounted) {
                          setState(() => _showConnected = false);
                        }
                      });
                    }
                  },
                  builder: (context, state) {
                    final value = state.tencentConnectStatus;
                    // 使用动画效果显示连接状态
                    return AnimatedSlide(
                      duration: const Duration(milliseconds: 300),
                      offset: value == DataStatus.loading || (value == DataStatus.success && _showConnected)
                          ? Offset.zero
                          : const Offset(0, -1),
                      child: AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        opacity:
                            value == DataStatus.loading || (value == DataStatus.success && _showConnected) ? 1.0 : 0.0,
                        child: Row(
                          children: [
                            if (value == DataStatus.loading)
                              const SizedBox(
                                width: 10,
                                height: 20,
                                child: CircularProgressIndicator(),
                              ),
                            if (value == DataStatus.loading) const SizedBox(width: 10),
                            Text(
                              value == DataStatus.loading ? '連接...' : '已连接',
                              style: const TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                const Spacer(flex: 3),
              ],
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(0),
              child: Column(
                children: [
                  ChatTypeTabs(
                    types: tabs,
                    index: selectedIndex,
                    onTap: (value) => setState(() => selectedIndex = value),
                  ),
                ],
              ),
            ),
          ),
          body: const TabBarView(
            children: [ConversationChats(), ContactsChats()],
          ),
        ),
      ),
    );
  }
}
