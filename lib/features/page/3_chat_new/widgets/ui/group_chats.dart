import 'package:flutter/material.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/special_tile.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_contact/widget/empty_contact_widget.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/expansion_tile.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';


class GroupChats extends StatelessWidget {
  const GroupChats({super.key, this.onGroupCountChanged});
  final Function(int)? onGroupCountChanged;
  @override
  Widget build(BuildContext context) {
    return MyExpansionTile(
      label: '我的频道',
      child: TIMUIKitGroup(
        itemBuilder: (context, V2TimGroupInfo groupInfo) {
          // return ChatContactCell(
          //   faceUrl: groupInfo.faceUrl,
          //   userID: groupInfo.groupID,
          //   name: groupInfo.groupName,
          // );
          return SpecialTile(
            groupId: groupInfo.groupID,
            name: groupInfo.groupName,
            url: groupInfo.faceUrl,
          );
        },
        emptyBuilder: (v) => const EmptyChatContact(),
        onGroupCountChanged: onGroupCountChanged,
      ),
    );
  }
}
