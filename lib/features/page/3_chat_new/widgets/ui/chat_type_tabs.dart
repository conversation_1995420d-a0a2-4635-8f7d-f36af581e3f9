import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/enums.dart';

class ChatTypeTabs extends StatelessWidget implements PreferredSizeWidget {
  const ChatTypeTabs({
    super.key,
    required this.index,
    this.onTap,
    required this.types,
  });

  final int index;
  final ValueChanged<int>? onTap;
  final List<ChatScreenType> types;

  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: onTap,
      tabAlignment: TabAlignment.center,
      tabs: [
        if (types.contains(ChatScreenType.ALL))
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'messages'.tr(),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        if (types.contains(ChatScreenType.CONTACTS))
          Tab(
            child: Text(
              'contacts'.tr(),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
      ],
      dividerHeight: 0,
      indicatorSize: TabBarIndicatorSize.label,
      indicatorColor: context.theme.primaryColor,
      labelColor: context.theme.primaryColor,
      unselectedLabelColor: context.colorTheme.textRegular,
      indicatorPadding: const EdgeInsets.only(bottom: 10, left: 0, right: 0),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);
}
