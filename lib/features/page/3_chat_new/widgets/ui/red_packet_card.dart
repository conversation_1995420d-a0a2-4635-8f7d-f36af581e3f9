import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../../../../../core/models/apis/chat.dart';
import '../../../../../core/models/entities/chat_red_envelope.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/easy_loading.dart';
import '../../../../../shared/widgets/red_pocket/red_packet.dart';
import '../../../../routers/app_router.dart';
import '../../../../routers/navigator_utils.dart';

class RedPacketCard extends StatelessWidget {
  static Timer? _clickTimer;
  static bool _isProcessing = false;
  final int id;
  final String title, subTitle;
  final String content;
  final String? name;
  final String? faceUrl;
  final RedEnvelopeStatus status;
  final V2TimMessage message; // Add message for status updates

  const RedPacketCard({
    super.key,
    this.title = "",
    this.subTitle = "",
    required this.content,
    required this.status,
    required this.id,
    required this.message,
    this.name,
    this.faceUrl,
  });

  Future<void> _handleTap(BuildContext context) async {
    if (_isProcessing) return;

    _clickTimer?.cancel();
    _clickTimer = Timer(const Duration(milliseconds: 500), () async {
      _isProcessing = true;
      try {
        GSEasyLoading.showLoading();

        final result = await ChatApi.checkRedEnvelopeStatus(redEnvelopeId: id);

        GSEasyLoading.dismiss();

        if (result.errorStr == null) {
          _updateMessageStatus(status: result.status);
        }
        if (!context.mounted) return;

        ChatRedPacketDialog(
          redPacketId: id,
          status: result.status,
          name: name,
          faceUrl: faceUrl,
        ).show(
          context,
          seq: message.seq,
          onOpen: (double? amount) {
            if (amount != null) {
              // Update message status
              _updateMessageStatus(status: RedEnvelopeStatus.userGrab);

              // Navigate to details
            }

            sl<NavigatorService>().push(AppRouter.chatRedEnvelopeDetail, arguments: {
              "title": title,
              "content": content,
              "amount": amount,
            });
          },
        );
        // }
      } catch (e) {
        GSEasyLoading.showToast(e.toString());
      } finally {
        _isProcessing = false;
      }
    });
  }

  bool isInactive(RedEnvelopeStatus status, String? textElem, String userId) {
    // Check if user has already grabbed the red packet

    if (textElem != null) {
      try {
        final grabUserIdList = json.decode(textElem)['userGrabList'];
        if (grabUserIdList != null && grabUserIdList.contains(userId)) {
          return true;
        }
      } catch (_) {} // Ignore JSON parsing errors
    }

    // Check red packet status
    if (status == RedEnvelopeStatus.open || status == RedEnvelopeStatus.unknown) {
      return false;
    }

    return true;
  }

  Future<void> _updateMessageStatus({required RedEnvelopeStatus status}) async {
    try {
      // Update custom message data
      final textElem = json.decode(message.textElem?.text ?? "{}");

      //update user grab list
      // 如果状态为本人已抢，则加上已抢名单，否则修改红包状态
      if (status == RedEnvelopeStatus.userGrab) {
        _updateUserGrabList(textElem, message.userID ?? "");
      } else {
        textElem['status'] = status.code.toString();
      }
      // custom message data, handling potential null cases
      if (message.textElem != null) {
        message.textElem!.text = json.encode(textElem);
      } else {
        // Create a new custom element if it doesn't exist
        message.textElem = V2TimTextElem(text: json.encode(textElem));
      }

      LogD("_updateMessageStatus.textElem>>> $textElem");
      // Update message in chat
      var result = await TencentImSDKPlugin.v2TIMManager.getMessageManager().modifyMessage(message: message);
      if (result.code != 0) {
        log("Error updating message status: ${result.desc}");
      }
    } catch (e) {
      log("Error updating message status: $e");
    }
  }

  void _updateUserGrabList(Map<String, dynamic> textElem, String userId) {
    // Initialize userGrabList if not exists or not a List
    if (textElem['userGrabList'] == null || textElem['userGrabList'] is! List) {
      textElem['userGrabList'] = [];
    }

    // Get the current list of grabbed user IDs
    List<String> userGrabList = List<String>.from(textElem['userGrabList']);

    // Add userId only if not already present
    if (!userGrabList.contains(userId)) {
      userGrabList.add(userId);
    }

    // Update the userGrabList
    textElem['userGrabList'] = userGrabList;
  }

  @override
  Widget build(BuildContext context) {
    const color1 = Color(0xffF9D098);
    const timeText = "";

    return Opacity(
      opacity: isInactive(status, message.textElem?.text, message.userID ?? "") ? 0.5 : 1,
      child: Bounceable(
        onTap: () => _handleTap(context),
        child: SizedBox(
          width: 270.gw,
          height: 96.gw,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xffF3674C),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(10.gw),
                bottomRight: Radius.circular(10.gw),
                topRight: Radius.circular(message.isSelf ?? false ? 0 : 10.gw),
                topLeft: Radius.circular(message.isSelf ?? false ? 10.gw : 0),
              ),
            ),
            padding: EdgeInsets.fromLTRB(10.gw, 10.gw, 10.gw, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      Assets.iconChatRedPacket,
                      width: 34.gw,
                      height: 40.gw,
                    ).animate(onPlay: (controller) {
                      if (!isInactive(status, message.textElem?.text, message.userID ?? "")) {
                        controller.repeat(period: 4.seconds);
                      } else {
                        controller.stop(); // Stop animation if inactive
                      }
                    }).shake(
                      duration: 8.seconds,
                      delay: 3.seconds,
                      hz: 1.5,
                      rotation: 0.05,
                      curve: Curves.easeInOut,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(fontSize: 16.fs, fontWeight: FontWeight.w500, color: color1),
                        ),
                        Text(
                          subTitle,
                          style: TextStyle(fontSize: 12.fs, color: color1),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 10.gw),
                Container(height: 0.67.gw, color: const Color(0xffFD833E)),
                SizedBox(height: 10.gw),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "微聊红包",
                      style: TextStyle(fontSize: 11.fs, color: color1),
                    ),
                    Text(
                      timeText,
                      style: TextStyle(fontSize: 11.fs, color: Colors.white),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
