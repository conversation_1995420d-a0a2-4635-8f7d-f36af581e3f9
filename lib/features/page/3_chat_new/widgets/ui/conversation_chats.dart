import 'package:flutter/material.dart';
import 'package:wd/core/utils/chat/enums.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/chat_tile.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_contact/widget/empty_contact_widget.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ConversationChats extends StatelessWidget {
  const ConversationChats({super.key, this.type, this.onMembersCountChange, this.onGroupsCountChange});

  final ChatScreenType? type;
  final Function(int count)? onMembersCountChange;
  final Function(int count)? onGroupsCountChange;

  @override
  Widget build(BuildContext context) {
    return TIMUIKitConversation(
      isShowOnlineStatus: false,
      emptyBuilder: () => const EmptyChatContact(),
      onMembersCountChange: onMembersCountChange,
      onGroupsCountChange: onGroupsCountChange,
      itemBuilder: (conversationItem, [onlineStatus]) {
        if (type == ChatScreenType.CONTACTS && conversationItem.type == 1) {
          return Container();
        }
        // return ChatConversationCell(data: conversationItem);

        return ChatTile(data: conversationItem);
      },
    );
  }
}
