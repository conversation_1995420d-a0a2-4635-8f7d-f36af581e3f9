import 'package:flutter/material.dart';
import 'package:wd/core/utils/color_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class TransactOrderMsg extends StatelessWidget {
  final Map<String, dynamic> data;

  const TransactOrderMsg({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    final content = data['content'] as List<dynamic>;

    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 8.gw),
              color: ColorUtil.stringToColor("${data["headBgColor"]}") ?? const Color(0xffDDBC92), // 设置背景色
              child: _buildTwoTextRow(
                title: '${data["title"]}',
                content: '${data["value"]}',
                isFontBold: true,
                titleColor: ColorUtil.stringToColor("${data["headTextColor"]}") ?? Colors.white,
                contentColor: ColorUtil.stringToColor("${data["headTextColor"]}") ?? Colors.white,
                contentTextAlign: TextAlign.left,
              )),
          Container(
              padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gw),
              child: ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) => _buildContentItem(item: content[index]),
                  separatorBuilder: (context, index) => SizedBox(height: 8.gw),
                  itemCount: content.length)),
        ],
      ),
    );
  }

  _buildContentItem({required Map item}) {
    final itemTitle = "${item['title']}";
    final itemValue = "${item['value']}";
    return _buildTwoTextRow(
      title: itemTitle,
      content: itemValue,
    );
  }

  _buildTwoTextRow({
    required String title,
    required String content,
    titleColor = const Color(0xffA5AEBE),
    contentColor = const Color(0xff49505D),
    contentTextAlign = TextAlign.left,
    isFontBold = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          constraints: BoxConstraints(maxWidth: 100.gw),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14.fs,
              color: titleColor,
              fontWeight: isFontBold ? FontWeight.bold : null,
            ),
          ),
        ),
        SizedBox(width: 5.gw),
        Flexible(
          child: Text(
            content,
            textAlign: contentTextAlign,
            style: TextStyle(
              fontSize: 14.fs,
              color: contentColor,
              fontWeight: isFontBold ? FontWeight.bold : null,
            ),
          ),
        ),
      ],
    );
  }
}
