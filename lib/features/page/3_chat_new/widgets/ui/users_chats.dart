import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/chat_service_online_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/tencent_im_util.dart';
import 'package:wd/features/page/3_chat_new/screens/chat_screen.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/dp.dart';
import 'package:wd/features/page/3_chat_plus/pages/chat_contact/widget/empty_contact_widget.dart';
import 'package:wd/features/page/3_chat_new/widgets/ui/expansion_tile.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class UsersChats extends StatefulWidget {
  const UsersChats({super.key, this.onMembersCountChange});

  final Function(int count)? onMembersCountChange;

  @override
  UsersChatsState createState() => UsersChatsState();
}

class UsersChatsState extends State<UsersChats> {
  @override
  Widget build(BuildContext context) {
    return MyExpansionTile(
      label: '我的好友',
      child: TIMUIKitContact(
        titleColor: context.colorTheme.textRegular,
        chatTileColor: context.theme.cardColor,
        onTapItem: (value) {
          TencentImUtil().notifyOnlineServiceClick(value.userID);

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: getConversation(
                  userID: value.userID,
                  name: value.friendRemark ?? value.userProfile?.nickName ?? value.userProfile?.userID ?? '',
                  faceUrl: value.userProfile?.faceUrl,
                ),
                freindInfo: value,
              ),
            ),
          );
        },
        avatarBuilder: (context, faceUrl, userId, name) {
          return Dp(
            faceUrl: faceUrl,
            type: 1,
            userId: userId,
            name: name,
          );
        },
        suffixBuilder: (context, userId, onTap) {
          return ListenableBuilder(
            listenable: TencentImUtil(),
            builder: (context, child) {
              ChatServiceOnlineEntity? model =
                  TencentImUtil().onlineServiceList.firstWhereOrNull((e) => e.toAccount == userId);
              if (model == null) return const SizedBox.shrink();
              final isOnline = model.status == "Online";
              return _buildOnlineStatusTag(isOnline);
            },
          );
        },
        emptyBuilder: (v) => const EmptyChatContact(),
        onCountChange: widget.onMembersCountChange,
      ),
    );
  }

  _buildOnlineStatusTag(bool isOnline) {
    final title = isOnline ? "在线" : "离线";
    final color = isOnline ? const Color(0xFF31CE49) : const Color(0xFFDEDEDE);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(color: color, width: 0.5),
        borderRadius: BorderRadius.circular(2.gw),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 10.fs,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
