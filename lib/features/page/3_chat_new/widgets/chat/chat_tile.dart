import 'package:flutter/material.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/color_pallette.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/tencent_im_util.dart';
import 'package:wd/features/page/3_chat_new/screens/chat_screen.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/chat_tile_heading.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/chat_tile_sub.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/dp.dart';
import 'package:wd/features/page/3_chat_new/widgets/chat/support_dp.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

/// 聊天列表项组件
/// 用于显示会话列表中的单个会话项，包括头像、名称、最后消息等信息
class ChatTile extends StatelessWidget {
  const ChatTile({
    super.key,
    required this.data, // 会话数据
  });

  /// 会话数据，包含会话类型、用户信息等
  final V2TimConversation data;

  @override
  Widget build(BuildContext context) {
    // 获取好友信息
    final freindInfo = data.friendInfo;

    // print("freindInfo>>> $freindInfo");
    return InkWell(
      // 点击会话项时的处理
      onTap: () {
        // 单聊类型
        if (data.type == 1) {

          if (data.userID != null) {
            TencentImUtil().notifyOnlineServiceClick(data.userID!);
          }
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: data,
                nickName: freindInfo?.userProfile?.userID,
                freindInfo: freindInfo, // 传递好友信息
              ),
            ),
          );
        }
        // 群聊类型
        if (data.type == 2) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: data,
                nickName: freindInfo?.userProfile?.userID,
              ),
            ),
          );
        }
      },
      child: Container(
        color: context.theme.cardColor,
        padding: const EdgeInsets.fromLTRB(16, 6, 16, 0),
        child: Column(
          children: [
            Row(
              children: [
                // 根据用户角色显示不同类型的头像
                // role为1或2时显示客服头像，否则显示普通用户头像
                if (freindInfo != null && (freindInfo.userProfile?.role == 1 || freindInfo.userProfile?.role == 2))
                  SupportDp(
                    faceUrl: data.faceUrl,
                    userId: data.userID ?? data.groupID,
                    name: freindInfo.userProfile?.nickName,
                  )
                else
                  Dp(
                    faceUrl: data.faceUrl,
                    // type: data.type,
                    userId: data.userID ?? data.groupID,
                    name: data.showName ?? freindInfo?.userProfile?.userID ?? data.userID ?? '',
                  ),
                const SizedBox(width: 10),
                // 会话信息区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ChatTileHeading(data: data), // 会话标题
                      ChatTileSub(data: data),     // 会话子标题（最后消息）
                    ],
                  ),
                ),
              ],
            ),
            // 分割线
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Divider(
                thickness: 0.35,
                height: 0,
                indent: 60,      // 左侧缩进
                endIndent: 15,   // 右侧缩进
                color: ColorPalette.greyColor2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
