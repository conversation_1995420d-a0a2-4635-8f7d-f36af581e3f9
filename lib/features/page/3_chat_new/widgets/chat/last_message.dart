import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitTextField/special_text/DefaultSpecialTextSpanBuilder.dart';
import 'package:extended_text/extended_text.dart';

class LastMessage extends StatelessWidget {
  const LastMessage({super.key, this.lastMessage});

  final V2TimMessage? lastMessage;

  @override
  Widget build(BuildContext context) {
    switch (lastMessage?.elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        final text = lastMessage?.textElem?.text ?? '';

        if (isRedEnvelope(lastMessage)) {
          return const MessageTypeStrip(
            icon: LucideIcons.wallet,
            label: '红包',
          );
        }

        return ExtendedText(
          TencentUtils.getContentSpan(text.replaceAll('TUIEmoji_', ''), context, decodeLink: false),
          maxLines: 1,
          style: TextStyle(
            fontSize: 14,
            color: context.colorTheme.textSecondary,
            overflow: TextOverflow.ellipsis,
          ),
          specialTextSpanBuilder: DefaultSpecialTextSpanBuilder(
            isUseQQPackage: true,
            isUseTencentCloudChatPackage: true,
            showAtBackground: true,
            checkHttpLink: true,
          ),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_CUSTOM:
        final customData = lastMessage?.customElem?.data;
        if (customData != null) {
          try {
            if (isTransactOrderMsg(lastMessage)) {
              return const MessageTypeStrip(
                icon: LucideIcons.file_archive,
                label: '订单消息',
              );
            }

            final decodedData = json.decode(customData);
            if (decodedData["businessID"] == 'delete') {
              return const SizedBox.shrink();
            }

            if (decodedData["mediaList"] != null && decodedData["mediaList"].isNotEmpty) {
              int videoCount = 0;
              int imageCount = 0;
              for (var item in decodedData["mediaList"]) {
                if (item["type"] == 0) {
                  imageCount++;
                } else if (item["type"] == 1) {
                  videoCount++;
                }
              }
              const video = '视频';
              const image = '图片';
              final label =
                  '${videoCount > 0 ? '$videoCount $video' : ''}${videoCount > 0 && imageCount > 0 ? ' ' : ''}${imageCount > 0 ? '$imageCount $image' : ''}';
              bool isOnlyVideo = videoCount > 0 && imageCount == 0;
              bool isOnlyImage = imageCount > 0 && videoCount == 0;
              return MessageTypeStrip(
                icon: isOnlyVideo
                    ? LucideIcons.video
                    : isOnlyImage
                        ? LucideIcons.image
                        : LucideIcons.images,
                label: label,
              );
            }
            if (decodedData['businessID'] == 'group_create') {
              return const MessageTypeStrip(
                icon: LucideIcons.users,
                label: '创建群',
              );
            }
          } catch (e) {}
          return const MessageTypeStrip(
            icon: LucideIcons.info,
            label: '[版本过低, 暂不支持此消息类型]',
          );
        }
        return const MessageTypeStrip(
          icon: LucideIcons.code,
          label: '自定义消息',
        );
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return const MessageTypeStrip(icon: LucideIcons.image, label: '图片');
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return const MessageTypeStrip(
          icon: LucideIcons.volume_2,
          label: '语音',
        );
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return const MessageTypeStrip(icon: LucideIcons.video, label: '视频');
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return const MessageTypeStrip(icon: LucideIcons.file, label: '文件');
      case MessageElemType.V2TIM_ELEM_TYPE_LOCATION:
        return const MessageTypeStrip(
          icon: LucideIcons.map_pin,
          label: '位置',
        );
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return const MessageTypeStrip(icon: LucideIcons.smile, label: '表情');
      case MessageElemType.V2TIM_ELEM_TYPE_GROUP_TIPS:
        return const SizedBox.shrink();
      // return const MessageTypeStrip(
      //   icon: LucideIcons.info,
      //   label: '群通知',
      // );
      case MessageElemType.V2TIM_ELEM_TYPE_MERGER:
        return const MessageTypeStrip(
          icon: LucideIcons.layers,
          label: '合并消息',
        );
      default:
        return const MessageTypeStrip(
          icon: LucideIcons.info,
          label: '未知消息',
        );
    }
  }
}

class MessageTypeStrip extends StatelessWidget {
  const MessageTypeStrip({
    super.key,
    required this.icon,
    required this.label,
    this.color,
  });

  final IconData icon;
  final String label;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? context.theme.primaryColor,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            label,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 14,
              color: color ?? context.theme.primaryColor,
              fontWeight: color != null ? FontWeight.w500 : null,
            ),
          ),
        ),
      ],
    );
  }
}

bool isRedEnvelope(V2TimMessage? message) {
  try {
    final msg = message?.textElem?.text;
    if (msg == null) return false;
    final parsedJson = json.decode(msg);
    final id = parsedJson['redEnvelopeId'];
    if (id != null) return true;
    return false;
  } catch (e) {
    return false;
  }
}

bool isTransactOrderMsg(V2TimMessage? message) {
  try {
    final msg = message?.customElem?.data;
    if (msg == null) return false;
    final parsedJson = json.decode(msg);
    final msgType = parsedJson['msgType'];
    if (msgType == 1) return true;
    return false;
  } catch (e) {
    return false;
  }
}
