import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';

class UnreadCount extends StatelessWidget {
  const UnreadCount({
    super.key,
    required this.unreadCount,
  });

  final String? unreadCount;

  @override
  Widget build(BuildContext context) {
    final unreadCountLength = unreadCount?.length ?? 0;
    final radius = unreadCountLength > 2 ? 12.0 : 10.0;
    int unreadCountInt = int.parse(unreadCount ?? '0');
    String unreadCountStr = unreadCount ?? '0';
    if (unreadCountInt > 99) {
      unreadCountStr = '99+';
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (unreadCount != '0')
          CircleAvatar(
            backgroundColor: context.theme.primaryColor,
            radius: radius,
            child: Text(
              unreadCountStr,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}
