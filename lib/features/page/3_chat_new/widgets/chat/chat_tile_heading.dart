import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/chat/chat_time_ago.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

/// 聊天列表项的标题组件
class ChatTileHeading extends StatelessWidget {
  const ChatTileHeading({
    super.key,
    required this.data,
  });

  /// 会话数据
  final V2TimConversation data;
  String getRealName(V2TimConversation? conversation) {
    String realName = conversation?.showName ??
        conversation?.friendInfo?.userProfile?.userID ??
        conversation?.userID ??
        '';
    if (conversation?.customData != null) {
      try {
        final Map<String, dynamic> customData =
            jsonDecode(conversation!.customData!);
        if (customData['userProfile'] != null &&
            customData['userProfile']['nickName'] != null &&
            customData['userProfile']['nickName'] != '') {
          return customData['userProfile']['nickName'];
        }
      } catch (e) {
        return realName;
      }
    }
    return realName;
  }

  @override
  Widget build(BuildContext context) {
    // 检查是否开启免打扰模式（会议类型群组除外）
    final isDisturb = (data.groupType == "Meeting" ? false : data.recvOpt != 0);
    // 获取最后一条消息的时间，格式化为"多久之前"的形式
    final lastTime =
        ChatTimeAgo.getTimeStringForChat(data.lastMessage?.timestamp ?? 0);

    // 获取好友信息
    final freindInfo = data.friendInfo;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 2,
          child: Row(
            children: [
              // 显示用户名或ID
              Text(
                getRealName(data),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                softWrap: false,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              // 如果开启免打扰，显示静音图标
              if (isDisturb) ...[
                const SizedBox(width: 10),
                const Icon(
                  LucideIcons.bell_off,
                  size: 14,
                  color: Colors.black26,
                ),
              ],
              // 如果是普通客服（role=1），显示普通客服标签
              if (freindInfo != null && freindInfo.userProfile?.role == 1) ...[
                const SizedBox(width: 10),
                const SupportTag.general(),
              ],
              // 如果是财务客服（role=2），显示财务客服标签
              if (freindInfo != null && freindInfo.userProfile?.role == 2) ...[
                const SizedBox(width: 10),
                const SupportTag.financial(),
              ],
            ],
          ),
        ),
        const SizedBox(width: 6),
        // 显示最后消息时间
        Expanded(
          flex: 1,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                lastTime ?? '',
                style: TextStyle(
                  fontSize: 12,
                  color: context.colorTheme.textHighlight
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// 客服标签组件
class SupportTag extends StatelessWidget {
  // 普通客服标签构造函数
  const SupportTag.general({
    super.key,
    this.color = Colors.green,
    this.label = 'General',
  });

  // 财务客服标签构造函数
  const SupportTag.financial({
    super.key,
    this.color = Colors.red,
    this.label = 'Financial',
  });

  final Color color;
  final String label;

  @override
  Widget build(BuildContext context) {
    // 创建带有圆角和背景色的标签容器
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: const TextStyle(
          fontSize: 12,
          color: Colors.white,
        ),
      ),
    );
  }
}
