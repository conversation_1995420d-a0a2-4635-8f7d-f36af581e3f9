import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/transact/top_up/top_up_history_cell.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import '../../../../../../core/constants/assets.dart';
import 'top_up_history_list_cubit.dart';
import 'top_up_history_list_state.dart';

class TopUpHistoryListPage extends BasePage {
  const TopUpHistoryListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _TopUpHistoryListPageState();
}

class _TopUpHistoryListPageState extends BasePageState<TopUpHistoryListPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = "充值记录";
    isNeedEmptyDataWidget = false;
    isAllowBack = false;
    _tabController = TabController(length: 3, vsync: this)
      ..addListener(() {
        final type = RecordDateType.values[_tabController.index];
        context.read<TopUpHistoryListCubit>().currentDateTypeChanged(type);
      });
    _getData();
  }

  _getData() {
    context.read<TopUpHistoryListCubit>().fetchListData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget right() {
    return GestureDetector(
      child: Image.asset(Assets.iconFilter, width: 36.gw, height: 36.gw),
      onTap: () {
        final cubit = context.read<TopUpHistoryListCubit>();
        final state = cubit.state;
        TransactHistoryFilterPopup.show(
          context: context,
          type: TransactType.topUp,
          filterWayList: state.filterWayList,
          filterTypeList: state.filterTypeList,
          onClickSure: () {
            cubit.updatePageNo(1);
            cubit.fetchListDataWithFilter();
          },
        );
      },
    );
  }

  /// 下拉刷新
  void _onRefresh() {
    context.read<TopUpHistoryListCubit>().updatePageNo(1);
    context.read<TopUpHistoryListCubit>().updateIsNoMoreDataState(false);
    context.read<TopUpHistoryListCubit>().fetchListData();
  }

  /// 下拉刷新
  void _onLoading() {
    context.read<TopUpHistoryListCubit>().updatePageNoToNext();
    context.read<TopUpHistoryListCubit>().fetchListData();
  }

  Widget mainPageWidget(TopUpHistoryListState state) {
    return GSDateTabBarWidget(tabController: _tabController, children: [
      if (state.netState == NetState.emptyDataState) ...[
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              emptyWidget('暂无数据'),
            ],
          ),
        )
      ],
      if (state.netState == NetState.dataSuccessState) ...[
        Expanded(
          child: AnimationLimiter(
            child: CommonRefresher(
              enablePullDown: true,
              enablePullUp: true,
              refreshController: refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              listWidget: ListView.separated(
                itemBuilder: (context, index) {
                  final model = state.dataList![index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      horizontalOffset: 50.0,
                      child: FadeInAnimation(
                        child: GestureDetector(
                            onTap: () {
                              sl<NavigatorService>().push(AppRouter.transactTopUpHistoryDetail, arguments: model);
                            },
                            child: TopUpHistoryCell(
                              model: model,
                            )),
                      ),
                    ),
                  );
                },
                separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                itemCount: state.dataList!.length,
              ),
            ),
          ),
        ),
      ]
    ]);
  }

  void _listener(BuildContext context, TopUpHistoryListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<TopUpHistoryListCubit, TopUpHistoryListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
