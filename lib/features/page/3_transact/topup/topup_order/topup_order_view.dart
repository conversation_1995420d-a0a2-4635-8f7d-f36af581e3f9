import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/entities/commit_top_up_entity.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/features/page/3_transact/topup/topup_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/container/golden_border_container.dart';

import 'topup_order_cubit.dart';
import 'topup_order_state.dart';

class TopupOrderPage extends BasePage {
  final CommitTopUpEntity model;
  final int channelId;

  const TopupOrderPage({
    super.key,
    required this.model,
    required this.channelId,
  });

  @override
  BasePageState<BasePage> getState() => _TopupOrderPageState();
}

class _TopupOrderPageState extends BasePageState<TopupOrderPage> {
  late TopUpOrderExt order;

  @override
  void initState() {
    order = widget.model.ext!;
    setupData();
    super.initState();
  }

  setupData() {
    if (order.payType == "ONLINE_BANK") {
      pageTitle = "网银充值";
    } else if (order.payType == "USDT") {
      pageTitle = "USDT充值";
    } else {
      pageTitle = "充值订单";
    }
  }

  _userOnClickSubmit() {
    sl<TopupCubit>()
        .submitOfflinePayment(context, channelId: widget.channelId, amount: order.orderAmount, orderNo: order.orderNo);
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => TopupOrderCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<TopupOrderCubit>(context);

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.gw),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Padding(padding: EdgeInsets.only(top: 32.gw), child: _buildOrderInfo(context)),
                      Positioned(
                        top: 14.gw,
                        child: SizedBox(
                          width: 48.gw,
                          height: 48.gw,
                          child: _buildImageByBase64String(
                            order.bannerImg
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.gw),
                  _buildInstructions(),
                  SizedBox(height: 16.gw),
                ],
              ),
            ),
          ),
        ),
        _buildBottomButton(context),
      ],
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return GoldenBorderContainer(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(15.gw),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                if (order.payType == "ONLINE_BANK") ...[
                  _buildInfoRow('订单号', order.orderNo),
                  _buildInfoRow('充值金额', order.orderAmount.formattedMoney),
                  _buildInfoRow('收款人', order.payee),
                  _buildInfoRow('银行卡号', order.bankCard),
                  _buildInfoRow('收款银行', order.receivingBank),
                  _buildOrderStatusItem(),
                  SizedBox(height: 16.gw),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5.gw),
                    child: Text(
                      '1.请手动复制收款人及银行卡号进行转账，转账完成后，联系客服手动上分。\n2.每个卡号只能成功支付一次，请勿重复支付(5分钟内有效)',
                      style: TextStyle(
                        fontSize: 12.fs,
                        color: const Color(0xff999999),
                      ),
                    ),
                  ),
                  SizedBox(height: 8.gw),
                  Text(
                    '此单重复支付和超时支付不到账不退',
                    style: TextStyle(
                      fontSize: 12.fs,
                      color: const Color(0xffFE0012),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ] else if (order.payType == "USDT") ...[
                  _buildInfoRow('币种', order.currency, showCopy: false),
                  _buildInfoRow(
                    '公链协议',
                    order.pact,
                    suffixIcon: _buildSuffixIcon(order.chainImg),
                  ),
                  _buildInfoRow(
                    '冲币数量',
                    order.depositNo.formattedMoney,
                    suffixIcon: _buildSuffixIcon(order.usdtImg),
                  ),
                  _buildInfoRow(
                    '汇率',
                    order.exchangeRate,
                    showCopy: false,
                    suffixIcon: _buildSuffixIcon(order.exchangeRateImg),
                  ),
                  _buildInfoRow(
                    '存款金额',
                    order.orderAmount.formattedMoney,
                    showCopy: false,
                    contentStyle: TextStyle(
                      fontSize: 14.fs,
                      color: const Color(0xff55AB37),
                    ),
                    suffixIcon: _buildSuffixIcon(order.cnyImg),
                  ),
                  _buildInfoRow('订单号', order.orderNo),
                  _buildInfoRow(
                    '充币地址',
                    order.depositAddress,
                    contentStyle: TextStyle(
                      fontSize: 14.fs,
                      color: const Color(0xff000000),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  _buildOrderStatusItem(),
                  SizedBox(height: 15.gw),
                  Text(
                    '请务必完整复制并仔细核对，以免造成损失',
                    style: TextStyle(
                      fontSize: 12.fs,
                      color: const Color(0xffFE0012),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 10.gw),
                  _buildQrCode(order.addressQrCode),
                ] else ...[
                  Text(
                    '暂不支持此类型，请更新App',
                    style: TextStyle(
                      fontSize: 12.fs,
                      color: const Color(0xffFE0012),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value,
      {bool showCopy = true, TextStyle? contentStyle, Widget? suffixIcon}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.gw),
          child: Row(
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.fs,
                  color: const Color(0xff5A5C69),
                ),
              ),
              SizedBox(width: 10.gw),
              Expanded(
                child: InkWell(
                  onTap: () {
                    if (showCopy) {
                      ClipboardTool.setDataToast(value, msg: "已复制$label");
                    }
                  },
                  child: Row(
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      suffixIcon != null
                          ? Padding(
                              padding: EdgeInsets.only(
                                right: 4.gw,
                              ),
                              child: suffixIcon,
                            )
                          : const SizedBox.shrink(),
                      Flexible(
                        child: Text(
                          value,
                          textAlign: TextAlign.right,
                          style: contentStyle ??
                              TextStyle(
                                fontSize: 14.fs,
                                color: const Color(0xff5A5C69),
                              ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      if (showCopy)
                        Image.asset(
                          "assets/images/button/btn_copy.png",
                          width: 12.gw,
                          height: 12.gw,
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          color: const Color(0xffF0F0F0),
          height: 0.5,
        )
      ],
    );
  }

  _buildOrderStatusItem() {
    return BlocBuilder<TopupOrderCubit, TopupOrderState>(
      builder: (context, state) {
        return _buildInfoRow('订单状态', '${order.orderStatus}(${TimeUtil.toCountdownMinSec(state.remainingSeconds)}s)',
            showCopy: false);
      },
    );
  }

  Widget _buildInstructions() {
    if (order.tipHead.isNotEmpty || order.tipHead.isNotEmpty) {
      return GoldenBorderContainer(
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(15.gw),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (order.tipHead.isNotEmpty) ...[
                Text(
                  order.tipHead,
                  style: TextStyle(
                    fontSize: 16.fs,
                    color: const Color(0xff000000),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 14.gw),
              ],
              if (order.tipHead.isNotEmpty) _buildInstruction(order.tipContent),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildInstruction(content) {
    return Text(
      content,
      style: TextStyle(
        fontSize: 14.fs,
        color: const Color(0xff999999),
      ),
    );
  }

  Widget _buildBottomButton(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.gw),
          topRight: Radius.circular(8.gw),
        ),
        color: Colors.white,
      ),
      child: Column(
        children: [
          CommonButton(
            title: '已完成充值，联系客服确认订单',
            bgImgPath: 'assets/images/button/bg_button_golden_36h.png',
            titlePadding: EdgeInsets.only(top: 1.gw, bottom: 7.gw),
            backgroundColor: Colors.transparent,
            height: 46.gw,
            fontSize: 14.fs,
            enable: true,
            onPressed: _userOnClickSubmit,
          ),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom,
          )
        ],
      ),
    );
  }

  Widget _buildQrCode(String qrCodeData) {
    return SizedBox(
      width: 95.gw,
      height: 95.gw,
      child: _buildImageByBase64String(qrCodeData),
    );
  }

  _buildSuffixIcon(String dataStr) {
    return SizedBox(
      width: 16.gw,
      height: 16.gw,
      child: _buildImageByBase64String(dataStr),
    );
  }

  _buildImageByBase64String(String qrCodeData) {
    // 移除 data URI 前缀
    final String pureBase64 = qrCodeData.replaceAll(RegExp(r'data:image/\w+;base64,'), '');

    // final errorIcon = Container(color: Colors.red,);
    final errorIcon = Icon(
      Icons.error_outline,
      color: Colors.grey[300],
      size: 16.gw,
    );

    try {
      return Image.memory(
        base64Decode(pureBase64),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return errorIcon;
        },
      );
    } catch (e) {
      return errorIcon;
    }
  }
}
