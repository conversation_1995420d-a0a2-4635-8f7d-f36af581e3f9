import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/third_party_wallet_info_entity.dart';
import 'package:wd/core/models/entities/top_up_list_entity.dart';
import 'package:wd/core/utils/chat/utils.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/3_chat_new/screens/chat_screen.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/dialog/third_party_wallet_fund_password_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

part 'topup_state.dart';

class TopupCubit extends Cubit<TopupState> {
  TopupCubit() : super(const TopupState()) {
    fetchTopUpList();
  }

  void fetchTopUpList() async {
    GSEasyLoading.showLoading();
    final list = await TransactApi.fetchOnlinePaymentList();

    GSEasyLoading.dismiss();
    if (list.isNotEmpty) {
      emit(state.copyWith(
          topUpList: list,
          selectedPayWay: list.first,
          selectedPayType: list.first.payTypeList.first,
          giftTips: list.first.payTypeList.isNotEmpty ? list.first.payTypeList.first.present : null,
          netState: NetState.dataSuccessState));
      checkFirstPayTypeFixedAmountSelected(list.first);
    } else {
      emit(state.copyWith(netState: NetState.emptyDataState));
    }

    // 异步加载钱包信息
    for (TopUpListEntity e in list) {
      for (TopUpListPayTypeList m in e.payTypeList) {
        await Future.delayed(const Duration(seconds: 5));
        if (m.redirectWalletLobby == true) {
          fetchThirdPartyWalletBalance(m);
        }
      }
    }
  }


  /// 获取三方钱包大厅链接
  Future<String?> fetchThirdPartyWalletLobbyLink(TopUpListPayTypeList? model) async {
    if (model == null) return null;
    final link = await TransactApi.fetchThirdPartyWalletLobbyLink(channelId: model.payTypeId);
    model.lobbyLink = link;
    return model.lobbyLink;
  }

  /// 获取三方钱包余额
  fetchThirdPartyWalletBalance(TopUpListPayTypeList? model) async {
    if (model == null) return;
    ThirdPartyWalletInfoEntity? res = await TransactApi.fetchThirdPartyWalletBalance(channelId: model.payTypeId);
    if (res != null) {
      model.balance = res.balance;
    }
  }

  /// 前往三方大厅
  Future<void> goToThirdPartyWalletLobby(TopUpListPayTypeList? model, [int attempt = 1]) async {
    if (model == null) return Future.value();
    if (attempt > 3) {
      return GSEasyLoading.showToast('获取大厅链接失败，请稍后重试');
    }

    await fetchThirdPartyWalletLobbyLink(model);
    SystemUtil.openUrlOnSystemBrowser(url: model.lobbyLink!);
  }

  void checkFirstPayTypeFixedAmountSelected(TopUpListEntity? selectedPayWay) {
    if (selectedPayWay == null) return;
    if (selectedPayWay.payTypeList.isNotEmpty && selectedPayWay.payTypeList[0].fixedAmount) {
      final amountList = selectedPayWay.payTypeList[0].amountList.split(",");
      emit(state.copyWith(input: amountList.first));
    } else {
      emit(state.copyWith(input: ""));
    }
  }

  void selectPayWay(TopUpListEntity selectedPayWay) {
    emit(state.copyWith(
      selectedPayWay: selectedPayWay,
      selectedPayType: selectedPayWay.payTypeList.first,
      giftTips: selectedPayWay.payTypeList.isNotEmpty ? selectedPayWay.payTypeList.first.present : null,
    ));
    checkFirstPayTypeFixedAmountSelected(selectedPayWay);
  }

  void selectPayType(TopUpListPayTypeList payType) {
    String input = '';
    bool isInputEnabled = true;
    if (payType.amountMaxLimit == payType.amountMinLimit) {
      input = payType.amountMaxLimit.toString();
      isInputEnabled = false;
    }
    emit(state.copyWith(
        selectedPayType: payType, input: input, isInputEnabled: isInputEnabled, giftTips: payType.present));

    if (payType.fixedAmount) {
      final amountList = payType.amountList.split(",");
      emit(state.copyWith(input: amountList.first));
    } else {
      emit(state.copyWith(input: ""));
    }
  }

  void updateInput(String input) {
    emit(state.copyWith(input: input));
  }

  submitPayment(context) async {
    sl<NavigatorService>().unFocus();
    if (state.selectedPayType == null) return GSEasyLoading.showToast('请先选择渠道');
    TopUpListPayTypeList model = state.selectedPayType!;
    double? amount = double.tryParse(state.input);
    if (amount == null) {
      return GSEasyLoading.showToast('请选定或输入金额');
    }

    if (amount < model.amountMinLimit) {
      return GSEasyLoading.showToast('当前金额小于「渠道最小金额」');
    }

    if (model.redirectWalletLobby == true) {
      return submitThirdPartyWalletPayment(context, model: model, amount:  amount);
    }

    int amountLimit = int.tryParse(model.amountLimit) ?? 0;

    // 1.线上类型 (offlineChatPayType: 0)
    // 默认走原来流程
    // 如果有超大金额 amountLimit ！="0" 或者 amountLimit ！= null
    // a.判断用户选择的金额或者输入的金额，是否大于超大金额(amountLimit字段值)
    // b.如果大于超大金额限制，则跳转至客服 /cashin/submitOfflinePay
    // c.否则走原来流程 /cashin/submitOnlinePay
    //
    // 2.线下类型 (offlineChatPayType: 1)
    // a.直接跳转至客服 /cashin/submitOfflinePay
    //
    // 3.线下-中间页类型 (offlineChatPayType: 2)
    // a.先调用发起支付接口获取中间页数据  /cashin/submitOnlinePay
    // b.用户在中间页确认后跳转至客服 /cashin/submitOfflinePay
    if (model.offlineChatPayType == 1 || (model.offlineChatPayType == 0 && amountLimit != 0 && amount >= amountLimit)) {
      return submitOfflinePayment(context, channelId: model.payTypeId, amount: amount);
    }

    GSEasyLoading.showLoading();
    final res = await TransactApi.submitPayment(channelId: model.payTypeId, amount: amount);
    GSEasyLoading.dismiss();
    if (res != null) {
      if (model.offlineChatPayType == 2) { /// 线下中间页
        if (res.ext == null) return GSEasyLoading.showToast("订单参数异常");
        sl<NavigatorService>().push(AppRouter.topUpOrderDetail, arguments: {"model": res, "channelId": model.payTypeId});
      } else {
        CommonDialog.show(
            context: context,
            title: "提示",
            content: "是否跳转支付链接",
            sureBtnTitle: "前往支付",
            complete: () {
              SystemUtil.openUrlOnSystemBrowser(url: res.payUrl);
            });
      }
      state.copyWith(input: '');
    }
  }

  submitOfflinePayment(context, {channelId, amount, orderNo}) async {
    GSEasyLoading.showLoading();
    final res = await TransactApi.submitOfflineOrder(channelId: channelId, amount: amount, orderNo: orderNo, isTopUp: true);
    GSEasyLoading.dismiss();
    if (res != null) {
      sl<MainScreenCubit>().onChangeShowChatFloatWidget(false);
      await Navigator.push<dynamic>(
        context,
        MaterialPageRoute(
          builder: (context) => ChatScreen(
            selectedConversation: getConversation(
              userID: res.userNo,
              name: res.nickName,
            ),
          ),
        ),
      );
      sl<MainScreenCubit>().onChangeShowChatFloatWidget(true);
      state.copyWith(input: '');
    }
  }

  submitThirdPartyWalletPayment(context, {required TopUpListPayTypeList model, required double amount, int attempt = 1}) async {
    if (model.balance == 0) {
      return GSEasyLoading.showToast('余额不足，请先购币');
    }
    if (model.balance == null) {
      if (attempt > 3) {
        return GSEasyLoading.showToast('获取钱包余额失败，请稍后重试');
      }
      await fetchThirdPartyWalletBalance(model);
      return submitThirdPartyWalletPayment(context, model: model, amount: amount, attempt: attempt + 1);
    }
    if (model.balance != null && model.balance! < amount) {
      return GSEasyLoading.showToast("钱包余额小于「充值金额」");
    }
    String? password = await ThirdPartyWalletFundPasswordDialog.show(context);
    if (password != null) {

      GSEasyLoading.showLoading();
      final flag = await TransactApi.fetchThirdPartyWalletSubmitCharge(channelId: model.payTypeId, amount: amount, password: password);
      GSEasyLoading.dismiss();
      if (flag) {
        GSEasyLoading.showToast("充值成功");
        fetchTopUpList();
      }
    }
  }


}
