import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_list/withdraw_history_list_cubit.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_history/withdraw_history_list/withdraw_history_list_state.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/button/service_button.dart';
import 'package:wd/shared/widgets/transact/transact_section_widget.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_progress_cell.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class WithdrawProgressPage extends BasePage {
  const WithdrawProgressPage({super.key});

  @override
  BasePageState<BasePage> getState() => _WithdrawProgressPageState();
}

class _WithdrawProgressPageState extends BasePageState<WithdrawProgressPage> {
  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    pageTitle = "提现进度";
    super.initState();
  }

  @override
  Widget right() {
    return const ServiceButton();
  }

  /// 下拉刷新
  void _onRefresh() {
    context.read<WithdrawHistoryListCubit>().updatePageNo(1);
    context.read<WithdrawHistoryListCubit>().updateIsNoMoreDataState(false);
    context.read<WithdrawHistoryListCubit>().fetchListData();
  }

  /// 下拉刷新
  void _onLoading() {
    context.read<WithdrawHistoryListCubit>().updatePageNoToNext();
    context.read<WithdrawHistoryListCubit>().fetchListData();
  }

  void _listener(BuildContext context, WithdrawHistoryListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = context.read<WithdrawHistoryListCubit>();
    return BlocConsumer<WithdrawHistoryListCubit, WithdrawHistoryListState>(
      listener: _listener,
      builder: (context, state) {
        return Column(children: [
          SizedBox(height: 8.gw),
          _getTipsWidget(),
          SizedBox(height: 1.gw),
          if (state.netState == NetState.emptyDataState) ...[
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  emptyWidget('暂无数据'),
                ],
              ),
            )
          ],
          if (state.netState == NetState.dataSuccessState) ...[
            Expanded(
              child: AnimationLimiter(
                child: CommonRefresher(
                  enablePullDown: true,
                  enablePullUp: true,
                  refreshController: refreshController,
                  onRefresh: _onRefresh,
                  onLoading: _onLoading,
                  listWidget: ListView.separated(
                    padding: EdgeInsets.only(top: 8.gw),
                    itemBuilder: (BuildContext context, int index) {
                      final model = state.dataList![index];
                      if (index == 0) {
                        sl<UserCubit>().onChangeWithdrawFloatBtnTitle(model);
                      }
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          horizontalOffset: 50.0,
                          child: FadeInAnimation(
                            child: GestureDetector(
                              onTap: () {
                                sl<NavigatorService>().push(AppRouter.transactWithdrawHistoryDetail, arguments: model);
                              },
                              child: WithdrawProgressCell(
                                model: model,
                                isExpanded: index == 0,
                                onTapCell: () async => await cubit.operateWithdrawRecordRead(model),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                    itemCount: state.dataList!.length,
                  ),
                ),
              ),
            ),
          ]
        ]);
      },
    );
  }

  _getTipsWidget() {
    const htmlStr = """
          <p>1.<span style="color: rgb(179, 149, 114);">审核通过</span>则表示该订单已完成，提现金额已到账。</p>
          <p>2.<span style="color: rgb(179, 149, 114);">审核驳回</span>则表示该笔订单提现异常，请联系<span style="color: rgb(179, 149, 114);">在线客服确认</span>。</p>
          <p>3.审核通过后，若未到账或部分金额到账，请联系在线客服处理。</p>
                    """;
    return TransactSectionWidget(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 9.gw),
          Text(
            "温馨提示",
            style: TextStyle(fontSize: 16.fs, color: const Color(0xffB39572)),
          ),
          Image.asset("assets/images/transact/icon_tip_bottom.png", width: 198.gw, height: 8.8.gw),
          SizedBox(height: 10.gw),
          Html(data: htmlStr, style: {
            'p': Style(
                fontSize: FontSize.medium,
                // 设置字体大小
                color: const Color(0xff6A7391),
                lineHeight: LineHeight.number(1.2),
                // 设置行高
                margin: Margins.only(bottom: 3),
                padding: HtmlPaddings.zero),
          }),
          SizedBox(height: 10.gw),
        ],
      ),
    );
  }
}
