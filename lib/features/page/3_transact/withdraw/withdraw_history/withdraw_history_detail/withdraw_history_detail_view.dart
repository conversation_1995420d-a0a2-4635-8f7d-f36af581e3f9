import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/shared/widgets/common_app_bar.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/row/text_row.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_history_cell.dart';

class WithdrawHistoryDetailPage extends StatelessWidget {
  final WithdrawRecord model;

  const WithdrawHistoryDetailPage({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(pageTitle: "提现详情"),
      body: CommonCardPage(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
            margin: EdgeInsets.fromLTRB(15.gw, 10.gw, 15.gw, 0),
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.gw), boxShadow: const [
              BoxShadow(
                color: Color(0xfff4f4fc),
                blurRadius: 20,
                offset: Offset(0, 0),
              ),
            ]),
            child: Column(
              children: [
                TextRow(
                  title: "当前状态",
                  rightWidget: WithdrawHistoryCell.buildOrderStatusWidget(context, orderStatus: model.orderStatus),
                ),
                TextRow(
                  title: "订单号",
                  rightWidget: GSTextImageButton(
                    text: model.transactionNo,
                    textStyle: Theme.of(context).textTheme.titleMedium,
                    imageAssets: "assets/images/common/icon_copy_black.png",
                    position: GSTextImageButtonPosition.right,
                    onPressed: () {
                      ClipboardTool.setData(model.transactionNo);
                    },
                  ),
                ),
                TextRow(
                  title: "提现方式",
                  content: model.cashoutWayName,
                ),
                TextRow(
                  title: "提现类型",
                  content: model.cashoutTypeName,
                ),
                if (model.orderAmount > 0)
                  TextRow(
                    title: "订单金额",
                    content: "${model.orderAmount}",
                  ),
                if (model.reduceAmount > 0)
                  TextRow(
                    title: "优惠金额",
                    content: "${model.reduceAmount}",
                  ),
                TextRow(
                  title: "手续费率",
                  content: "${model.serviceChargeRate}%",
                ),
                TextRow(
                  title: "手续费",
                  content: "${model.serviceCharge}",
                ),
                if (model.finalAmount > 0)
                  TextRow(
                    title: "到账金额",
                    content: "${model.finalAmount}",
                  ),
                if (model.cardNo.isNotEmpty)
                  TextRow(
                    title: "转入的银行卡号",
                    content: model.cardNo,
                  ),
                TextRow(
                  title: "充值时间",
                  content: model.requestTime,
                ),
                TextRow(
                  title: "操作时间",
                  content: model.operateTime,
                ),
                if (model.orderStatus == 2) // 未通过显示「拒绝原因」
                  TextRow(
                    title: "备注",
                    content: model.refusalRemark,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
