import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';

class WithdrawState extends BaseState {
  double availableBalance = 0;
  String input = '';
  bool fetchingBankInfo = false;
  bool fetchingSubmit = false;
  List<Map<String, String>> withdrawTypeList = [
    {"title": "银行卡", "image": "assets/images/transact/icon_payment_union_pay.png"},
    {"title": "钱包", "image": "assets/images/transact/icon_payment_wallet.png"},
    {"title": "人工通道", "image": "assets/images/transact/icon_payment_manual_channel.png"},
  ];
  WithdrawType type = WithdrawType.bankCard;
  WithdrawUserBankInfoEntity? currentSelBank;
  WithdrawUserBankInfoEntity? currentSelWallet;
  WithdrawUserBankInfoEntity? currentSelUsdt;
  List<WithdrawUserBankBrief>? myBankCardList;
  List<WithdrawUserBankBrief>? myWalletList;
  List<WithdrawUserBankBrief>? myUsdtList;
  List<WithdrawManualChannelEntity>? manualChannelList;
  WithdrawManualChannelEntity? currentSelManualChannel;

  WithdrawState init() {
    return WithdrawState()
      ..availableBalance = 0
      ..input = ""
      ..fetchingBankInfo = false
      ..fetchingSubmit = false
      ..myBankCardList = []
      ..myWalletList = []
      ..myUsdtList = []
      ..type = WithdrawType.bankCard
      ..currentSelBank = null
      ..currentSelWallet = null
      ..currentSelUsdt = null
      ..manualChannelList = null
      ..currentSelManualChannel = null
      ..netState = NetState.initializeState;
  }

  WithdrawState clone() {
    return WithdrawState()
      ..availableBalance = availableBalance
      ..input = input
      ..fetchingBankInfo = fetchingBankInfo
      ..fetchingSubmit = fetchingSubmit
      ..myBankCardList = myBankCardList
      ..myWalletList = myWalletList
      ..myUsdtList = myUsdtList
      ..type = type
      ..currentSelBank = currentSelBank
      ..currentSelWallet = currentSelWallet
      ..currentSelUsdt = currentSelUsdt
      ..manualChannelList = manualChannelList
      ..currentSelManualChannel = currentSelManualChannel
      ..netState = netState;
  }
}
