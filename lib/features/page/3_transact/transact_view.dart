import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/image_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/features/page/3_transact/topup/topup_view.dart';
import 'package:wd/features/page/3_transact/withdraw/withdraw_view.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/gstext_image_button.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/text/gradient_text.dart';

import 'transact_cubit.dart';
import 'transact_state.dart';

class TransactPage extends StatefulWidget {
  const TransactPage({super.key});

  @override
  _TransactPageState createState() => _TransactPageState();
}

class _TransactPageState extends State<TransactPage> with SingleTickerProviderStateMixin {
  ui.Image? _tabIndicatorImage;
  late TabController _tabController;
  final tabList = ["recharge".tr(), "withdraw".tr()];
  final PageController _pageController = PageController();
  int pageViewIndex = 0;

  @override
  void initState() {
    _loadTabIndicatorImage();

    final cubit = context.read<TransactCubit>();
    _tabController = TabController(length: tabList.length, vsync: this)
      ..addListener(() {
        if (_tabController.indexIsChanging) return;
        if (_tabController.index != _tabController.previousIndex) {
          if (_tabController.index == 0) {
            cubit.fetchTopUpListData();
          } else {
            cubit.fetchWithdrawBankListData(showLoading: true);
          }
          _pageController.jumpToPage(_tabController.index);
        }
      });
    cubit.pageController = _pageController; // 将 PageController 传递给 Cubit

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _pageController.jumpToPage(cubit.state.currentPageIndex);
    });

    super.initState();
  }

  _loadTabIndicatorImage() async {
    _tabIndicatorImage = await ImageUtil.loadImageAsset('assets/images/home/<USER>');
    setState(() {}); // 触发界面重新构建
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TransactCubit, TransactState>(
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            elevation: 0.1,
            shadowColor: const Color(0x80B6B6B6),
            backgroundColor: Colors.white,
            centerTitle: true,
            title: TabBar(
              tabAlignment: TabAlignment.center,
              padding: EdgeInsets.zero,
              // isScrollable: true,
              controller: _tabController,
              indicatorPadding: EdgeInsets.zero,
              labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
              indicator:
                  _tabIndicatorImage != null ? HomeTabIndicator(image: _tabIndicatorImage!, radius: 10.gw) : null,
              tabs: tabList.map((str) {
                final isSelected = _tabController.index == tabList.indexOf(str);
                return _buildTabItem(text: str, isSelected: isSelected);
              }).toList(),
            ),
            leading: SizedBox(width: 50.gw),
            actions: [
              GSTextImageButton(
                text: "记录",
                titleColor: const Color(0xff838597),
                titleSize: 13.fs,
                padding: EdgeInsets.only(right: 14.gw),
                imageAssets: "assets/images/transact/v3/btn_record.png",
                onPressed: () {
                  AuthUtil.checkIfLogin(() {
                    if (_tabController.index == 0) {
                      sl<NavigatorService>().push(AppRouter.transactTopUpHistoryList);
                    } else {
                      sl<NavigatorService>().push(AppRouter.transactWithdrawHistoryList);
                    }
                  });
                },
              )
            ],
          ),
          body: PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            onPageChanged: (index) {
              pageViewIndex = index;
              _tabController.animateTo(index);
              FocusScope.of(context).unfocus();
              context.read<TransactCubit>().jumpToPage(index);
            },
            children: const [
              TopupPage(),
              WithdrawPage(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabItem({
    required String text,
    required bool isSelected,
  }) {
    var colors = const [Color(0xff6A7391), Color(0xff6A7391)];
    if (isSelected) colors = const [Color(0xffEACA9F), Color(0xffB9936D)];
    return GradientText(
      text: text,
      colors: colors,
      fontSize: 16.fs,
    );
  }
}
