import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/notification/notification_badge.dart';

import 'home_drawer_cubit.dart';
import 'home_drawer_state.dart';

class HomeDrawer extends StatelessWidget {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
        elevation: 0,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        width: 290,
        child: ListView(padding: EdgeInsets.fromLTRB(15.gw, 58.gw, 15.gw, 0), children: <Widget>[
          Text(
            'convenient_features'.tr(),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(width: 10.gw),
          _buildCustomListTile(
            context,
            icon: "assets/images/drawer/icon_drawer_wallet.png",
            text: 'my_wallet'.tr(),
            action: BlocSelector<UserCubit, UserState, String?>(
              selector: (state) => state.balanceInfo?.accountMoney.formattedMoney,
              builder: (context, balance) {
                return balance != null
                    ? Row(
                        children: [
                          Image.asset('assets/images/drawer/icon_drawer_finance.png', width: 20, height: 20),
                          SizedBox(width: 5.gw),
                          Text(balance,
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontFamily: 'WeChatSansStd',
                                    fontWeight: FontWeight.w400,
                                  ))
                        ],
                      )
                    : Container();
              },
            ),
            onTap: () {
              AuthUtil.checkIfLogin(() {
                sl<NavigatorService>().push(AppRouter.myWallet);
              });
            },
          ),
          _buildCustomListTile(
            context,
            icon: "assets/images/drawer/icon_drawer_message.png",
            text: 'my_notifications'.tr(),
            action: const NotificationBadge(),
            onTap: () {
              AuthUtil.checkIfLogin(() {
                sl<NavigatorService>().push(AppRouter.notifications);
              });
            },
          ),
          if (SystemUtil.isWeb())
            _buildCustomListTile(context, icon: "assets/images/drawer/icon_drawer_app.png", text: 'download_app'.tr(),
                onTap: () {
              SystemUtil.goToAppDownload();
            }),
          _buildCustomListTile(context, icon: "assets/images/mine/icon_mine_support.png", text: 'get_help'.tr(),
              onTap: () {
            SystemUtil.contactService();
          }),
          _buildCustomListTile(context,
              icon: "assets/images/navigation_bar/nav_chat_selected.png", text: 'community_chat'.tr(), onTap: () {
            if (!sl<UserCubit>().state.isLogin) {
              AuthUtil.checkIfLogin(() async {
                await Future.delayed(const Duration(seconds: 1));
                sl<MainScreenCubit>().goToChatPage();
              });
            } else {
              sl<MainScreenCubit>().goToChatPage();
            }
          }),
          SizedBox(height: 15.gw),
          Text(
            'language_mode'.tr(),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: 10.gw),
          BlocBuilder<HomeDrawerCubit, HomeDrawerState>(
            builder: (context, state) {
              return Wrap(
                  spacing: 10.gw,
                  children: state.localList
                      .map((element) => _getSelButton(
                            context,
                            title: element.name,
                            isSel: element.toLocale() == LocaleUtil().appLocale,
                            onPressed: () => context.read<HomeDrawerCubit>().toggleLanguage(context, element),
                          ))
                      .toList());
            },
          ),
        ]));
  }

  Widget _buildCustomListTile(
    BuildContext context, {
    required String icon,
    required String text,
    Widget? action,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        children: [
          SizedBox(
            height: 50.gw,
            child: Row(
              children: <Widget>[
                Image.asset(
                  icon,
                  width: 24.gw,
                  height: 24.gw,
                ),
                SizedBox(width: 8.gw),
                Text(text, style: Theme.of(context).textTheme.titleLarge),
                const Spacer(),
                if (action != null) action,
                const Icon(Icons.chevron_right),
              ],
            ),
          ),
          Divider(height: 1, color: Theme.of(context).dividerColor),
        ],
      ),
    );
  }

  Widget _getSelButton(
    BuildContext context, {
    required String title,
    required bool isSel,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
          height: 35.gw,
          child: Row(
            children: [
              SvgPicture.asset(
                "assets/images/common/icon_check_round${isSel ? "_golden_selected" : ""}.svg",
                width: 26.gw,
                height: 26.gw,
              ),
              SizedBox(width: 6.gw),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          )),
    );
  }
}
