import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/core/utils/locale/locale_util.dart';

import 'home_drawer_state.dart';

class HomeDrawerCubit extends Cubit<HomeDrawerState> {
  HomeDrawerCubit() : super(const HomeDrawerState(localList: [])) {
    reloadData();
  }

  reloadData() {
    List<LocaleEntity> localList = LocaleUtil().supportLocaleList;
    emit(state.copyWith(localList: localList));
  }

  toggleLanguage(BuildContext context, LocaleEntity model) async {
    Locale newLocale = model.toLocale();
    if (newLocale != LocaleUtil().appLocale) {
      LocaleUtil().appLocale = newLocale;
    }
  }
}
