import 'dart:async';

import 'package:anydrawer/anydrawer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/lottery_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/lottery_odds_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_float_screen_widget.dart';
import 'package:wd/shared/widgets/lottery/lottery_count_down_widget.dart';
import 'package:wd/shared/widgets/app_bar/lottery_detail_appbar.dart';
import 'package:wd/shared/widgets/lottery/lottery_detail_input_bar.dart';
import 'package:wd/shared/widgets/lottery/lottery_game_filter_drawer.dart';
import 'package:wd/shared/widgets/lottery/lottery_item_section.dart';
import 'package:wd/shared/widgets/lottery/result/lottery_result_cell.dart';
import 'package:wd/shared/widgets/lottery/lottery_tab_cell.dart';
import 'package:wd/shared/widgets/lottery/result/lottery_result_cell_addition.dart';
import 'package:wd/shared/widgets/lottery/result/lottery_result_cell_spinning.dart';
import 'package:wd/shared/widgets/lottery/result/lottery_result_list_view.dart';

import 'lottery_detail_cubit.dart';
import 'lottery_detail_state.dart';

class LotteryDetailPage extends BasePage {
  late Lottery model;
  final List<LotteryGroup> lotteryGroupList;

  LotteryDetailPage({super.key, required this.model, required this.lotteryGroupList});

  @override
  BasePageState<BasePage> getState() => _LotteryDetailPageState();
}

class _LotteryDetailPageState extends BasePageState<LotteryDetailPage> with SingleTickerProviderStateMixin {
  final AnyDrawerController anyDrawerController = AnyDrawerController();
  final GlobalKey<LotteryDetailInputBarState> inputBarKey = GlobalKey<LotteryDetailInputBarState>();
  Timer? _timer; // 定义一个定时器
  int seconds = 0;
  double paddingH = 14;
  bool _isDrawerOpen = false;

  double timeWidgetHeight = 46;
  double resultWidgetHeight = 46;

  late FloatScreenController floatScreenController;
  late TextEditingController textController;

  // 启动倒计时
  void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }

    final cubit = BlocProvider.of<LotteryDetailCubit>(context);
    // 每秒更新一次
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      seconds++;

      /// 刷新数据
      if (seconds % 5 == 0) {
        cubit.reFetchAllData();
        sl<UserCubit>().fetchUserBalance();
      }
      cubit.executeCloseCountDown();
    });
  }

  @override
  void initState() {
    updateUI();
    floatScreenController = FloatScreenController(vsync: this);
    textController = TextEditingController(text: '10');
    startTimer();
    BlocProvider.of<LotteryDetailCubit>(context).setLotteryId(widget.model);
    super.initState();
  }

  updateUI() {
    if (widget.model.gameCategoryCode == "LHC") {
      resultWidgetHeight = 56;
    } else {
      resultWidgetHeight = 46;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    floatScreenController.dispose();
    textController.dispose();
    super.dispose();
  }

  Widget buildBetRecordItem() {
    return InkWell(
      onTap: () => sl<NavigatorService>().push(AppRouter.betRecordList),
      child: Padding(
        padding: const EdgeInsets.only(right: 15.0),
        child: Image.asset(
          "assets/images/home/<USER>/icon_lottery_record.png",
          width: 20.gw,
          height: 20.gw,
        ),
      ),
    );
  }

  @override
  Widget right() {
    return GestureDetector(
        onTap: () {
          LogD("MediaQuery.sizeOf(context)>>> ${MediaQuery.sizeOf(context).height}");
          final cubit = context.read<LotteryDetailCubit>();
          _isDrawerOpen = true;
          showDrawer(
            context,
            builder: (context) => BlocProvider.value(
              value: cubit,
              child: LotteryGameFilterDrawer(
                currentId: widget.model.id,
                lotteryGroupList: widget.lotteryGroupList,
                onClickSure: (model) {
                  anyDrawerController.close();
                  _isDrawerOpen = false;

                  // If user clicks on lottery ID 54 (animal game), navigate to animal game page
                  if (model.id == 54) {
                    sl<NavigatorService>().pushReplace(
                      AppRouter.animalGame,
                      arguments: {'gameId': model.id},
                    );
                  } else {
                    // Stay on lottery detail view for other lotteries
                    widget.model = model;
                    cubit.resetData(changeLottery: true);
                    cubit.setLotteryId(widget.model);
                    updateUI();
                  }
                },
              ),
            ),
            config: DrawerConfig(
              widthPercentage: GSScreenUtil().screenWidth > 500 ? 0.6 : 0.7,
              side: DrawerSide.right,
              closeOnClickOutside: true,
              closeOnResume: true,
              backdropOpacity: 0.5,
              borderRadius: 5,
            ),
            onClose: () {
              _isDrawerOpen = false;
            },
            controller: anyDrawerController,
          );
        },
        child: Padding(
          padding: EdgeInsets.only(right: 15.gw),
          child: Image.asset(
            "assets/images/home/<USER>/icon_lottery_game_filter.png",
            width: 17.gw,
            height: 17.gw,
          ),
        ));
  }

  _onClickConfirmNow() {
    inputBarKey.currentState?.hideKeyboardAndFastButton();
    BlocProvider.of<LotteryDetailCubit>(context).onClickConfirmNow(context);
  }

  _getTimeWidget(LotteryDetailState state) {
    final isDiceLottery = LotteryUtil.diceLotteryList.contains(widget.model.id);

    // if (state.currentEventDisplay.isEmpty) {
    //   return Container(height: timeWidgetHeight, color: Colors.white,);
    // }
    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingH),
      height: timeWidgetHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFDDE1E8), // 底部分割线颜色
            width: 1,
          ),
        ),
      ),
      child: state.isStart
          ? Row(
              children: [
                if (state.countDown > 0) ...[
                  Text(
                    "距${state.currentEventDisplay}期 投注截止",
                    style: TextStyle(
                      color: const Color(0xff694E85),
                      fontSize: 14.fs,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 9),
                  LotteryCountDownWidget(model: CountDownEntity.fromSeconds(state.countDown)),
                ] else ...[
                  Text(
                    "第${state.currentEventDisplay}期已停止投注",
                    style: TextStyle(
                      color: const Color(0xff694E85),
                      fontSize: 14.fs,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  if (isDiceLottery)
                    const Padding(
                      padding: EdgeInsets.only(right: 4),
                      child: LotteryResultSpinning(),
                    ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 2),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: const Color(0xff79388F),
                    ),
                    child: Text(
                      '开奖中',
                      style: TextStyle(color: Colors.white, fontSize: 14.fs),
                    ),
                  )
                ],
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '未开盘',
                  style: TextStyle(color: const Color(0xff79388F), fontSize: 14.fs),
                ),
              ],
            ),
    );
  }

  _getResultWidget(LotteryDetailState state) {
    final isDiceLottery = LotteryUtil.diceLotteryList.contains(widget.model.id);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        floatScreenController.toggle();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: paddingH),
        height: resultWidgetHeight,
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFDDE1E8), // 底部分割线颜色
              width: 1,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (state.lastEventDisplay.isNotEmpty)
              Text(
                "${state.lastEventDisplay}期",
                style: TextStyle(
                  color: const Color(0xff694E85),
                  fontSize: 14.fs,
                  fontWeight: FontWeight.w500,
                ),
              ),
            if (state.lastResult.isNotEmpty)
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: LotteryResultCell(
                    type: widget.model.gameCategoryCode,
                    results: state.lastResult,
                    openTime: state.lastResultOpenTime,
                    lotteryId: widget.model.id,
                  ),
                ),
              ),
            if (state.lastResult.isNotEmpty && isDiceLottery)
              Container(
                alignment: Alignment.center,
                child: LotteryResultCellAddition(
                  result: state.lastResult,
                ),
              ),
          ],
        ),
      ),
    );
  }

  _getOperationWidget(LotteryDetailState state) {
    return Row(
      children: [
        // 左侧Group的标题列表
        Container(
          width: 80.gw,
          color: Colors.white,
          child: ListView.builder(
              itemBuilder: (context, index) {
                LotteryOddsGroup model = state.dataList![index];
                final name = getSectionName(model.name, widget.model.id);

                return LotteryTabCell(
                  title: name,
                  isSelected: model.isSelected,
                  orderCount: model.orderCount,
                  onTap: () {
                    BlocProvider.of<LotteryDetailCubit>(context).onChangeCurrentLotteryOddsGroup(model);
                  },
                );
              },
              itemCount: state.dataList!.length),
        ),
        if (widget.model.gameCategoryCode == "LHC") ...[
          Builder(builder: (context) {
            if (state.dataList!.length <= state.currentTabIndex && state.currentTabIndex >= 0) {
              return Container();
            }

            LotteryOddsGroup group = state.dataList![state.currentTabIndex];

            if (group.name == "过关") {
              return _buildOperaOddItemListView(
                  dataList: state.dataList!.cast<LotteryOddsGroup>(),
                  currentTabIndex: state.currentTabIndex,
                  isLhcGuoGuan: true);
            }

            return Expanded(
              child: DefaultTabController(
                  key: ValueKey(group.name),
                  initialIndex: group.selIndex,
                  length: group.sectionMap.entries.length,
                  animationDuration: const Duration(seconds: 0),
                  child: Builder(builder: (context) {
                    final tabController = DefaultTabController.of(context);
                    tabController.addListener(() {
                      group.selIndex = tabController.index;
                      setState(() {}); //解决切换延迟，勿删
                    });
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (group.sectionMap.keys.length > 1)
                          Container(
                            padding: const EdgeInsets.only(left: 1),
                            height: 42, // 与LotteryTabCell同高，解决顶部2像素间隙
                            child: TabBar(
                              isScrollable: true,
                              padding: EdgeInsets.zero,
                              indicator: const BoxDecoration(),
                              indicatorWeight: 0,
                              automaticIndicatorColorAdjustment: false,
                              tabAlignment: TabAlignment.start,
                              labelPadding: EdgeInsets.zero,
                              tabs: group.sectionMap.keys
                                  .map((key) => Tab(
                                        child: LotteryTabCell(
                                          title: key,
                                          isSelected:
                                              tabController.index == group.sectionMap.keys.toList().indexOf(key),
                                          horizontalPadding: 15,
                                          orderCount: group.sectionMap[key]!.orderCount,
                                          showRightBorder: true,
                                        ),
                                      ))
                                  .toList(),
                            ),
                          ),
                        Expanded(
                          child: TabBarView(
                            children: group.sectionMap.values
                                .map(
                                  (section) => SingleChildScrollView(
                                    padding: EdgeInsets.only(bottom: 49.gw),
                                    child: LotteryItemSection(
                                      sectionModel: section,
                                      isLHC: true,
                                      lotteryId: widget.model.id,
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ],
                    );
                  })),
            );
          }),
        ] else ...[
          _buildOperaOddItemListView(
              dataList: state.dataList!.cast<LotteryOddsGroup>(), currentTabIndex: state.currentTabIndex)
        ]
      ],
    );
  }

  _buildOperaOddItemListView(
      {required List<LotteryOddsGroup> dataList, required int currentTabIndex, bool isLhcGuoGuan = false}) {
    return Expanded(
        child: dataList.length > currentTabIndex
            ? ListView.builder(
                padding: EdgeInsets.only(bottom: 49.gw),
                itemBuilder: (context, index) {
                  LotteryOddsGroup group = dataList[currentTabIndex];
                  Map<String, LotteryOddsSection> map = group.sectionMap;
                  String key = map.keys.elementAt(index);
                  LotteryOddsSection section = map[key]!;
                  return LotteryItemSection(
                    groupModel: group.name == "过关" ? group : null,
                    sectionModel: section,
                    isLhcGuoGuan: isLhcGuoGuan,
                    lotteryId: widget.model.id,
                  );
                },
                itemCount: dataList[currentTabIndex].sectionMap.keys.length,
              )
            : Container());
  }

  _buildBottomToolBar(LotteryDetailState state) {
    final blackTextStyle = TextStyle(
      fontSize: 12.fs,
      color: const Color(0xff555555),
      fontWeight: FontWeight.w500,
    );
    final redTextStyle = TextStyle(
      fontSize: 14.fs,
      color: const Color(0xffFF0000),
      fontWeight: FontWeight.w700,
    );
    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      color: Colors.white,
      child: SizedBox(
        height: 49.gw,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  RichText(
                    text: TextSpan(
                      style: blackTextStyle,
                      children: <TextSpan>[
                        const TextSpan(text: '已选 '),
                        TextSpan(
                          text: '${state.totalCount}',
                          style: redTextStyle,
                        ),
                        const TextSpan(text: ' 单， 共 '),
                        TextSpan(
                          text: state.totalPrice.formattedMoney,
                          style: redTextStyle,
                        ),
                        const TextSpan(text: ' 元'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 3),
                  BlocSelector<UserCubit, UserState, String?>(
                      selector: (state) => state.balanceInfo?.accountMoney.formattedMoney,
                      builder: (context, balance) {
                        if (balance != null) {
                          return RichText(
                            text: TextSpan(
                              style: blackTextStyle,
                              children: <TextSpan>[
                                const TextSpan(text: '总余额 '),
                                TextSpan(
                                  text: balance,
                                  style: redTextStyle,
                                ),
                                const TextSpan(text: ' 元'),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      }),
                ],
              ),
            ),
            const SizedBox(width: 15),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => _onClickConfirmNow(),
              child: Container(
                width: 108.gw,
                color: state.isStart ? const Color(0xff694E85) : const Color(0xffb1b5b4),
                alignment: Alignment.center,
                child: Text(
                  "立即投注",
                  style: TextStyle(color: Colors.white, fontSize: 16.fs, fontWeight: FontWeight.w500),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LotteryDetailCubit, LotteryDetailState>(
      builder: (context, state) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) return;
            // Check if drawer is open and close it first
            if (_isDrawerOpen) {
              anyDrawerController.close();
              _isDrawerOpen = false;
              return;
            }
            sl<NavigatorService>().pop();
          },
          child: Scaffold(
            appBar: LotteryDetailAppBar(
              title: widget.model.lotteryName,
              actions: [
                buildBetRecordItem(),
                right(),
              ],
            ),
            body: CommonFloatScreenWidget(
              topMargin: timeWidgetHeight + resultWidgetHeight,
              floatScreenController: floatScreenController,
              floatChild: LotteryResultListView(
                gameCategoryCode: widget.model.gameCategoryCode,
                dataList: state.resultList,
                lotteryId: widget.model.id,
              ),
              child: Stack(
                children: [
                  Column(
                    children: [
                      _getTimeWidget(state),
                      _getResultWidget(state),
                      Expanded(
                        child: _getOperationWidget(state),
                      ),
                      _buildBottomToolBar(state),
                    ],
                  ),
                  Positioned(
                      bottom: MediaQuery.of(context).padding.bottom + 49.gw,
                      child: LotteryDetailInputBar(
                        key: inputBarKey,
                        controller: textController,
                      )),
                  if (!state.isStart && state.netState == NetState.dataSuccessState)
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        width: 250,
                        height: 150,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.black.withOpacity(0.6),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          "当前未开盘",
                          style: TextStyle(
                            fontSize: 18.fs,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Container();
  }
}
