import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/home/<USER>';

import 'home_lottery_cubit.dart';
import 'home_lottery_state.dart';

class HomeLotteryPage extends StatefulWidget {
  const HomeLotteryPage({super.key});

  @override
  State<StatefulWidget> createState() => _HomeLotteryPageState();
}

class _HomeLotteryPageState extends State<HomeLotteryPage> with TickerProviderStateMixin {
  TabController? _tabController;

  void _initializeMainTabController(HomeLotteryState state) {
    if (state.dataList!.isEmpty) return;
    _tabController = TabController(vsync: this, length: state.dataList!.length, initialIndex: 1);
    _tabController?.addListener(_handleTabSelection);
  }

  void _handleTabSelection() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeLotteryCubit, HomeLotteryState>(builder: (context, state) {
      if (state.netState == NetState.loadingState) {
        return const Center(child: CircularProgressIndicator());
      }
      if (state.netState == NetState.emptyDataState) {
        return NetErrorWidget(title: "暂无数据", refreshMethod: () => sl<HomeLotteryCubit>().fetchListData());
      }
      if (state.netState == NetState.dataSuccessState && _tabController == null) {
        _initializeMainTabController(state);
      }

      return Container(
        color: Colors.white,
        child: Column(
          children: [
            const HomeSectionTitle(title: "场馆选择"),
            Container(
              padding: EdgeInsets.zero,
              margin: EdgeInsets.zero,
              child: TabBar(
                controller: _tabController,
                padding: const EdgeInsets.only(left: 15),
                tabAlignment: TabAlignment.start,
                isScrollable: true,
                labelPadding: EdgeInsets.fromLTRB(0, 5.gw, 15, 0),
                indicatorColor: Colors.transparent,
                tabs: state.dataList!.map((model) {
                  bool isSelected = _tabController!.index == state.dataList!.indexOf(model);
                  return HomeSectionTabItem(title: model.groupName,
                      logoUrl: '',
                      isSelected: isSelected);
                }).toList(),
              ),
            ),

            const HomeSectionTitle(title: "游戏选择"),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: state.dataList!.map((model) {
                  if (model.groupName == "收藏" && model.list.isEmpty) {
                    return const EmptyWidget(title: "收藏竟然是空的~\n快去添加");
                  }
                  return GridView.builder(
                    padding: const EdgeInsets.fromLTRB(15, 0, 15, 10),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: (GSScreenUtil().screenWidth / 500).floor() + 3, // 每行显示3个
                      childAspectRatio: 104 / 132, // 宽高比
                      crossAxisSpacing: 17.5, // 水平间距
                      mainAxisSpacing: 10, // 垂直间距
                    ),
                    itemCount: model.list.length,
                    itemBuilder: (context, index) {
                      Lottery lottery = model.list[index];
                      return null;
                      // return HomeGameCell(
                      //   title: lottery.lotteryName,
                      //   iconUrl: lottery.imgUrl,
                      //   // isFav: lottery.isFav,
                      //   onTap: () {
                      //     AuthUtil.checkIfLogin(() {
                      //
                      //       // LotteryApi.submitClickLottery(id: lottery.id, groupId: group.id);
                      //       sl<NavigatorService>().push(AppRouter.lotteryDetail, arguments: {
                      //         'list': state.dataList,
                      //         'model': lottery,
                      //       });
                      //     });
                      //   },
                      //   // onFavoriteTap: () =>
                      //   //     context.read<HomeLotteryCubit>().toggleFavorite(model.groupName, lottery.id),
                      // );
                    },
                  );
                }).toList(),
              ),
            ),
            // Divider(height: 1, color: Theme.of(context).dividerColor),
          ],
        ),
      );
    });
  }
}
