import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/chat/log.dart';
import 'package:wd/core/utils/tencent_im_util.dart';
import 'package:wd/injection_container.dart';

import '../../../core/utils/log_util.dart';

part 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  ChatCubit() : super(const ChatState());

  bool isConnected = false;
  int retryCount = 0;

  void initChats(int sdkAppID, userId, userSig, String token) async {
    if (state.tencentConnectStatus == DataStatus.success) return;
    emit(
      state.copyWith(tencentConnectStatus: DataStatus.loading),
    );
    try {
      while (!isConnected && sl<UserCubit>().state.isLogin) {
        try {
          LogD("是否已连接： $isConnected");
          LogD("是否已登录YL： ${sl<UserCubit>().state.isLogin}");

          await TencentImUtil().initSdkCore(
            sdkAppID,
            userId,
            userSig,
            token: token,
            onLoginSuccess: () {
              isConnected = true;
              emit(
                state.copyWith(
                  isTencentInitialized: true,
                  tencentConnectStatus: DataStatus.success,
                ),
              );
            },
            // onConnecting: () {
            //   emit(state.copyWith(tencentConnectStatus: ChatConnectStatus.loading));
            // },
            onConnectFailed: (code, error) {
              LogE('连接失败，正在重试第 ${retryCount + 1} 次: $error');
              emit(state.copyWith(tencentConnectStatus: DataStatus.failed));
              isConnected = false;
            },
          );

          if (!isConnected) {
            retryCount++;
            // 可选：添加延迟，避免立即重试
            await Future.delayed(const Duration(seconds: 2));
          }
        } catch (e) {
          LogE('连接异常: $e');
          await Future.delayed(const Duration(seconds: 2));
        }
      }
    } catch (e) {
      LogE('initChats异常: $e');
    }

    // try {
    //   onLoginSuccess() {
    //     emit(
    //       state.copyWith(
    //         isTencentInitialized: true,
    //         tencentConnectStatus: DataStatus.success,
    //         initialLoading: DataStatus.success,
    //       ),
    //     );
    //   }
    //
    //   onConnectFailed(code, error) {
    //     print("onConnectFailed>>>>>>>> $error");
    //     logDev(error, 'onConnectFailed', error: true);
    //     emit(state.copyWith(tencentConnectStatus: DataStatus.failed));
    //   }
    //
    //   TencentImUtil().initSdkCore(
    //     sdkAppID,
    //     userId,
    //     userSig,
    //     onLoginSuccess: onLoginSuccess,
    //     onConnectFailed: (code, error) {
    //       logDev(error, 'onConnectFailed', error: true);
    //       TencentImUtil().initSdkCore(
    //         sdkAppID,
    //         userId,
    //         userSig,
    //         onLoginSuccess: onLoginSuccess,
    //         onConnectFailed: onConnectFailed,
    //         token: token,
    //       );
    //     },
    //     token: token,
    //   );
    // } catch (e) {
    //   emit(state.copyWith(initialLoading: DataStatus.failed));
    //   logDev(e, 'initChats', error: true);
    // }
  }

  Future<void> logout() async {
    try {
      await TencentImUtil().logout(
        onLogoutSuccess: () {
          isConnected = false;
          emit(
            state.copyWith(
              isTencentInitialized: false,
              tencentConnectStatus: DataStatus.idle,
            ),
          );
        },
      );
    } catch (e) {
      logDev(e, 'logout', error: true);
    }
  }
}
