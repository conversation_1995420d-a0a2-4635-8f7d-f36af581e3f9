import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/core/utils/screenUtil.dart';

class RedEnvelopeDetailPage extends StatelessWidget {
  final double? amount;
  final String title;
  final String content;

  const RedEnvelopeDetailPage({
    super.key,
    required this.title,
    required this.content,
    this.amount,
  });

  @override
  Widget build(BuildContext context) {
    final paddingTop = MediaQuery.of(context).padding.top;

    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Stack(
              children: <Widget>[
                Image.asset(
                  "assets/images/chat/bg_red_packet.png",
                  width: double.infinity,
                  fit: BoxFit.fitWidth,
                ),
                Positioned(
                  top: paddingTop,
                  left: 0,
                  child: IconButton(
                    icon: Image.asset(
                      'assets/images/chat/btn_back_yellow.png',
                      width: 10,
                      height: 17,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
                Positioned(
                  top: paddingTop + 80, // 使用ScreenUtil获取高度
                  left: 0,
                  right: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Text(
                        title, //"系统红包",
                        style: TextStyle(
                          fontSize: 25, // 使用.sp简化setSp
                          color: const Color(0xffF0CC9B),
                          fontWeight: SystemUtil.isWeb() ? FontWeight.w500 : FontWeight.w600,
                        ),
                      ),
                      Text(
                        content, // "恭喜发财，大吉大利",
                        style: TextStyle(
                          fontSize: 20.fs,
                          color: const Color(0xffF0CC9B),
                        ),
                      ),
                      SizedBox(height: 20.gw),
                      if (amount != null) ...[
                        Row(
                          textBaseline: TextBaseline.alphabetic,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          children: <Widget>[
                            Text(
                              amount.toString(),
                              style: TextStyle(
                                fontSize: 20.fs,
                                color: const Color(0xffF0CC9B),
                              ),
                            ),
                            Text(
                              "元",
                              style: TextStyle(
                                fontSize: 15.fs,
                                color: const Color(0xffF0CC9B),
                              ),
                            ),
                          ],
                        ),
                        Text(
                          "已存入余额",
                          style: TextStyle(
                            fontSize: 15.fs,
                            color: const Color(0xffF0CC9B),
                          ),
                        ),
                      ] else ...[
                        Text(
                          "来晚了一步，下次趁早喔~",
                          style: TextStyle(
                            fontSize: 15.fs,
                            color: const Color(0xffF0CC9B),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 165),
              child: Center(
                child: Image.asset(
                  "assets/images/chat/icon_red_packet_detail.png",
                  width: 150,
                  height: 106,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
