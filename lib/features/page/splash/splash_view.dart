import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:video_player/video_player.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<StatefulWidget> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  late VideoPlayerController videoPlayerController;
  bool isVideoError = false;

  static const _navigationDelay = Duration(milliseconds: 500);
  static const _totalTimeout = Duration(seconds: 5);

  @override
  void initState() {
    super.initState();

    if (SystemUtil.isWeb()) {
      _handleNavigation();
    } else {
      _initializeVideo();
    }
    GlobalConfig().isSplashPageLoad = true;
  }

  @override
  void dispose() {
    if (!SystemUtil.isWeb()) {
      videoPlayerController.dispose();
    }
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    if (SystemUtil.isWeb()) return;
    videoPlayerController = VideoPlayerController.asset(Assets.introVideo)
      ..setLooping(false)
      ..addListener(_ensureVideoPlaying);

    try {
      await videoPlayerController.initialize();
      setState(() => isVideoError = false);
      videoPlayerController.addListener(() {
        if (videoPlayerController.value.position >= videoPlayerController.value.duration) {
          _handleNavigation();
        }
      });
      videoPlayerController.play();
    } catch (error) {
      log("Video initialization error: $error");
      setState(() => isVideoError = true);
      _handleNavigation();
    }
  }

  void _ensureVideoPlaying() {
    final value = videoPlayerController.value;
    if (value.isInitialized && !value.isBuffering && !value.isPlaying) {
      videoPlayerController.play();
    }
  }

  Future<void> _handleNavigation() async {
    if (!GlobalConfig().isSplashPageLoad) {
      await Future.doWhile(() async {
        await Future.delayed(const Duration(milliseconds: 100));
        return !GlobalConfig().isSplashPageLoad;
      });
    }

    if (SystemUtil.isApp() && SystemUtil.isFirstInstall) {
      sl<NavigatorService>().pushReplace(AppRouter.guide);
    } else {
      pushToMainPage();
    }
  }

  Future<void> pushToMainPage() async {
    sl<NavigatorService>().pushReplace(AppRouter.nav);
    if (GlobalConfig().needRedirectToTiktokPage) {
      sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.videoHome);
    } else if (GlobalConfig().needToRegister) {
      await Future.delayed(_navigationDelay);
      sl<NavigatorService>().push(AppRouter.register);
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child:
            // 临时加上GlobalConfig.needShowVideoPage()，因为没提供启动页素材
            (!SystemUtil.isWeb() && !isVideoError)
                ? SizedBox.expand(
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: SizedBox(
                        width: videoPlayerController.value.size.width,
                        height: videoPlayerController.value.size.height,
                        child: VideoPlayer(videoPlayerController),
                      ),
                    ),
                  )
                : Container(
                    color: Colors.white,
                    child: Image.asset(
                      Assets.introFallback,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
      ),
    );
  }
}
