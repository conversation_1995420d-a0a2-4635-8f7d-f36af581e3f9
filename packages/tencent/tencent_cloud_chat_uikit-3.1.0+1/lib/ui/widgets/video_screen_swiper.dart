import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/permission.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:universal_html/html.dart' as html;
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class VideoScreen2 extends StatefulWidget {
  const VideoScreen2({required this.message, required this.heroTag, required this.videoElement, Key? key})
      : super(key: key);

  final V2TimMessage message;
  final dynamic heroTag;
  final V2TimVideoElem videoElement;

  @override
  State<StatefulWidget> createState() => _VideoScreenState();
}

class _VideoScreenState extends TIMUIKitState<VideoScreen2> {
  late final player = Player();
  late final controller = VideoController(player);
  final TUIChatGlobalModel model = serviceLocator<TUIChatGlobalModel>();
  bool isInit = false;
  WebViewController? _webViewController;
  String? _errorMessage;

  @override
  initState() {
    super.initState();
    if (PlatformUtils().isWeb) {
      _setupWebViewVideoPlayer();
    } else {
      setVideoPlayerController();
    }
    // 允许横屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  // 保存网络视频到本地
  Future<void> _saveNetworkVideo(
    context,
    String videoUrl, {
    bool isAsset = true,
  }) async {
    if (PlatformUtils().isWeb) {
      RegExp exp = RegExp(r"((\.){1}[^?]{2,4})");
      String? suffix = exp.allMatches(videoUrl).last.group(0);
      var xhr = html.HttpRequest();
      xhr.open('get', videoUrl);
      xhr.responseType = 'arraybuffer';
      xhr.onLoad.listen((event) {
        final a = html.AnchorElement(href: html.Url.createObjectUrl(html.Blob([xhr.response])));
        a.download = '${md5.convert(utf8.encode(videoUrl)).toString()}$suffix';
        a.click();
        a.remove();
      });
      xhr.send();
      return;
    }
    if (PlatformUtils().isMobile) {
      if (PlatformUtils().isIOS) {
        if (!await Permissions.checkPermission(
          context,
          Permission.photosAddOnly.value,
        )) {
          return;
        }
      } else {
        final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        if ((androidInfo.version.sdkInt) >= 33) {
          final videos = await Permissions.checkPermission(
            context,
            Permission.videos.value,
          );

          if (!videos) {
            return;
          }
        } else {
          final storage = await Permissions.checkPermission(
            context,
            Permission.storage.value,
          );
          if (!storage) {
            return;
          }
        }
      }
    }
    String savePath = videoUrl;
    if (!isAsset) {
      if (widget.message.msgID == null || widget.message.msgID!.isEmpty) {
        return;
      }
      if (model.getMessageProgress(widget.message.msgID) == 100) {
        String savePath;
        if (widget.message.videoElem!.localVideoUrl != null && widget.message.videoElem!.localVideoUrl != '') {
          savePath = widget.message.videoElem!.localVideoUrl!;
        } else {
          savePath = model.getFileMessageLocation(widget.message.msgID);
        }
        File f = File(savePath);
        if (f.existsSync()) {
          var result = await ImageGallerySaverPlus.saveFile(savePath);
          if (PlatformUtils().isIOS) {
            if (result['isSuccess']) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
            }
          } else {
            if (result != null) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
            }
          }
        }
      } else {
        onTIMCallback(TIMCallback(
            type: TIMCallbackType.INFO, infoRecommendText: TIM_t("the message is downloading"), infoCode: -1));
      }
      return;
    }
    var result = await ImageGallerySaverPlus.saveFile(savePath);
    if (PlatformUtils().isIOS) {
      if (result['isSuccess']) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
      }
    } else {
      if (result != null) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
      }
    }
    return;
  }

  double getVideoHeight() {
    double height = widget.videoElement.snapshotHeight?.toDouble() ?? 100.0;
    double width = widget.videoElement.snapshotWidth?.toDouble() ?? 100.0;
    // 横图
    if (width > height) {
      return height * 1.3;
    }
    return height;
  }

  double getVideoWidth() {
    double height = widget.videoElement.snapshotHeight?.toDouble() ?? 100.0;
    double width = widget.videoElement.snapshotWidth?.toDouble() ?? 100.0;
    // 横图
    if (width > height) {
      return width * 1.3;
    }
    return width;
  }

  void _setupWebViewVideoPlayer() {
    String? videoUrl = (TencentUtils.checkString(widget.videoElement.videoPath) != null) ||
            widget.message.status == MessageStatus.V2TIM_MSG_STATUS_SENDING
        ? widget.videoElement.videoPath
        : (TencentUtils.checkString(widget.videoElement.localVideoUrl) == null)
            ? widget.videoElement.videoUrl
            : widget.videoElement.localVideoUrl;

    if (videoUrl == null || videoUrl.isEmpty) {
      setState(() {
        isInit = false;
        _errorMessage = 'Invalid video URL';
      });
      print(
          'Error: Invalid video URL - videoPath: ${widget.videoElement.videoPath}, videoUrl: ${widget.videoElement.videoUrl}, localVideoUrl: ${widget.videoElement.localVideoUrl}');
      return;
    }

    // Create HTML content for the video player with additional Safari compatibility
    final htmlContent = '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          html { height: calc(100% - 50px); background: black;}
          body { margin: 0; background: black;height: 100%;display: flex;justify-content: center;align-items: center; }
          video { width: 100%; height: 100%; object-fit: contain; }
        </style>
      </head>
      <body>
        <video controls playsinline>
          <source src="$videoUrl" type="video/mp4">
          Your browser does not support the video tag.
        </video>
        <script>
          window.addEventListener('error', function(event) {
            console.error('Video error:', event);
          });
        </script>
      </body>
      </html>
    ''';

    final PlatformWebViewControllerCreationParams params = WebViewPlatform.instance is WebKitWebViewPlatform
        ? WebKitWebViewControllerCreationParams(
            allowsInlineMediaPlayback: true, // Enable inline video playback
          )
        : const PlatformWebViewControllerCreationParams();

    _webViewController = WebViewController.fromPlatformCreationParams(params)
      // ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..setBackgroundColor(Colors.black)
      // ..setNavigationDelegate(
      //   NavigationDelegate(
      //     onWebResourceError: (error) {
      //       setState(() {
      //         isInit = false;
      //         _errorMessage = 'Failed to load video: ${error.description}';
      //       });
      //       print('WebView error: ${error.description}');
      //     },
      //   ),
      // )
      ..loadHtmlString(htmlContent);

    setState(() {
      isInit = true;
      _errorMessage = null;
    });
  }

  setVideoPlayerController() async {
    if (!mounted) {
      return;
    }

    if (isInit) {
      await player.dispose();
      setState(() {
        isInit = false;
      });
    }

    if (!PlatformUtils().isWeb) {
      if (TencentUtils.checkString(widget.message.msgID) != null && widget.videoElement.localVideoUrl == null) {
        String savePath = model.getFileMessageLocation(widget.message.msgID);
        File f = File(savePath);
        if (f.existsSync()) {
          widget.videoElement.localVideoUrl = savePath;
        }
      }
    }

    String? videoUrl = PlatformUtils().isWeb
        ? ((TencentUtils.checkString(widget.videoElement.videoPath) != null) ||
                widget.message.status == MessageStatus.V2TIM_MSG_STATUS_SENDING
            ? widget.videoElement.videoPath
            : (TencentUtils.checkString(widget.videoElement.localVideoUrl) == null)
                ? widget.videoElement.videoUrl
                : widget.videoElement.localVideoUrl)
        : widget.videoElement.videoPath;

    if (videoUrl == null || videoUrl.isEmpty) {
      setState(() {
        isInit = false;
        _errorMessage = 'Invalid video URL';
      });
      print(
          'Error: Invalid video URL - videoPath: ${widget.videoElement.videoPath}, videoUrl: ${widget.videoElement.videoUrl}, localVideoUrl: ${widget.videoElement.localVideoUrl}');
      return;
    }

    try {
      await player.open(Media(videoUrl));

      if (!mounted) {
        await player.dispose();
        return;
      }

      setState(() {
        isInit = true;
        _errorMessage = null;
      });
    } catch (e) {
      await player.dispose();
      setState(() {
        isInit = false;
        _errorMessage = 'Failed to load video: $e';
      });
      print('MediaKit error: $e');
    }
  }

  @override
  didUpdateWidget(oldWidget) {
    if (oldWidget.videoElement.videoUrl != widget.videoElement.videoUrl ||
        oldWidget.videoElement.videoPath != widget.videoElement.videoPath) {
      if (PlatformUtils().isWeb) {
        _setupWebViewVideoPlayer();
      } else {
        setVideoPlayerController();
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    if (isInit && !PlatformUtils().isWeb) {
      player.dispose();
    }
    super.dispose();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    return ExtendedImageSlidePageHandler(
      child: isInit
          ? _buildVideoWidget()
          : Center(
              child: Text(
                _errorMessage ?? 'Failed to initialize video player',
                style: const TextStyle(color: Colors.white),
              ),
            ),
    );
  }

  Widget _buildVideoWidget() {
    return PlatformUtils().isWeb
        ? SizedBox(
            width: getVideoWidth(),
            height: getVideoHeight(),
            child: _webViewController != null
                ? WebViewWidget(controller: _webViewController!)
                : Center(
                    child: Text(
                      _errorMessage ?? 'Failed to load video',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
          )
        : Container(
            color: Colors.black,
            child: Column(
              children: [
                Expanded(
                    child: MaterialVideoControlsTheme(
                        normal: const MaterialVideoControlsThemeData(
                          seekBarThumbColor: Colors.white,
                          seekBarPositionColor: Colors.white,
                        ),
                        fullscreen: const MaterialVideoControlsThemeData(),
                        child: Video(controller: controller, controls: AdaptiveVideoControls))),
                SizedBox(height: 60 + MediaQuery.of(context).viewInsets.bottom),
              ],
            ),
          );
  }
}
