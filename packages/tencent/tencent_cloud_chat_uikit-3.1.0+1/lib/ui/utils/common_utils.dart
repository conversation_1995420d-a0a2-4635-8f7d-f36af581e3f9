import 'dart:convert';

import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/models/custom_element.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';

import '../views/TIMUIKitChat/TIMUIKitTextField/special_text/http_text.dart';
import '../widgets/link_preview/common/utils.dart';

class TencentUtils {
  static bool isTextNotEmpty(String? text) {
    return text != null && text.isNotEmpty;
  }

  static String? checkString(String? text) {
    return (text != null && text.isEmpty) ? null : text;
  }

  static String? checkStringWithoutSpace(String? text) {
    if (text == null || text.trim().isEmpty || text.contains(' ')) {
      return null;
    }
    return text;
  }

  static String getFileType(String fileType) {
    switch (fileType) {
      case "3gp":
        return "video/3gpp";
      case "torrent":
        return "application/x-bittorrent";
      case "kml":
        return "application/vnd.google-earth.kml+xml";
      case "gpx":
        return "application/gpx+xml";
      case "asf":
        return "video/x-ms-asf";
      case "avi":
        return "video/x-msvideo";
      case "bin":
      case "class":
      case "exe":
        return "application/octet-stream";
      case "bmp":
        return "image/bmp";
      case "c":
        return "text/plain";
      case "conf":
        return "text/plain";
      case "cpp":
        return "text/plain";
      case "doc":
        return "application/msword";
      case "docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case "xls":
      case "csv":
        return "application/vnd.ms-excel";
      case "xlsx":
        return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      case "gif":
        return "image/gif";
      case "gtar":
        return "application/x-gtar";
      case "gz":
        return "application/x-gzip";
      case "h":
        return "text/plain";
      case "htm":
        return "text/html";
      case "html":
        return "text/html";
      case "jar":
        return "application/java-archive";
      case "java":
        return "text/plain";
      case "jpeg":
        return "image/jpeg";
      case "jpg":
        return "image/jpeg";
      case "js":
        return "application/x-javascript";
      case "log":
        return "text/plain";
      case "m3u":
        return "audio/x-mpegurl";
      case "m4a":
        return "audio/mp4a-latm";
      case "m4b":
        return "audio/mp4a-latm";
      case "m4p":
        return "audio/mp4a-latm";
      case "m4u":
        return "video/vnd.mpegurl";
      case "m4v":
        return "video/x-m4v";
      case "mov":
        return "video/quicktime";
      case "mp2":
        return "audio/x-mpeg";
      case "mp3":
        return "audio/x-mpeg";
      case "mp4":
        return "video/mp4";
      case "mpc":
        return "application/vnd.mpohun.certificate";
      case "mpe":
        return "video/mpeg";
      case "mpeg":
        return "video/mpeg";
      case "mpg":
        return "video/mpeg";
      case "mpg4":
        return "video/mp4";
      case "mpga":
        return "audio/mpeg";
      case "msg":
        return "application/vnd.ms-outlook";
      case "ogg":
        return "audio/ogg";
      case "pdf":
        return "application/pdf";
      case "png":
        return "image/png";
      case "pps":
        return "application/vnd.ms-powerpoint";
      case "ppt":
        return "application/vnd.ms-powerpoint";
      case "pptx":
        return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
      case "prop":
        return "text/plain";
      case "rc":
        return "text/plain";
      case "rmvb":
        return "audio/x-pn-realaudio";
      case "rtf":
        return "application/rtf";
      case "sh":
        return "text/plain";
      case "tar":
        return "application/x-tar";
      case "tgz":
        return "application/x-compressed";
      case "txt":
        return "text/plain";
      case "wav":
        return "audio/x-wav";
      case "wma":
        return "audio/x-ms-wma";
      case "wmv":
        return "audio/x-ms-wmv";
      case "wps":
        return "application/vnd.ms-works";
      case "xml":
        return "text/plain";
      case "z":
        return "application/x-compress";
      case "zip":
        return "application/x-zip-compressed";
      default:
        return "*/*";
    }
  }

  static V2TimMessage fromCustomMediaMessage(
    V2TimMessage message,
    MediaItem mediaItem,
  ) {
    final isVideo = mediaItem.type == MediaType.video;
    return V2TimMessage(
      msgID: message.msgID,
      timestamp: message.timestamp,
      progress: message.progress,
      sender: message.sender,
      nickName: message.nickName,
      friendRemark: message.friendRemark,
      faceUrl: message.faceUrl,
      nameCard: message.nameCard,
      groupID: message.groupID,
      userID: message.userID,
      status: message.status,
      elemType: isVideo ? MessageElemType.V2TIM_ELEM_TYPE_VIDEO : MessageElemType.V2TIM_ELEM_TYPE_IMAGE,
      videoElem: isVideo
          ? V2TimVideoElem(
              videoPath: mediaItem.url,
              localVideoUrl: mediaItem.localPath,
              duration: mediaItem.duration,
              snapshotPath: mediaItem.thumbnailPath,
              snapshotUrl: mediaItem.thumbnailUrl,
            )
          : null,
      imageElem: !isVideo
          ? V2TimImageElem(
              imageList: [
                V2TimImage(
                  type: V2TimImageTypesEnum.original.index,
                  url: mediaItem.url,
                  localUrl: mediaItem.localPath,
                )
              ],
            )
          : null,
      cloudCustomData: message.cloudCustomData,
      isSelf: message.isSelf,
      isRead: message.isRead,
      isPeerRead: message.isPeerRead,
      priority: message.priority,
      offlinePushInfo: message.offlinePushInfo,
      groupAtUserList: message.groupAtUserList,
      seq: message.seq,
      random: message.random,
      isExcludedFromUnreadCount: message.isExcludedFromUnreadCount,
      isExcludedFromLastMessage: message.isExcludedFromLastMessage,
      isSupportMessageExtension: message.isSupportMessageExtension,
      messageFromWeb: message.messageFromWeb,
      id: message.id,
      needReadReceipt: message.needReadReceipt,
      customElem: message.customElem,
      textElem: message.textElem,
      soundElem: message.soundElem,
      fileElem: message.fileElem,
      locationElem: message.locationElem,
      faceElem: message.faceElem,
      groupTipsElem: message.groupTipsElem,
      mergerElem: message.mergerElem,
      localCustomData: message.localCustomData,
      localCustomInt: message.localCustomInt,
    );
  }

  static String getContentSpan(String text, BuildContext context,
      {Function(String)? onLinkTap, TextStyle? style, bool decodeLink = true}) {
    List<InlineSpan> _contentList = [];
    String processedContent = "";

    // Find all emoji patterns [...]
    RegExp emojiRegex = RegExp(r'\[.*?\]');
    List<RegExpMatch> emojiMatches = emojiRegex.allMatches(text).toList();

    // /// 破罐破摔 临时写 待重构， 文字默认有加密
    // if (text.contains("\"param\"")) {
    //   try {
    //     final dict = jsonDecode(text);
    //     if (dict is Map<String, dynamic> && dict.containsKey('param') && dict.containsKey('e')) {
    //       var res = HybridEncryptedData.fromJson(dict);
    //       final tmp = HybridCryptoUtil.decryptWithAESAndRSA(encryptedData: res);
    //       text = tmp;
    //     }
    //   } catch (_) {}
    // }

    if (emojiMatches.isEmpty) {
      // No emojis, just decode the entire text
      try {
        processedContent = text.fromBase64;
      } catch (e) {
        processedContent = text;
      }
    } else {
      int lastIndex = 0;

      // Process text segments and emojis in order
      for (RegExpMatch match in emojiMatches) {
        // If there's text before the emoji, decode it
        if (match.start > lastIndex) {
          String textPart = text.substring(lastIndex, match.start);
          try {
            processedContent += textPart.fromBase64;
          } catch (e) {
            processedContent += textPart;
          }
        }

        // Add the emoji as is
        processedContent += text.substring(match.start, match.end);
        lastIndex = match.end;
      }

      // Handle any remaining text after the last emoji
      if (lastIndex < text.length) {
        String textPart = text.substring(lastIndex);
        try {
          processedContent += textPart.fromBase64;
        } catch (e) {
          processedContent += textPart;
        }
      }
    }
    if (decodeLink) {
      // Process URLs in the decoded content
      Iterable<RegExpMatch> matches = LinkUtils.urlReg.allMatches(processedContent);
      String finalContent = "";
      int index = 0;

      for (RegExpMatch match in matches) {
        String c = processedContent.substring(match.start, match.end);
        if (match.start == index) {
          index = match.end;
        }
        if (index < match.start) {
          String a = processedContent.substring(index, match.start);
          index = match.end;
          finalContent += a;
          _contentList.add(
            TextSpan(text: a),
          );
        }

        if (LinkUtils.urlReg.hasMatch(c)) {
          finalContent += HttpText.flag + c + HttpText.flag;
          _contentList.add(TextSpan(
              text: c,
              style: TextStyle(color: LinkUtils.hexToColor("015fff")),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  if (onLinkTap != null) {
                    onLinkTap(processedContent.substring(match.start, match.end));
                  } else {
                    LinkUtils.launchURL(context, processedContent.substring(match.start, match.end));
                  }
                }));
        } else {
          finalContent += c;
          _contentList.add(
            TextSpan(text: c, style: style ?? const TextStyle(fontSize: 16.0)),
          );
        }
      }

      if (index < processedContent.length) {
        String a = processedContent.substring(index, processedContent.length);
        finalContent += a;
        _contentList.add(
          TextSpan(text: a, style: style ?? const TextStyle(fontSize: 16.0)),
        );
      }
      return finalContent;
    }
    return processedContent;
  }
}

extension ConversationExtension on String {
  bool get hasSquareBrackets => startsWith('[') && endsWith(']');

  String get processEmojiString {
    final RegExp emojiPattern = RegExp(r'\[([A-Za-z0-9_]+)\]');
    return replaceAllMapped(emojiPattern, (match) {
      final emojiName = match.group(1);
      return '[TUIEmoji_$emojiName]';
    });
  }

  String get toBase64 => hasSquareBrackets ? processEmojiString : base64.encode(utf8.encode(this));

  String get fromBase64 {
    try {
      if (isBase64) {
        return utf8.decode(base64.decode(this));
      }
      return this;
    } catch (e) {
      return this;
    }
  }

  bool get isBase64 {
    try {
      // Check if the input contains only valid Base64 characters
      final base64RegExp = RegExp(r'^[A-Za-z0-9+/]*={0,2}$');
      if (!base64RegExp.hasMatch(this)) {
        return false;
      }
      // Try decoding the input
      base64.decode(this);
      return true; // If decoding succeeds, it is valid Base64
    } catch (e) {
      return false; // If decoding fails, it's not valid Base64
    }
  }

  bool get isBase64Image {
    // Check if the string starts with a valid base64 image prefix
    final RegExp base64Pattern = RegExp(r'^data:image\/(png|jpeg|jpg|gif|webp);base64,');

    if (!base64Pattern.hasMatch(this)) return false;

    try {
      // Extract the base64 part and decode it
      final String base64Data = split(',').last;
      base64Decode(base64Data);
      return true;
    } catch (e) {
      return false;
    }
  }

  String get cleanBase64 {
    final RegExp base64Pattern = RegExp(r'data:image\/[a-zA-Z]+;base64,');
    return replaceFirst(base64Pattern, '');
  }
}
